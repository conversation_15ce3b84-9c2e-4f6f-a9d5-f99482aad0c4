{"buildFiles": ["/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Flutter-Projects/Ajory/Dubai-Apps/aqar-dxb-admin/android/app/.cxx/Debug/3h5e4o6v/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Flutter-Projects/Ajory/Dubai-Apps/aqar-dxb-admin/android/app/.cxx/Debug/3h5e4o6v/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}