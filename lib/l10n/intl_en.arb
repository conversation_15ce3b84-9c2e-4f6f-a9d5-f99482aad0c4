{"Login": "<PERSON><PERSON>", "EmailAdress": "<PERSON><PERSON>", "Password": "Password", "ForgotPassword": "Forgot Password", "enteremail": "Please enter your registered email in order to send you a 4-digit OTP to reset your password", "Email": "Email", "SenddigitsOTP": "Send 4-digits OTP", "EnterdigitsOTP": "Enter 4-digits O<PERSON>", "checkemail": "4-digit OTP Was sent to your email,Please check and enter it to continue", "Continue": "Continue", "EnteraNewPassword": "Enter a New Password,that is easy to remember", "EnterNewPassword": "Enter New Password", "ConfirmNewPassword": "Confirm New Password", "Resetpassword": "Reset password", "type": "Type", "passwordchangedsuccessfullly": "password changed successfullly", "pleaseusenewpasswordtologin": "please use new password to login", "WelcometoDashboard": "Welcome to Dashboard", "Manageallfeaturesfromoneplace": "Manage all features from one place", "Categories": "Categories", "FeturedVideos": "Fetured Videos", "website": "Website", "Othersettigs": "Other settigs", "ManageFeatures": "Manage Features,types,brands and other stuff", "Language": "Language", "pricepernight": "Price Per Night", "TurnedOn": "Turned On", "Type": "Type", "Logout": "Logout", "English": "English", "Arabic": "Arabic", "Agents": "Agents", "starting": "Starting", "AllAgents": "All Agents", "AddNewagent": "Add New agent", "HolidayHomesAgents": "Holiday Homes Agents", "CarrentalAgets": "Car rental Agets", "ApplyFilter": "Apply Filter", "reset": "reset", "Agentcompanyname": "Agent company name", "Contactfinishdate": "Contact finish date", "UserInformation": "User Information", "Country": "Country", "Fullname": "Full name English", "EmailAddress": "Email Address", "AgentDetails": "Agent Details", "whatsapp": "Whatsapp", "MainAgentInformation": "Main Agent Information", "AgentCompanyName": "Agent Company Name", "Agenty": "Agenty", "AddAgent": "Add Agent", "Edit": "Edit", "DeleteAgent": "Delete Agent", "Areyyousureyouwanttodeletethisagent": "Are yyou sure you want to delete this agent", "yDeleteAgent": "Yes,Delete Agent", "Notifications": "Notifications", "Requestforcarrentapproved": "Request for car rent approved", "YourrequestforHyundaiSonataNo": "Your request for Hyundai Sonata No. 380282 is approved", "FeaturedVideos": "Featured Videos", "AllFeaturedVideos": "All Featured Videos", "Filteredas": "<PERSON><PERSON><PERSON><PERSON>", "areaar": "Area Name Arabic", "areaen": "Area Name English", "AddApplicationfeature": "Add Application feature", "Removefromcategoryfeature": "Remove from category feature", "Requests": "Requests", "AllRequests": "All Requests", "NumberofPeople": "Number of People", "Status": "Status", "Requestedon": "Requestedon", "Agent": "Agent", "Numberofpeople": "Number of people", "TotalPrice": "TotalPrice", "editPrice": "Edit Price", "Agentname": "Agent name", "TeslaModel3": "Tesla Model3", "Callclient": "Call client", "CallAgent": "Call Agent", "RequestDetails": "Request Details", "Requesteddate": "Requeste date", "PrivateDriver": "Private Driver", "PickupDropOff": "Pickup/DropOff", "Note": "Note", "NumberOfDays": "Number Of Days", "AgentNote": "Agent Note", "AgreementTotalPrice": "Agreement Total Price", "HolidayHome1": "Holiday Home 1", "AlMalekFahedStreet": "Al Malek Fahed Street", "PriceBreakdown": "Price Breakdown", "Price": "Price", "priprice": "Private drive price", "PleaseIwantaverycleanandbigcar": "Please I want a very clean and big car.", "Subtotal": "Subtotal", "PromoSubtotal": "Promo Subtotal", "Vat": "Vat", "Accepted": "Accepted", "Pending": "Pending", "termar": "Arabic Terms And Condtion", "termen": "English Terms And Condtion", "YouTube": "YouTube", "Instagram": "Instagram", "Denied": "Denied", "Canceled": "Canceled", "Restaurants": "Restaurants", "Numberofdays": "Number of days", "Tourismfee": "Tourism fee (5Nights)", "Agentnote": "Agent note", "Fee": "Fee", "Othersettings": "Other settings", "Types": "Types", "manage": "Manage features, types, brands and other stuff", "Carbrads": "Car brads", "Configuration": "Configuration", "AllPromoCodes": "All Promo Codes", "usersusedPromoCode": "20 users used Promo Code", "AddnewPromoCode": "Add new Promo Code", "AllPromocodes": "All Promo codes", "Discount": "Discount", "Code": "Code", "endat": "End at", "promo": "Promo Codes", "DiscountPercentage": "Discount Percentage", "Exit": "Exit", "SaveChanges": "Save Changes", "AddNewPromoCode": "Add New Promo Code", "DeletePromoCode": "Delete Promo Code", "updatepro": "Profile was updated successfuly", "Areyousureyouwanttodeletethispromocode": "Are you sure you want to delete this promo code", "AdminAccountInformation": "Admin Account Information", "PhoneNumber": "Phone Number", "wrong": "Something went wrong, please try again later", "deletehome": "Are you sure you want to delete this Holiday Home, if yes you won’t be able to see it again.", "face": "Facebook page", "feaen": "Feature Name English", "feaar": "Feature Name Arabic", "typeen": "Type Name English", "typear": "Type Name Arabic", "enteraran": "Please enter arabic name", "deletepropertystatus": "Delete Property Status", "deletepropertystatusDesc": "Are you sure you want to delete this property status, if yes you won’t be able to see it again.", "areyousureyouwanttodelete": "Are you sure you want to delete this", "enterenn": "Please enter English name", "AddPropertyStatus": "Add Property Status", "enterc": "Please enter category", "deleteag": "Are you sure you want to delete this agent", "ContractFinishDate": "Contract Finish Date", "insta": "Instagram Page", "selectimg": "Select images please", "selectvid": "Select main video please", "added": "Added success", "fill": "Fill required fields above please", "ChangePassword": "Change Password", "Save": "Save", "editedSuccessfully": "Edited Successfully", "AllFeatures": "All Features", "DeleteArea": "Delete Area", "Search": "Search", "entere": "Please enter the email field", "enterp": "Please enter the password field", "enterpc": "Please enter the  confirm new password field", "BurjKhalifaview": "<PERSON><PERSON><PERSON> view", "Bigrooms": "Big rooms", "Balcony": "Balcony", "AddNewFeature": "Add New Feature", "ChooseCategory": "Choose Category", "Seaview": "Sea view", "featureName": "feature Name", "Deletefeature": "Delete feature", "Areyousureyouwanttodeletethisfeature": "Are you sure you want to delete this feature", "yesDeletefeature": "yes,Delete feature", "HolidayHome": "Holiday Home", "Activities": "Activities", "Alltypes": "All types", "Addnewtype": "Add new type", "Category": "Category", "Property": " Property", "Filter": "Filter", "Reset": "Reset", "AllCategories": "All Categories", "villa": "villa", "Chalet": "<PERSON><PERSON>", "Farm": "Farm", "Properety": "Properety", "Deletetype": "Delete type", "Areyousureyouwanttodeletethistype": "Are you sure you want to delete this type", "yesDeletetype": "yes,Delete type", "CarBrands": "Car Brands", "AllCarBrands": "All Car Brands", "Toyota": "Toyota", "Tesla": "Tesla", "Brand": "Brand", "BMW": "BMW", "Chevrolet": "Chevrolet", "Kia": "<PERSON><PERSON>", "RangeRover": "Range Rover", "AddNewBrand": "Add New Brand", "BrandName": "Brand Name", "DeleteBrand": "Delete Brand", "Areyousureyouwanttodeletethisbrand": "Are you sure you want to delete this brand", "yesDeletebrand": "yes,Delete brand", "LargeSelectionofLuxuryHotelsforyouandyourlovedoneBookonlinetoday": "Large Selection of Luxury Hotels for you and your loved one،Book online today ..", "Editimages": "Edit images", "EditReelvideos": "Edit Reel videos", "ViewDiscussion": "View Discussion", "DeleteReelVideo": "Delete Reel Video", "Cancel": "Cancel", "Areyousureyouwanttodeletethisreelvideo": "Are you sure you want to delete this reel video", "yesDeletereelvideo": "yes,Del<PERSON>eel video", "Uploadimages": "Upload images", "deletev": "Delete Video", "Uploadreelvideos": "Upload reel videos", "Tabheretouploadimage": "Tab here to upload image's", "UploadPhotos": "Upload Photos", "Tabheretouploadvideo": "Tab here to upload video/s", "Uploadvideos": "Upload videos", "ReUploadreelvideo": "Re-Upload reel video", "Tabheretoreelvideo": "Tab here to reel video", "Discussion": "Discussion", "tapp": "Tab here to upload photo", "Thisplaceiseverythingyouneedforthebeststay": "This place is everything you need for the best stay, Perfect location, view and atmosphere.", "arahome": "Arabic Holiday Home Name", "enhome": "English Holiday Home Name", "HotelName": "Hotel Name", "PriceRangePerNight": "Price Range Per Night", "Jointhediscussion": "Join thediscussion", "holidaysi": "Property size", "numroom": "Number of bedrooms", "Editcomment": "Edi tcomment", "TranslateComment": "Translate Comment", "DeleteComment": "Delete Comment", "HideComment": "Hide Comment", "HolidayHomes": "Holiday Homes", "Allholidayhomes": "All holiday homes", "AddnewHolidayHome": "Add new Holiday Home", "Basicinformation": "Basic information", "AED": "AED", "arabicnum": "Arabic Car Rental Name", "engnum": "English Car Rental Name", "From": "From", "filteras": "Filtered As", "activityara": "Arabic Activity Name", "To": "To", "desara": "Arabic Description", "deaen": "English Description", "activityeng": "English Activity Name", "Features": "Features", "nofea": "There are no Features", "Setallfeatures": "Set all features", "Numberofbedrooms": "Number of bedrooms ", "HomeSize": "Home Size", "LocationInformation": "Location Information ", "Location": " Location ", "SetLocationonmap": "Set Location on map?", "UploadMainVideo": "Upload Main Video", "UploadMainVideoAr": "Upload Main Arabic Video", "Tabheretouploadmainvideo": "Tab here to upload main video", "TabheretouploadmainvideoAr": "Tab here to upload main arabic video ", "Assignasfeaturedvideoincatpage": "Assign as featured video in category page", "Assignasfeaturedvideoinhomepage": "Assign as featured video in home page", "UploadReel": "Upload Reel/s", "Tabheretouploadreel": "Tab here to upload reel/s", "UploadImage": "Upload Image/s", "ExtraInformation": "Extra Information", "GoogleReviewLink": "Google Review Link", "CarRental": "Car Rental", "Reviews": "Reviews", "AllRentalCars": "AllRentalCars", "SearchCars": "SearchCars", "AddNewRentalCars": "+ Add New Rental Cars", "BasicInformation": "Basic Information", "CarName": "Car Name", "Writedownyourdescription": "Write down your description", "PriceRangePerDay": "Price Range Per Day", "DriverPricePerDay": "Driver Price Per Day", "CarProductionYear": "Car Production Year", "DeleteHolidayhome": "Delete Holiday home", "AreyousureyouwanttodeletethisHolidayhome": "Are you sure you want to delete this Holiday home", "yesDeleteHolidayhome": "yes,Delete Holiday home", "Destination": "Destination", "Destinations": "Destinations", "searchRmsDiscount": "Search RMS Discount", "selectRmsDiscount": "Select RMS Discount", "AllDestinations": "All Destinations", "Carrental": "Car rental", "AllRentalcars": "All Rental cars", "Searchcars": "Search cars", "Addnewrentalcars": "Add new rental cars", "Addnewrentalcar": "Add new rental car", "Carname": "Car name", "Description": "Description", "Pricerangeperday": "Price range per day", "DriverPriceperday": "Driver Price per day", "DeleteRentalcar": "Delete Rental car", "delemsgv": "Are you sure you want to delete this Video,if yes you wont be able to see again", "Areyousureyouwanttodeletethisrentalcar": "Are you sure you want to delete this rental car,if yes you wont be able to see again", "yesDeleterentalcar": "yes,Delete rental car", "Properties": "Properties", "AllProperties": "All Properties", "addres": "Add New Restaurant", "AddnewProperty": "Add new Property", "Searchplacesandlocations": "Search destinations and locations", "AddNewDestination": "Add New Destination", "Propertyname": "Property name", "Pricestartsfrom": "Price starts from", "DeleteProperty": "Delete Property", "SearchAreas": "Search Areas", "AddNewArea": "Add New Area", "passcur": "Current Password", "deletepro": "Are you sure you want to delete this property,if yes you wont be able to see again", "yeDeleteproperty": "yes,Delete property", "Hotels": "Hotels", "AllHotels": "All Hotels", "Addnewhotel": "Add new hotel", "hotelar": "Arabic Hotel Name", "hotenen": "English Hotel Name", "Hotelname": "Hotel name", "Pricerangepernight": "Price range per night", "Pricepernight": "Price per night", "Deletehotel": "Delete hotel", "Areyousureyouwanttodeletethishotelifyesyouwontbeabletoseeagain": "Are you sure you want to delete this hotel,if yes you wont be able to see again", "YesDeletehotel": "Yes,Delete hotel", "Resturants": "Resturants", "Allresturants": "All resturants", "Addnewresturant": "Add new resturant", "Resturantname": "Resturant name", "Averagepriceper": "Average price(per person)", "DeleteResturant": "Delete Resturant", "Areyousureyouwanttodeletethisresturant": "Are you sure you want to delete this resturant,if yes you wont be able to see again", "yesDeleteresturant": "yes,Delete resturant", "Actvities": "Actvities", "Allactivities": "All activities", "AddNewActivity": "Add New Activity", "ActivityName": "Activity Name", "addcar": "Add New Car Rental", "Locationation": "Location information", "delecar": "Delete Car Rental", "DeleteActivity": "Delete Activity", "yesde": "Yes, Delete", "yesdecar": "Yes, Delete Car Rent", "allcar": "All Car Rentals", "carr": "Car Rentals", "Areyousureyouwanttodeletethisactivitcaroseeagain": "Are you sure you want to delete this Car Rental,if yes you wont be able to see again", "Areyousureyouwanttodeletethisactivityifyesyouwontbeabletoseeagain": "Are you sure you want to delete this activity,if yes you wont be able to see again", "yesDeleteactivity": "yes,Delete activity", "Shops": "Coffee Shops", "Allshops": "All Coffee Shops", "AskingPrice": "Asking Price", "Addnewshop": "Add new shop", "contact": "Contact Us", "Shopname": "Shop name", "Message": "Message", "Submit": "Submit", "Setlocationonmap": "Set location on map?", "DeleteShop": "Delete Shop", "deleare": "Are you sure you want to delete this Area, if yes you won’t be able to see it again.", "deletmsgres": "Are you sure you want to delete this Restaurant, if yes you won’t be able to see it again.", "AreyousureyouwanttodeletethisShopifyesyouwontbeabletoseeagain": "Are you sure you want to delete this Shop,if yes you wont be able to see again", "yesDeleteShop": "yes,DeleteShop", "Areas": "Areas", "AllAreas": "All Areas", "shopar": "Arabic Shop Name", "shopen": "English Shop Name", "AddnewArea": "Add new Area", "allres": "All Restaurants", "Areaname": "Area name", "MediaUpload": "Media Upload", "Almalekfahedstreet": "Al malek fahed street", "Pleaseiwantaverycleanandbigcar": "Please i want a very clean and big car", "Yes": "Yes", "Dubaicitycenter": "Dubai city center", "usersusedpromocode": "users used promo code", "choosecategory": "choose category", "addedon": "added on", "setallfeatures": "setallfeatures", "RoofTop": "<PERSON><PERSON>", "arapro": "Arabic Property Name", "enpro": "English Property Name", "startsize": "Start size", "endsize": "End size", "resara": "Arabic Restaurant Name", "resen": "English Restaurant Name", "Averagep": "Average price", "deleres": "Delete Restaurant", "AddCaterogryfeature": "Add Caterogry feature", "RemoveApplicationfeature": "Remove Application feature", "Therearenoitems": "There are no items", "areyousureyouwanttodeletethisimage": "are you sure you want to delete this image", "deleteimage": "delete image", "configurationhasbeenupdatedsuccessfully": "configuration has been updated successfully", "GoogleReviewName": "Google Review Name", "Title": "Title", "isPublished": "is Published", "cannotDeleteBecauseItIsUsedInVideo": "cannot Delete Because It Is Used In Property", "TitleEn": "English Title", "TitleAr": "Arabic Title", "descriptionEn": "English Description", "descriptionAr": "Arabic Description", "CarYears": "Car Years", "AllCarYears": "All Car Years", "AddNewYear": "Add New Year", "EditYear": "Edit Year", "Year": "Year", "DeleteYear": "Delete Year", "yesDeleteyear": "yes,Delete year", "Areyousureyouwanttodeletethisyear": "Are you sure you want to delete this year,if yes you wont be able to see again", "AgreedPrice": "Agreed Price", "startDate": "Start Date", "endDate": "End Date", "PriceDetails": "Price Details", "NormalPrices": "Normal Prices", "ScheduleDaysPrices": "Schedule Days Prices", "ShowSchedulePrices": "Show Schedule Prices", "NormalDays": "Normal Days", "DayPrice": "Day Price", "NightPrice": "Night Price", "days": "days", "websiteAr": "Arabic Website", "Users": "Users", "AllUsers": "All Users", "ThereAreNoUsers": "There are no users", "androidUsers": "Android Users", "iosUsers": "IOS Users", "AddNewChalet": "Add New Chalet", "Allchalets": "All chalets", "Chalets": "Chalets", "chaletara": "Arabic Chalet Name", "chaleteng": "English Chalet Name", "DeleteChalet": "Delete Chalet", "Areyousureyouwanttodeletethischaletifyesyouwontbeabletoseeagain": "Are you sure you want to delete this chalet,if yes you wont be able to see again", "yesDeletechalet": "yes,Delete chalet", "rmsProperties": "RMS Properties", "rmsUnits": "Units", "FullnameAr": "Full Name Arabic", "fillRMSFields": "Fill RMS Fields above please", "selectRmsUnit": "Select RMS Unit", "selectRmsProperty": "Select RMS Property", "searchRmsProperty": "Search RMS Property", "searchRmsUnit": "Search RMS Unit", "noResultFound": "No Result Found", "selectRmsArea": "Select RMS Area", "searchRmsArea": "Search Area", "ChooseCategories": "Choose Categories", "deletePricePlan": "Delete Price Plan", "cannotDeleteBecauseUsed": "Cannot delete because it is being used", "paymentPlan": "Payment Plan", "date": "Date", "enterDate": "Enter date", "order": "Order", "enterOrder": "Enter order", "payment": "Payment (%)", "enterPaymentPercentage": "Enter payment percentage", "installment": "Installment", "enterInstallmentInfo": "Enter installment info", "addPricePlan": "Add Price Plan", "editPricePlan": "Edit Price Plan", "descriptionEnglish": "Description (English)", "enterDescriptionEnglish": "Enter description in English", "descriptionArabic": "Description (Arabic)", "enterDescriptionArabic": "Enter description in Arabic", "paymentPlans": "Payment Plans", "addMore": "Add More", "projectPlans": "Project Plans", "addProjectPlan": "Add Project Plan", "floorPlans": "Floor Plans", "addFloorPlan": "Add Floor Plan", "featuredSettings": "Featured Settings", "featuredOnHome": "Featured on Home", "displayProjectOnHomePage": "Display this project on the home page", "featuredInCategory": "Featured in Category", "displayProjectInCategory": "Display this project prominently in its category", "bedroomsArabic": "Bedrooms (Arabic)", "enterBedroomsArabic": "Enter bedrooms in Arabic", "bedroomsEnglish": "Bedrooms (English)", "enterBedroomsEnglish": "Enter bedrooms in English", "priceFrom": "Price From", "enterStartingPrice": "Enter starting price", "priceTo": "Price To", "enterEndingPrice": "Enter ending price", "spaceSizeArabic": "<PERSON> Size (Arabic)", "enterSpaceSizeArabic": "Enter space size in Arabic", "spaceSizeEnglish": "<PERSON> Size (English)", "enterSpaceSizeEnglish": "Enter space size in English", "floorPlanNameArabic": "Name (Arabic)", "enterFloorPlanNameArabic": "Enter floor plan name in Arabic", "floorPlanNameEnglish": "Name (English)", "enterFloorPlanNameEnglish": "Enter floor plan name in English", "addProject": "Add Project", "addNewProject": "Add New Project", "projectAddedSuccessfully": "Project added successfully", "failedToAddProject": "Failed to add project", "pleaseEnterArabicName": "Please enter Arabic name", "pleaseEnterEnglishName": "Please enter English name", "pleaseEnterArabicDescription": "Please enter Arabic description", "pleaseEnterEnglishDescription": "Please enter English description", "pleaseSelectType": "Please select a type", "pleaseSelectPropertyStatus": "Please select property status", "pleaseSelectPricePlan": "Please select a price plan", "pleaseSelectLocation": "Please select location", "locationSelected": "Location selected", "selectLocation": "Select Location", "propertyStatus": "Property Status", "pricePlan": "Price Plan", "galleryImages": "Gallery Images", "imageSelected": "image selected", "imagesSelected": "images selected", "paymentMethod": "Payment Method", "cash": "Cash", "selectPaymentMethod": "Select Payment Method", "floorPlanImage": "Floor Plan Image", "descriptions": "Descriptions", "enterArabicDescription": "Enter Arabic Description", "enterEnglishDescription": "Enter English Description", "arabicDescriptionSaved": "Arabic description saved successfully", "englishDescriptionSaved": "English description saved successfully", "save": "Save", "cancel": "Cancel"}