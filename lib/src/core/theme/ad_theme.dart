import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'ad_colors.dart';

abstract class ADTheme {
  static ThemeData lightTheme = ThemeData(
      scaffoldBackgroundColor: Colors.white,
      useMaterial3: true,
      primarySwatch: _getMaterialColor(ADColors.primary),
      cupertinoOverrideTheme: const NoDefaultCupertinoThemeData(
        primaryColor: CupertinoColors.link,
      ),
      appBarTheme: const AppBarTheme(
          backgroundColor: GlobalColors.primaryColor,
          elevation: 0,
          centerTitle: true,
          titleTextStyle: TextStyle(
              color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
          toolbarTextStyle: TextStyle(color: Colors.white),
          iconTheme: IconThemeData(color: Colors.white)),
      textTheme: GoogleFonts.latoTextTheme(),
      elevatedButtonTheme: ElevatedButtonThemeData(
          style: ButtonStyle(
              minimumSize:
                  MaterialStateProperty.all(const Size(double.infinity, 50)),
              textStyle: MaterialStateProperty.all(GoogleFonts.latoTextTheme()
                  .titleLarge
                  ?.copyWith(fontSize: 16.0)))));

  static MaterialColor _getMaterialColor(Color color) {
    List strengths = <double>[.05];
    final swatch = <int, Color>{};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (double strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.value, swatch);
  }
}
