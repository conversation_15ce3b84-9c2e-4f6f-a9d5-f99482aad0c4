import 'package:admin_dubai/src/core/utils/logger.dart';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';

class RmsService {
  static final Dio _dio = Dio();
  static String token = '';
  static List<(int id, String name)> properties = [];

  // * Get Auth Token
  static Future<String> getAuthToken() async {
    try {
      final response = await _dio.post(
        _RmsConstants.authToken,
        data: _RmsConstants.productionCredentials,
      );

      Log.w('RMS_Auth_Token_Response: $response');

      if (response.statusCode == 200 && response.data['token'] != null) {
        token = response.data['token'];
        return token;
      }
    } catch (error, stacktrace) {
      Log.e('RMS_Auth_Token_Error: $error stackTrace: $stacktrace');
    }
    return '';
  }

  // * Get Properties
  static Future<List<(int id, String name)>> getProperties() async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }
      final response = await _dio.get(
        _RmsConstants.properties,
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final propertiesList = response.data as List;
        Log.i('RMS_Properties_Response: $response');

        properties = List.generate(
          propertiesList.length,
          (index) =>
              (propertiesList[index]['id'], propertiesList[index]['name']),
        );

        return properties;
      }
    } catch (error, stacktrace) {
      Log.e('RMS_Properties_Error: $error stackTrace: $stacktrace');
    }

    return [];
  }

  // * Get Categories By Property
  static Future<List<(int id, String name)>> getCategoriesByProperty(
      int? propertyId) async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }
      final response = await _dio.get(
        '${_RmsConstants.categories}?propertyId=$propertyId',
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final categories = response.data as List;
        Log.i('RMS_Categories_Response: $response');

        return List.generate(
          categories.length,
          (index) => (categories[index]['id'], categories[index]['name']),
        );
      }
    } catch (error, stacktrace) {
      Log.e('RMS_Categories_Error: $error stackTrace: $stacktrace');
    }

    return [];
  }

  // * Get Areas By Categories
  static Future<List<(int id, String name)>> getAreasByCategory(
      int? categoryId) async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }
      final response = await _dio.get(
        _RmsConstants.areasByCategory(categoryId),
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final areas = response.data as List;
        Log.i('RMS_Areas_Response: $response');

        return List.generate(
          areas.length,
          (index) => (areas[index]['id'], areas[index]['name']),
        );
      }
    } catch (error, stacktrace) {
      Log.e('RMS_Areas_Error: $error stackTrace: $stacktrace');
    }

    return [];
  }

  // * Get Category Price
  static Future<num> getUnitPrice({
    required int? propertyId,
    required int? unitId,
  }) async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }

      final response = await _dio.post(
        _RmsConstants.categoryPrice,
        data: {
          "propertyId": propertyId,
          "categoryId": unitId,
          "agentId": _RmsConstants.agentId,
          "rateTypeId": _RmsConstants.rateTypeId,
          "arrivalDate": _RmsConstants.arrivalDate,
          "departureDate": _RmsConstants.departureDate,
        },
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final category = response.data;
        Log.i('RMS_Category_Price_Response: $response');

        return category['baseRate'];
      }
    } catch (error, stacktrace) {
      Log.e('RMS_Category_Price_Error: $error stackTrace: $stacktrace');
    }

    return 0.0;
  }

  // * Get discounts (int id, String name, String description)
  static Future<List<(int id, String name, String description)>>
      getDiscounts() async {
    try {
      if (token.isEmpty) {
        await getAuthToken();
      }
      final response = await _dio.get(
        _RmsConstants.discounts,
        options: Options(
          headers: {
            'authtoken': token,
          },
        ),
      );

      if (response.statusCode == 200) {
        final discounts = response.data as List;
        Log.i('RMS_Discounts_Response: $response');

        return List.generate(
          discounts.length,
          (index) => (
            discounts[index]['id'],
            discounts[index]['name'] ?? '',
            discounts[index]['description'] ?? '',
          ),
        );
      }
    } catch (error, stacktrace) {
      Log.e('RMS_Discounts_Error: $error stackTrace: $stacktrace');
    }

    return [];
  }
}

class _RmsConstants {
  static const String baseUrl = 'https://restapi12.rmscloud.com';

  static const String authToken = '$baseUrl/authToken';
  static const String properties = '$baseUrl/properties';
  static const String categories = '$baseUrl/categories';

  static String areasByCategory(int? categoryId) =>
      '$categories/$categoryId/areas';

  static const String categoryPrice = '$baseUrl/rates/rateQuote';
  static const String discounts = '$baseUrl/discounts';

  // * Production Constants
  static const int agentId = 1078;
  static const int rateTypeId = 9;
  static final String arrivalDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(
    DateTime.now(),
  );
  static final String departureDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(
    DateTime.now().add(
      const Duration(hours: 1),
    ),
  );

  // * Demo Constants
  // static const int agentId = 15;
  // static const int rateTypeId = 9;
  // static const String arrivalDate = "2019-08-23 10:00:00";
  // static const String departureDate = "2019-08-25 13:25:00";

  // * Production Credentials
  static const productionCredentials = {
    "agentId": 1078,
    "agentPassword": "a@6syWmkuaa",
    "clientId": 20642,
    "clientPassword": r"3Kw$!j7B",
    "useTrainingDatabase": false,
    "moduleType": ["guestServices"]
  };

// * Demo Credentials
//   static const demoCredentials = {
//   "agentId": 15,
//   "agentPassword": r"1h&29$vk449f8",
//   "clientId": 11281,
//   "clientPassword": r"6k!Dp$N4",
//   "useTrainingDatabase": false,
//   "moduleType": ["datawarehouse"]
// };
}
