import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/services/rms_service/rms_service.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:get/get_utils/get_utils.dart';

class RmsDropDownWidgets extends HookWidget {
  final ValueNotifier<int?> selectedRMSProperty;
  final ValueNotifier<int?> selectedRMSCategory;
  final ValueNotifier<int?> selectedRMSArea;
  final TextEditingController unitPriceCtrl;

  const RmsDropDownWidgets({
    super.key,
    required this.selectedRMSProperty,
    required this.selectedRMSCategory,
    required this.selectedRMSArea,
    required this.unitPriceCtrl,
  });

  @override
  Widget build(BuildContext context) {
    final isLoaded = useState(false);
    final properties = useState<List<(int id, String name)>>([]);
    final categories = useState<List<(int id, String name)>>([]);
    final areas = useState<List<(int id, String name)>>([]);
    final isCategoryLoading = useState(false);
    final isAreaLoading = useState(false);

    Future<void> getAreas({int? categoryId}) async {
      if (categoryId != null) {
        isAreaLoading.value = true;

        final areasData = await RmsService.getAreasByCategory(categoryId);
        areas.value = areasData;

        isAreaLoading.value = false;
      }
    }

    void getData({int? propertyId}) async {
      if (RmsService.properties.isEmpty) {
        final propertiesData = await RmsService.getProperties();
        RmsService.properties = propertiesData;
        properties.value = propertiesData;
      } else {
        properties.value = RmsService.properties;
      }

      if (properties.value.isNotEmpty) {
        isCategoryLoading.value = true;

        final categoriesData = await RmsService.getCategoriesByProperty(
          propertyId ?? properties.value.firstOrNull?.$1,
        );
        categories.value = categoriesData;

        isCategoryLoading.value = false;

        if (selectedRMSCategory.value != null) {
          await getAreas(categoryId: selectedRMSCategory.value);
        }
      }

      isLoaded.value = true;
    }

    void showSearchBottomSheet(
      BuildContext context, {
      required bool isProperty,
      bool isCategory = false,
    }) {
      final List<(int, String)> items = isProperty
          ? properties.value
          : isCategory
              ? categories.value
              : areas.value;
      final ValueNotifier<String> searchQuery = ValueNotifier<String>('');

      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  onChanged: (value) => searchQuery.value = value,
                  decoration: InputDecoration(
                    hintText: isProperty
                        ? S.of(context).searchRmsProperty
                        : isCategory
                            ? S.of(context).searchRmsUnit
                            : S.of(context).searchRmsArea,
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: ValueListenableBuilder<String>(
                    valueListenable: searchQuery,
                    builder: (context, query, child) {
                      final filteredItems = items
                          .where((item) => item.$2
                              .toLowerCase()
                              .contains(query.toLowerCase()))
                          .toList();

                      if (filteredItems.isEmpty) {
                        return Center(
                          child: Text(
                            S.of(context).noResultFound,
                            style: const TextStyle(fontSize: 16),
                          ),
                        );
                      }

                      return ListView.builder(
                        itemCount: filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = filteredItems[index];
                          return ListTile(
                            title: Row(
                              children: [
                                (isProperty &&
                                            selectedRMSProperty.value ==
                                                item.$1) ||
                                        (isCategory &&
                                            selectedRMSCategory.value ==
                                                item.$1) ||
                                        (!isProperty &&
                                            !isCategory &&
                                            selectedRMSArea.value == item.$1)
                                    ? const CircleAvatar(
                                        backgroundColor:
                                            GlobalColors.primaryColor,
                                        radius: 5,
                                      )
                                    : const SizedBox.shrink(),
                                const SizedBox(width: 10),
                                Text(item.$2),
                              ],
                            ),
                            onTap: () async {
                              if (isProperty) {
                                selectedRMSProperty.value = item.$1;
                                getData(propertyId: item.$1);
                              } else if (isCategory) {
                                selectedRMSCategory.value = item.$1;
                                await getAreas(categoryId: item.$1);

                                RmsService.getUnitPrice(
                                  propertyId: selectedRMSProperty.value,
                                  unitId: selectedRMSCategory.value,
                                ).then(
                                  (value) {
                                    unitPriceCtrl.text =
                                        value.toStringAsFixed(2);
                                  },
                                );
                              } else {
                                selectedRMSArea.value = item.$1;
                              }
                              Navigator.pop(context);
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        getData(
          propertyId: selectedRMSProperty.value,
        );
      });
      return () {};
    }, const []);

    if (!isLoaded.value) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        GestureDetector(
          onTap: () => showSearchBottomSheet(context, isProperty: true),
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(width: 0.5, color: Colors.grey[300]!),
              color: Colors.white,
            ),
            height: 50,
            alignment: Alignment.centerLeft,
            child: Text(
              selectedRMSProperty.value != null
                  ? properties.value
                          .firstWhereOrNull(
                              (p) => p.$1 == selectedRMSProperty.value!)
                          ?.$2 ??
                      S.of(context).selectRmsProperty
                  : S.of(context).selectRmsProperty,
              style: const TextStyle(fontSize: 16, color: Colors.black),
            ),
          ),
        ),
        const SizedBox(height: 10),
        if (isCategoryLoading.value)
          const Center(child: LinearProgressIndicator())
        else
          GestureDetector(
            onTap: () => showSearchBottomSheet(context,
                isProperty: false, isCategory: true),
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.symmetric(horizontal: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(width: 0.5, color: Colors.grey[300]!),
                color: Colors.white,
              ),
              height: 50,
              alignment: Alignment.centerLeft,
              child: Text(
                selectedRMSCategory.value != null
                    ? categories.value
                            .firstWhereOrNull(
                                (c) => c.$1 == selectedRMSCategory.value!)
                            ?.$2 ??
                        S.of(context).selectRmsUnit
                    : S.of(context).selectRmsUnit,
                style: const TextStyle(fontSize: 16, color: Colors.black),
              ),
            ),
          ),
        const SizedBox(height: 10),
        if (isAreaLoading.value)
          const Center(child: LinearProgressIndicator())
        else
          GestureDetector(
            onTap: () => showSearchBottomSheet(context,
                isProperty: false, isCategory: false),
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.symmetric(horizontal: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(width: 0.5, color: Colors.grey[300]!),
                color: Colors.white,
              ),
              height: 50,
              alignment: Alignment.centerLeft,
              child: Text(
                selectedRMSArea.value != null
                    ? areas.value
                            .firstWhereOrNull(
                                (a) => a.$1 == selectedRMSArea.value!)
                            ?.$2 ??
                        S.of(context).selectRmsArea
                    : S.of(context).selectRmsArea,
                style: const TextStyle(fontSize: 16, color: Colors.black),
              ),
            ),
          ),
      ],
    );
  }
}
