import 'dart:developer';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/services/rms_service/rms_service.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:get/get_utils/get_utils.dart';

class RmsDiscountDropDownWidget extends HookWidget {
  final ValueNotifier<int?> selectedRMSDiscount;

  const RmsDiscountDropDownWidget({
    super.key,
    required this.selectedRMSDiscount,
  });

  @override
  Widget build(BuildContext context) {
    log('asfssfa ${selectedRMSDiscount.value}');
    final isLoaded = useState(false);
    final discounts =
        useState<List<(int id, String name, String description)>>([]);
    final isDiscountLoading = useState(false);

    void getData() async {
      isDiscountLoading.value = true;

      final discountsData = await RmsService.getDiscounts();
      discounts.value = discountsData;

      isDiscountLoading.value = false;
      isLoaded.value = true;
    }

    void showSearchBottomSheet(BuildContext context) {
      final List<(int, String, String)> items = discounts.value;
      final ValueNotifier<String> searchQuery = ValueNotifier<String>('');

      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  onChanged: (value) => searchQuery.value = value,
                  decoration: InputDecoration(
                    hintText: S.of(context).searchRmsDiscount,
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: ValueListenableBuilder<String>(
                    valueListenable: searchQuery,
                    builder: (context, query, child) {
                      final filteredItems = items
                          .where((item) => item.$2
                              .toLowerCase()
                              .contains(query.toLowerCase()))
                          .toList();

                      if (filteredItems.isEmpty) {
                        return Center(
                          child: Text(
                            S.of(context).noResultFound,
                            style: const TextStyle(fontSize: 16),
                          ),
                        );
                      }

                      return ListView.builder(
                        itemCount: filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = filteredItems[index];
                          return ListTile(
                            title: Row(
                              children: [
                                selectedRMSDiscount.value == item.$1
                                    ? const CircleAvatar(
                                        backgroundColor:
                                            GlobalColors.primaryColor,
                                        radius: 5,
                                      )
                                    : const SizedBox.shrink(),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(item.$2),
                                      Text(item.$3,
                                          style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            onTap: () {
                              selectedRMSDiscount.value = item.$1;
                              Navigator.pop(context);
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        getData();
      });
      return () {};
    }, const []);

    if (!isLoaded.value) {
      return const Center(child: CircularProgressIndicator());
    }

    return GestureDetector(
      onTap: () => showSearchBottomSheet(context),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(width: 0.5, color: Colors.grey[300]!),
          color: Colors.white,
        ),
        height: 50,
        alignment: Alignment.centerLeft,
        child: Text(
          selectedRMSDiscount.value != null
              ? discounts.value
                      .firstWhereOrNull(
                          (d) => d.$1 == selectedRMSDiscount.value!)
                      ?.$2 ??
                  S.of(context).selectRmsDiscount
              : S.of(context).selectRmsDiscount,
          style: const TextStyle(fontSize: 16, color: Colors.black),
        ),
      ),
    );
  }
}
