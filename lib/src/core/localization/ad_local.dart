import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ADLocale {
  final Locale locale;

  ADLocale(this.locale);

  static String? languageCode = 'en';

  static Map<String?, String?> _loadedLocalizedValues = {};

  static ADLocale of(BuildContext context) {
    return Localizations.of(context, ADLocale);
  }

  Future loadLang() async {
    String? _langFile = await rootBundle
        .loadString('assets/translations/${locale.languageCode}.json');
    languageCode = locale.languageCode;
    Map<String?, dynamic> _loadedValues = jsonDecode(_langFile);
    _loadedLocalizedValues =
        _loadedValues.map((key, value) => MapEntry(key, value.toString()));
  }

  String? getTranslated(String? key) {
    return _loadedLocalizedValues[key] ?? 'Not found';
  }

  static String? getTranslatedWithoutContext(String? key) {
    return _loadedLocalizedValues[key] ?? 'Not found';
  }

  static const LocalizationsDelegate<ADLocale> delegate = _AppLocalDelegate();
}

class _AppLocalDelegate extends LocalizationsDelegate<ADLocale> {
  const _AppLocalDelegate();
  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<ADLocale> load(Locale locale) async {
    ADLocale appLocale = ADLocale(locale);
    await appLocale.loadLang();
    return appLocale;
  }

  @override
  bool shouldReload(_AppLocalDelegate old) => false;
}

getLang(BuildContext context, String? key) {
  return ADLocale.of(context).getTranslated(key);
}
