import 'package:dio/dio.dart';

String handleError(dynamic error) {
  String errorDescription = "";
  if (error == null) {
    errorDescription = "Unexpected error occurred";
    return errorDescription;
  }
  switch (error) {
    case DioErrorType.cancel:
      errorDescription = "Request to API server was cancelled";
      break;
    case DioErrorType.connectTimeout:
      errorDescription = "Connection timeout with API server";
      break;
    case DioErrorType.sendTimeout:
      errorDescription =
          "Connection to API server failed due to internet connection";
      break;
    case DioErrorType.receiveTimeout:
      errorDescription = "Receive timeout in connection with API server";
      break;
    case DioErrorType.response:
      _handleError(error.response!.statusCode!, error.response!.confugrations);
      break;
    case DioErrorType.other:
      errorDescription = "Connection Error";
      break;
    default:
      errorDescription = "Unexpected error occurred";
  }
  return errorDescription;
}

String _handleError(int statusCode, dynamic error) {
  switch (statusCode) {
    case 400:
      return 'Bad request';
    case 404:
      return error["message"];
    case 500:
      return 'Internal server error';
    default:
      return 'Oops something went wrong';
  }
}
