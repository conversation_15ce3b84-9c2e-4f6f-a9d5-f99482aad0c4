import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:admin_dubai/src/core/utils/screen_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RoundedAnimatedButton extends StatefulWidget {
  final String? text;
  final Color? color;
  final Widget? leadingImage;
  final Color? textColor;
  final bool? isLoading;

  final VoidCallback onPressed;

  const RoundedAnimatedButton(
      {Key? key,
      this.text,
      this.color,
      this.leadingImage,
      this.textColor,
      this.isLoading = false,
      required this.onPressed})
      : super(key: key);

  @override
  _RoundedAnimatedButtonState createState() => _RoundedAnimatedButtonState();
}

class _RoundedAnimatedButtonState extends State<RoundedAnimatedButton> {
  final Duration _animationDuration = const Duration(seconds: 1);

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: _animationDuration,
      child: _getAnimatedWidget(),
//      transitionBuilder: (Widget child, Animation<double> animation) {
//        return ScaleTransition(
//          scale: animation,
//          child: child,
//        );
//      },
    );
  }

  _getAnimatedWidget() {
    if (widget.isLoading!) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: const <Widget>[
          Center(child: CircularProgressIndicator()),
        ],
      );
    }
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: ScreenUtil().setWidth(0.0).toDouble()),
      child: AnimatedContainer(
        duration: _animationDuration,
        width: widget.isLoading!
            ? ScreenUtil().setHeight(130).toDouble()
            : MediaQuery.of(context).size.width -
                ScreenUtil().setWidth(170).toDouble(),
        height: ScreenUtil().setHeight(130).toDouble(),
        child: ElevatedButton(
          onPressed: () {
            widget.onPressed();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.color ?? GlobalColors.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(widget.isLoading!
                  ? ScreenUtil().setHeight(130).toDouble()
                  : ScreenUtil().setWidth(18).toDouble()),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: widget.leadingImage == null
                ? MainAxisAlignment.center
                : MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    widget.text!,
                    style: GlobalStyles.kDefaultGreyTextStyle.copyWith(
                        color: widget.textColor ?? Colors.white,
                        fontSize: ScreensHelper.scaleText(
                          40,
                        ),
                        fontWeight: FontWeight.w500),
                  ),
                ),
              ),
              widget.leadingImage ?? Container(),
            ],
          ),
        ),
      ),
    );
  }
}
