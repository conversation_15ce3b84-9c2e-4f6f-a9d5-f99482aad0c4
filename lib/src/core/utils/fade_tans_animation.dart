import 'package:flutter/material.dart';

class FadeTransAnimation extends StatefulWidget {
  final Widget child;
  final int? delayInMillisecond;

  final AxisDirection direction;

  final double translateYDistance;

  FadeTransAnimation(
      {this.delayInMillisecond,
      required this.child,
      this.direction = AxisDirection.up,
      this.translateYDistance = 30});

  @override
  State<FadeTransAnimation> createState() => _FadeTransAnimationState();
}

class _FadeTransAnimationState extends State<FadeTransAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);

    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: Transform.translate(
        offset: const Offset(0, 0),
        child: widget.child,
      ),
    );
  }
}
