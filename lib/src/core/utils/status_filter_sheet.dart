import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../features/bloc/other_settings_bloc.dart';
import '../../features/models/agent_list_model.dart';
import '../../features/response/agent_list_response.dart';
import '../shared_widgets/snack_bar.dart';
import 'fade_tans_animation.dart';
import 'screen_utils.dart';

class StatusFilterSheet extends StatefulWidget {
  Function? applyFilter;
  StatusFilterSheet({super.key, this.applyFilter});

  @override
  _StatusFilterSheetState createState() => _StatusFilterSheetState();
}

class _StatusFilterSheetState extends State<StatusFilterSheet> {
  AgentListModel? _agent;
  String datev = '';
  @override
  Widget build(BuildContext context) {
    ScreensHelper(context);
    return SingleChildScrollView(
      child: AnimatedPadding(
        padding: MediaQuery.of(context).viewInsets,
        duration: const Duration(milliseconds: 500),
        child: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: ScreensHelper.fromWidth(10),
                child: Divider(
                  color: Colors.grey.withOpacity(0.5),
                  thickness: 4,
                  height: ScreensHelper.fromHeight(4),
                ),
              ),
              SizedBox(
                height: ScreensHelper.fromHeight(2.3),
              ),
              FadeTransAnimation(
                delayInMillisecond: 500,
                child: Text(
                  S.of(context).Status,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              SizedBox(
                height: ScreensHelper.fromHeight(2.3),
              ),
              FadeTransAnimation(
                delayInMillisecond: 600,
                child: Padding(
                  padding: EdgeInsets.all(ScreensHelper.fromWidth(1)),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius:
                            BorderRadius.circular(ScreensHelper.fromWidth(3))),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Column(
                        children: [
                          FadeTransAnimation(
                            delayInMillisecond: 800,
                            child: Padding(
                              padding: const EdgeInsets.only(top: 10),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 2),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        GestureDetector(
                                          onTap: () {},
                                          child: TextButton(
                                            onPressed: () async {
                                              Navigator.pop(context);
                                              await widget.applyFilter!(
                                                  null, null);
                                            },
                                            child: const Text("RESET"),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Text(
                                      S.of(context).Agentname,
                                      style: const TextStyle(fontSize: 13),
                                    ),
                                    const SizedBox(height: 10),
                                    StreamBuilder<AgentListResponse?>(
                                      stream: othersettingsbloc.agents.stream,
                                      builder: (BuildContext context,
                                          AsyncSnapshot<AgentListResponse?>
                                              snapshot) {
                                        if (snapshot.hasData &&
                                            snapshot.connectionState !=
                                                ConnectionState.waiting) {
                                          if (snapshot.data!.code != 1) {
                                            snackbar(snapshot.data!.msg ?? '');
                                            return const SizedBox();
                                          }
                                          if (snapshot
                                              .data!.agentList.isEmpty) {
                                            return const SizedBox();
                                          }
                                          _agent =
                                              snapshot.data!.agentList.first;
                                          return Container(
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            padding: const EdgeInsets.fromLTRB(
                                                10, 0, 10, 0),
                                            decoration: BoxDecoration(
                                                border: Border.all(
                                                    width: 1,
                                                    color: const Color(
                                                        0xFFEFEFEF)),
                                                borderRadius:
                                                    BorderRadius.circular(5)),
                                            height: 50,
                                            child: DropdownButtonFormField<
                                                AgentListModel>(
                                              decoration: const InputDecoration(
                                                border: InputBorder.none,
                                              ),
                                              isExpanded: true,
                                              hint: Container(
                                                padding: const EdgeInsets.only(
                                                    left: 5, right: 5),
                                                child: Text(
                                                  S.of(context).Agents,
                                                  style: const TextStyle(
                                                      color: Color(0xffB7B7B7)),
                                                ),
                                              ),
                                              value: snapshot
                                                  .data!.agentList.first,
                                              iconEnabledColor: Colors.black,
                                              items: snapshot.data!.agentList
                                                  .map((AgentListModel value) {
                                                return DropdownMenuItem<
                                                    AgentListModel>(
                                                  value: value,
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .only(start: 10.0),
                                                    child: Text(
                                                      value.fullname!,
                                                      style: const TextStyle(
                                                          fontSize: 16),
                                                    ),
                                                  ),
                                                );
                                              }).toList(),
                                              onChanged: (value) {
                                                print("object");
                                                _agent = value;
                                              },
                                            ),
                                          );
                                        }
                                        return const Center(
                                            child: CircularProgressIndicator());
                                      },
                                    ),
                                    const SizedBox(height: 10),
                                    Padding(
                                      padding:
                                          const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                      child: Text(
                                        S.of(context).Requesteddate,
                                        style: const TextStyle(
                                            fontFamily: 'Roboto-Medium',
                                            fontSize: 13,
                                            color: Color(0xFF1C2127)),
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Padding(
                                      padding:
                                          const EdgeInsets.fromLTRB(0, 0, 0, 0),
                                      child: InkWell(
                                        onTap: () {
                                          FocusScope.of(context)
                                              .requestFocus(new FocusNode());

                                          showDatePicker(
                                                  context: context,
                                                  initialDate: DateTime.now(),
                                                  firstDate: DateTime(1950),
                                                  lastDate: DateTime(2100))
                                              .then((value) {
                                            if (value != null) {
                                              setState(() {
                                                datev = value
                                                    .toString()
                                                    .substring(0, 10);
                                              });
                                            }
                                          });
                                        },
                                        child: Container(
                                          width:
                                              MediaQuery.of(context).size.width,
                                          padding: const EdgeInsets.fromLTRB(
                                              10, 0, 10, 0),
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  width: 1,
                                                  color:
                                                      const Color(0xFFEFEFEF)),
                                              borderRadius:
                                                  BorderRadius.circular(5)),
                                          child: Padding(
                                            padding: const EdgeInsets.fromLTRB(
                                                10, 15, 0, 15),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  datev == ''
                                                      ? 'DD/MM/YY'
                                                      : datev,
                                                  style: const TextStyle(
                                                      fontFamily:
                                                          'Roboto-Regular',
                                                      fontSize: 14,
                                                      color: Color(0xFFB7B7B7)),
                                                ),
                                                Row(
                                                  children: [
                                                    SvgPicture.asset(
                                                        'assets/cal.svg'),
                                                    const Padding(
                                                      padding: EdgeInsets.only(
                                                          left: 6),
                                                      child: Icon(
                                                        Icons
                                                            .keyboard_arrow_down_rounded,
                                                      ),
                                                    )
                                                  ],
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Center(
                                      child: Container(
                                        // padding: EdgeInsets.only(right: 20, left: 20),
                                        child: GestureDetector(
                                          onTap: () async {
                                            Navigator.pop(context);
                                            await widget.applyFilter!(
                                                datev, _agent!.id.toString());
                                          },
                                          child: Container(
                                            height: 50,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            decoration: BoxDecoration(
                                                color:
                                                    GlobalColors.primaryColor,
                                                borderRadius:
                                                    BorderRadius.circular(5)),
                                            child: Container(
                                                padding:
                                                    const EdgeInsets.all(10),
                                                child: Center(
                                                    child: Text(
                                                  S.of(context).ApplyFilter,
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                ))),
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 20),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: ScreensHelper.fromHeight(2.3),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
