import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'generale_utils.dart';

abstract class GlobalStyles {
  static String? lang;

  static TextStyle get kDefaultGreyTextStyle => TextStyle(
      color: GlobalColors.textGrayColor,
      fontFamily: GeneralUtils.localeIt(
        whenArabic: 'Cairo',
      ),
      fontSize: ScreenUtil().setSp(34).toDouble(),
      height: 1.25,
      fontWeight: FontWeight.w400);
}

abstract class GlobalColors {
  static const Color primaryColor = Color(0xFF27b4a8);
  static Color lightPrimaryColor = const Color(0xFF27b4a8).withOpacity(.1);
  static const Color secondaryColor = Color(0xFFeeeeee);
  static Color goldColor = const Color(0xFFE6CB9C);
  static Color primaryGoldColor = const Color(0xFFD8B77F);
  static Color labelBlueTextColor = const Color(0xFF1C2127);
  static Color blueTextColor = const Color(0xFF8B959E);
  static Color grayBackground = const Color(0xFFF1F1F1);
  static Color borderGrayColor = const Color(0xFFF1F1F2);

  static Color blackCardTextColor = const Color(0xFF191C1F);

  static Color lightPrimaryBlue = const Color(0xFF0D5AB8);
  static Color transparentLightBlue = const Color(0xFFE7ECF4);
  static Color blackTextColor = const Color(0xFF333333);
  static Color textGrayColor = const Color(0xFF8B959E);
  static Color yellowDiscount = const Color(0xFFFFD422);
  static Color facebookBlue = const Color(0xFF475993);
  static Color borderSearchColor = const Color(0xFF707070);

  static Color lightGrey = const Color(0xFFF9FAFB);
  static Color darkGrey = const Color(0xFFA6AAB4);

  static Color red = const Color(0xFFE04E4D);
  static Color blue = const Color(0xFF0852AB);
  static Color green = const Color(0xFF2AC294);
  static Color fillDialogColor = const Color(0xFFF5F6F7);
}

abstract class DubaiPagesIcons {
  /// SVG ICONS
  static const appIcon = 'assets/icons/app_icon.svg';
  static const rightArrowIcon = 'assets/icons/right_arrow.svg';
  static const filterIcon = 'assets/filter.svg';
  static const successIcon = 'assets/icons/success.svg';
  static const check = 'assets/check.svg';
  // static const appBarIcon = 'assets/icons/app_icon.svg';

  /// PNG IMAGES
  static const applicationIcon = 'assets/images/applicaion_icon.png';
  static const appbarIcon = 'assets/icons/app_bar_icon.png';
  static const holidaysImage = 'assets/images/holidays.jpg';
}
