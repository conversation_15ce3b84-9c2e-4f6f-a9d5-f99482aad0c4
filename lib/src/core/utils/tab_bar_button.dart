import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:admin_dubai/src/core/utils/screen_utils.dart';
import 'package:flutter/material.dart';

class TabBarButton extends StatefulWidget {
  final bool isSelected;
  final String title;
  void Function() callBack;

  TabBarButton(
      {Key? key,
      this.isSelected = false,
      required this.title,
      required this.callBack})
      : super(key: key);

  @override
  _TabBarButtonState createState() => _TabBarButtonState();
}

class _TabBarButtonState extends State<TabBarButton> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(ScreensHelper.fromWidth(1.2)),
      child: InkWell(
        onTap: () {
          widget.callBack();
        },
        child: AnimatedContainer(
          duration: const Duration(microseconds: 2),
          height: ScreensHelper.fromHeight(6),
          width: ScreensHelper.fromWidth(43),
          decoration: BoxDecoration(
              color: widget.isSelected
                  ? GlobalColors.primaryGoldColor
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(5)),
          child: Center(
            child: Text(
              widget.title,
              style: TextStyle(
                  color: widget.isSelected
                      ? Colors.white
                      : GlobalColors.primaryGoldColor),
            ),
          ),
        ),
      ),
    );
  }
}
