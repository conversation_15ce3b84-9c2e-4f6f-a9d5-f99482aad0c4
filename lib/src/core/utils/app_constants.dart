import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

abstract class AppConstants {
  /// Endpoint
  // static const String endpoint = "https://api.dubaipage.ae/api/"; //? Live
  // static const String endpoint = "https://api2.dubaipage.ae/api/"; //? New Live
  static const String endpoint = "https://demo.dubaipage.ae/api/"; //? Demo
  // static const String endpoint = "https://api.dubaipage.ae/new/public/api/"; //? New Live

  static final Widget uploadIcon = SizedBox(
      width: 15,
      height: 15,
      child: SvgPicture.asset(
        'assets/icons8-upload.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));

  ///supported Locales
  static const List<Locale> supportedLocales = [
    Locale('en'),
    Locale('ar'),
    Locale('ru'),
    Locale('fr'),
    Locale('tr'),
  ];

  //? Main Categories IDs
  static const int hotelsId = 1;
  static const int restaurantsId = 2;
  static const int activitiesId = 3;
  static const int coffeeShopsId = 4;
  static const int holidayHomesId = 8;
  static const int carRentalsId = 6;
  static const int destinationsId = 7;
  static const int propertiesId = 8;
  static const int chaletsId = 9;
  static const int projectsId = 10;
}
