import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:admin_dubai/src/core/utils/screen_utils.dart';
import 'package:flutter/material.dart';

import '../../../generated/l10n.dart';
import 'base_validator.dart';

class RequestedSearchWidget extends StatefulWidget {
  final void Function(String)? onFieldSubmitted;
  const RequestedSearchWidget({super.key, required this.onFieldSubmitted});

  @override
  _RequestedSearchWidgetState createState() => _RequestedSearchWidgetState();
}

class _RequestedSearchWidgetState extends State<RequestedSearchWidget> {
  bool triedToSubmit = false;
  bool _searchValidation = true;
  final _key = GlobalKey<FormFieldState<String>>();
  final _controller = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return TextFormField(
        style: const TextStyle(
            decorationThickness: 0, decorationColor: Color(0xFF), fontSize: 14),
        key: _key,
        controller: _controller,
        focusNode: searchFocusNode,
        textInputAction: TextInputAction.search,
        decoration: InputDecoration(
            prefixIcon: Padding(
              padding: EdgeInsets.all(
                ScreensHelper.fromWidth(4),
              ),
              child: Icon(
                Icons.search,
                color: GlobalColors.textGrayColor,
              ),
            ),
            fillColor: GlobalColors.borderGrayColor,
            focusedErrorBorder: InputBorder.none,
            focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: GlobalColors.borderGrayColor)),
            errorBorder: InputBorder.none,
            enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
              color: GlobalColors.borderGrayColor,
            )),
            errorStyle: const TextStyle(height: 0.8),
            border: OutlineInputBorder(
              borderSide:
                  BorderSide(color: GlobalColors.borderGrayColor, width: 2.0),
            ),
            isDense: true,
            hintText: S.of(context).Search,
            hintStyle: TextStyle(color: GlobalColors.textGrayColor)),
        validator: (value) {
          return BaseValidator.validateValue(
            context,
            value!,
            [],
            _searchValidation,
          );
        },
        //autovalidate: false,
        onChanged: (value) {
          if (!triedToSubmit) return;
          _searchValidation = true;
          if (_key.currentState!.validate()) {
            setState(() {
              _searchValidation = false;
            });
          }
        },
        onFieldSubmitted: widget.onFieldSubmitted);
  }
}
