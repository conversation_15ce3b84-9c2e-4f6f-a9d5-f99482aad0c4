import 'package:flutter/material.dart';

class ADTextFormField extends StatelessWidget {
  const ADTextFormField(
      {Key? key,
      this.label,
      this.validator,
      this.onSaved,
      this.keyboardType = TextInputType.text,
      this.textInputAction,
      this.controller})
      : super(key: key);
  final String? label;
  final String? Function(String?)? validator;
  final void Function(String?)? onSaved;
  final TextInputType keyboardType;
  final TextInputAction? textInputAction;
  final TextEditingController? controller;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label!,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(
          height: 10,
        ),
        Container(
          height: 50,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(width: 0.5, color: Colors.grey),
              color: Colors.white),
          child: Container(
            padding:
                const EdgeInsets.only(left: 20, right: 20, top: 15, bottom: 15),
            child: TextForm<PERSON><PERSON>(
              controller: controller,
              keyboardType: keyboardType,
              decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: label,
                  hintStyle:
                      const TextStyle(color: Color(0xffB7B7B7), fontSize: 14)),
              validator: validator!,
              onSaved: onSaved!,
              textInputAction: textInputAction,
            ),
          ),
        ),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }
}
