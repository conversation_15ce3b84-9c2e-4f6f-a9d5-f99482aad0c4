import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

void snackbar(String msg) {
  Fluttertoast.showToast(
      msg: msg,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      timeInSecForIosWeb: 3,
      backgroundColor: Colors.red,
      textColor: Colors.white,
      fontSize: 16.0);
}

late ProgressDialog pr;
void progrsss(BuildContext context) {
  pr = ProgressDialog(context);
  pr.update(
    progress: 50.0,
    message: 'Please Wait',
    progressWidget: Container(
        padding: const EdgeInsets.all(8.0),
        child: const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xff233549)))),
    maxProgress: 100.0,
    progressTextStyle: const TextStyle(
        color: Colors.black, fontSize: 13.0, fontWeight: FontWeight.w400),
    messageTextStyle: const TextStyle(
        color: Colors.black, fontSize: 19.0, fontWeight: FontWeight.w600),
  );
}

Widget buildErrorWidget(String error) {
  return Center(
      child: Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Text(error),
    ],
  ));
}

Widget nodatafound(String msg) {
  final Widget svg2 = SizedBox(
      width: 88,
      height: 88.0,
      child: SvgPicture.asset(
        'assets/Group 6222.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ));
  return Center(
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(
          height: 20,
        ),
        svg2,
        const SizedBox(
          height: 20,
        ),
        Text(
          msg,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    ),
  );
}
