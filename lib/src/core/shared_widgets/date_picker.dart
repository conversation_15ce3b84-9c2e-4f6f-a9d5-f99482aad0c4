import 'package:datetime_picker_formfield/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BuildDateFieldWidget extends StatelessWidget {
  final TextEditingController controller;
  final format;

  const BuildDateFieldWidget(this.controller, {Key? key, this.format})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 55,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(3)),
        child: Container(
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(5)),
                border: Border.all(color: Colors.black12, width: 1.0)),
            child: DateTimeField(
                controller: controller,
                //
                format: format ?? DateFormat("yyyy-MM-dd"),
                initialValue: DateTime.now(),
                decoration: const InputDecoration(
                    hintText: 'DD/MM/YY',
                    hintStyle: TextStyle(color: Colors.grey, fontSize: 12),
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(5.0),
                      child: SizedBox(),
                    ),
                    prefixIconConstraints: BoxConstraints(
                      minWidth: 15,
                      minHeight: 15,
                    ),
                    border: InputBorder.none),
                onShowPicker: (context, currentValue) {
                  return showDatePicker(
                      context: context,
                      firstDate: DateTime(1900),
                      initialDate: currentValue ?? DateTime.now(),
                      lastDate: DateTime(2100));
                })));
  }
}
