import 'package:flutter/material.dart';

import '../../../generated/l10n.dart';
import '../../features/bloc/category_bloc.dart';
import '../../features/models/main_category_model.dart';

class MainCategoryDropDown extends StatefulWidget {
  final MainCategoryModel? selectedCategory;
  final Function(MainCategoryModel?) onChanged;
  final List<int> allowedCatIds;

  const MainCategoryDropDown({
    super.key,
    required this.selectedCategory,
    required this.onChanged,
    this.allowedCatIds = const [],
  });

  @override
  State<MainCategoryDropDown> createState() => _MainCategoryDropDownState();
}

class _MainCategoryDropDownState extends State<MainCategoryDropDown> {
  @override
  void initState() {
    super.initState();

    categoryBloc.getMainCategories();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<MainCategoryModel>?>(
        stream: categoryBloc.mainCategoriesSubject.stream,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: LinearProgressIndicator());
          }
          var category = snapshot.data ?? [];

          if (widget.allowedCatIds.isNotEmpty) {
            category = category
                .where((element) => widget.allowedCatIds.contains(element.id))
                .toList();
          }

          return Container(
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(width: 0.5, color: Colors.grey),
                  color: Colors.white),
              height: 50,
              child: DropdownButton<MainCategoryModel>(
                  isExpanded: true,
                  hint: Container(
                    padding: const EdgeInsets.only(left: 5, right: 5),
                    child: Text(
                      S.of(context).ChooseCategory,
                      style: const TextStyle(color: Color(0xffB7B7B7)),
                    ),
                  ),
                  value: widget.selectedCategory,
                  underline: const SizedBox(),
                  iconEnabledColor: Colors.black,
                  items: category.map((MainCategoryModel value) {
                    return DropdownMenuItem<MainCategoryModel>(
                      value: value,
                      child: Container(
                          padding: const EdgeInsets.only(left: 10, right: 10),
                          child: Text(
                            value.name ?? "",
                            style: const TextStyle(fontSize: 16),
                          )),
                    );
                  }).toList(),
                  onChanged: widget.onChanged));
        });
  }
}
