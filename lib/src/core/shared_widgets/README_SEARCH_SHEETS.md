# Search Sheets Documentation

This document describes the new search sheet widgets created for the Dubai Apps project.

## Overview

Three new search sheet widgets have been created based on the `BaseSearchSheet` pattern:

1. **TypesSearchSheet** - For property types selection
2. **PropertyStatusSearchSheet** - For property status selection  
3. **PricePlansSearchSheet** - For price plans selection

All widgets are built using HookWidget and include "Add New" functionality that allows users to navigate to the respective add/edit forms.

## Enhanced BaseSearchSheet

The `BaseSearchSheet` has been enhanced with the following new properties:

- `showAddNew` - Boolean to show/hide the "Add New" button
- `addNewText` - Custom text for the "Add New" button
- `onAddNew` - Callback function when "Add New" is pressed
- `onDataRefresh` - Callback to refresh data after adding new items

## Usage Examples

### TypesSearchSheet

```dart
TypesSearchSheet(
  label: S.of(context).Type,
  selectedValue: selectedType.value,
  onChanged: (value) {
    selectedType.value = value;
  },
  category: 'Properties', // Optional category filter
)
```

### PropertyStatusSearchSheet

```dart
PropertyStatusSearchSheet(
  label: 'Property Status',
  selectedValue: selectedPropertyStatus.value,
  onChanged: (value) {
    selectedPropertyStatus.value = value;
  },
)
```

### PricePlansSearchSheet

```dart
PricePlansSearchSheet(
  label: 'Price Plan',
  selectedValue: selectedPricePlan.value,
  onChanged: (value) {
    selectedPricePlan.value = value;
  },
)
```

### Enhanced BaseSearchSheet with Add New

```dart
BaseSearchSheet(
  label: context.tr.city,
  data: cities,
  itemModelAsName: (city) => city,
  selectedValue: selectedCity.value,
  isEng: AuthBloc.isEnglish,
  showAddNew: true,
  addNewText: AuthBloc.isEnglish ? "Add New City" : "إضافة مدينة جديدة",
  onChanged: (value) {
    selectedCity.value = value;
  },
  onAddNew: () async {
    await Navigator.push(context, MaterialPageRoute(builder: (context) => AddCityPage()));
  },
  onDataRefresh: () {
    // Refresh the data list
    loadCities();
  },
)
```

## Features

### Search Functionality
- Real-time search filtering
- Case-insensitive search
- Supports both English and Arabic text

### Add New Functionality
- Dynamic "Add New" button
- Navigates to appropriate add/edit forms
- Automatically refreshes data after adding new items
- Closes bottom sheet before navigation

### Localization Support
- Uses `S.of(context)` for proper localization
- Supports both English and Arabic languages
- Uses `AuthBloc.isEnglish` for language detection

### Data Loading
- Automatic data loading on sheet open
- Loading indicators during data fetch
- Error handling for failed requests
- Stream-based data updates

## API Integration

The search sheets integrate with the following API endpoints:

- **Types**: `othersettingsbloc.gettypes()` and `othersettingsbloc.types.stream`
- **Property Status**: `othersettingsbloc.getPropertyStatus()` and `othersettingsbloc.propertyStatus.stream`
- **Price Plans**: `othersettingsbloc.getPricePlans()` and `othersettingsbloc.pricePlans.stream`

## Navigation Integration

Each search sheet navigates to the appropriate form:

- **TypesSearchSheet** → `AddType()` page
- **PropertyStatusSearchSheet** → `PropertyStatusForm()` page
- **PricePlansSearchSheet** → `PricePlanForm()` page

## File Structure

```
lib/src/core/shared_widgets/
├── base_search_sheet.dart (enhanced)
├── types_search_sheet.dart (new)
├── property_status_search_sheet.dart (new)
├── price_plans_search_sheet.dart (new)
├── search_sheets_usage_example.dart (example)
└── README_SEARCH_SHEETS.md (this file)
```

## Migration from Old Dropdowns

To replace old StreamBuilder dropdowns with the new search sheets:

### Before (Old StreamBuilder approach):
```dart
StreamBuilder<OtherSettingsResponse?>(
  stream: othersettingsbloc.types.stream,
  builder: (context, snapshot) {
    // Complex dropdown logic...
    return DropdownButtonFormField<OtherSettingsModel>(...);
  },
)
```

### After (New Search Sheet approach):
```dart
TypesSearchSheet(
  label: S.of(context).Type,
  selectedValue: selectedType.value,
  onChanged: (value) {
    selectedType.value = value;
  },
)
```

## Benefits

1. **Simplified Usage** - Single widget instead of complex StreamBuilder logic
2. **Consistent UI** - All search sheets follow the same design pattern
3. **Add New Functionality** - Built-in support for adding new items
4. **Better UX** - Search functionality and loading states
5. **Maintainable** - Centralized logic for each data type
6. **Localized** - Proper support for multiple languages
