import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/main.dart';
import 'package:flutter/material.dart';
import 'package:restart_app/restart_app.dart';

import '../../features/bloc/auth_blok.dart';
import '../../features/models/multi_select_bottom_model.dart';

class BottomSheetAccount extends StatefulWidget {
  final List<ModelSelect> langList;
  final String name;

  const BottomSheetAccount(this.langList, this.name, {super.key});

  @override
  _BottomNavgationBar createState() => _BottomNavgationBar();
}

class _BottomNavgationBar extends State<BottomSheetAccount> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(15), topLeft: Radius.circular(15)),
            color: Color(0xFFF5F6F7),
          ),
          alignment: Alignment.center,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(
                height: 15,
              ),
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFD2D4D6),
                  borderRadius: BorderRadius.circular(30),
                ),
                width: 36,
                height: 4,
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 25, 0, 10),
                child: Text(
                  widget.name,
                  style: const TextStyle(
                      color: Color(
                        0xff191C1F,
                      ),
                      fontWeight: FontWeight.bold,
                      fontSize: 16),
                ),
              ),
              Padding(
                  padding: const EdgeInsets.fromLTRB(15, 15, 15, 20),
                  child: Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.white),
                    child: Padding(
                        padding: const EdgeInsets.fromLTRB(15, 15, 15, 15),
                        child: ListView.builder(
                          itemBuilder: (context, index) {
                            return Column(
                              children: [
                                Container(
                                    width: MediaQuery.of(context).size.width,
                                    child: InkWell(
                                      onTap: () {
                                        setState(() {
                                          for (int i = 0;
                                              i < widget.langList.length;
                                              i++) {
                                            widget.langList[i].check = false;
                                          }
                                          widget.langList[index].check = true;
                                          print(
                                              "widget.langList[index].name  : ${widget.langList[index].name}");
                                          if (widget.langList[index].name ==
                                              "Arabic") {
                                            AuthBloc.isEnglish = false;
                                            setLang();
                                            Restart.restartApp();
                                          } else {
                                            AuthBloc.isEnglish = true;
                                            setLang();
                                            Restart.restartApp();
                                          }
                                        });
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                            top: 10, bottom: 10),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              widget.langList[index].name ==
                                                      "Arabic"
                                                  ? S.of(context).Arabic
                                                  : S.of(context).English,
                                              style: widget.langList[index]
                                                          .check ==
                                                      true
                                                  ? const TextStyle(
                                                      color: Color(
                                                        0xff191C1F,
                                                      ),
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 14)
                                                  : const TextStyle(
                                                      color: Color(
                                                        0xff191C1F,
                                                      ),
                                                      fontSize: 14),
                                            ),
                                            widget.langList[index].check == true
                                                ? const Icon(Icons.check,
                                                    color: Color(0xFFD8B77F))
                                                : const Visibility(
                                                    visible: false,
                                                    child: Text(''),
                                                  )
                                          ],
                                        ),
                                      ),
                                    )),
                                widget.langList.length.isFinite
                                    ? const Visibility(
                                        visible: false,
                                        child: Text(''),
                                      )
                                    : Padding(
                                        padding: const EdgeInsets.fromLTRB(
                                            0, 10, 0, 10),
                                        child: Container(
                                          width:
                                              MediaQuery.of(context).size.width,
                                          height: 1,
                                          color: const Color(0xFFF1F1F1),
                                        ),
                                      ),
                              ],
                            );
                          },
                          itemCount: widget.langList.length,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                        )),
                  ))
            ],
          )),
    );
  }

  void setLang() async {
    print(AuthBloc.isEnglish);
    sharedPreferences!.setString("lang", AuthBloc.isEnglish ? "en" : "ar");
  }
}
