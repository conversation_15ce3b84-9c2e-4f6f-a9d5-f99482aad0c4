import 'package:flutter/material.dart';

import '../../../generated/l10n.dart';
import '../../features/bloc/category_bloc.dart';
import '../../features/models/main_category_model.dart';
import '../utils/resources.dart';

class MultiCategoryDropDown extends StatefulWidget {
  final List<MainCategoryModel> selectedCategories;
  final Function(List<MainCategoryModel>) onChanged;
  final List<int> allowedCatIds;
  final List<MainCategoryModel>? initialSelectedCategories;

  const MultiCategoryDropDown({
    super.key,
    required this.selectedCategories,
    required this.onChanged,
    this.allowedCatIds = const [],
    this.initialSelectedCategories,
  });

  @override
  State<MultiCategoryDropDown> createState() => _MultiCategoryDropDownState();
}

class _MultiCategoryDropDownState extends State<MultiCategoryDropDown> {
  @override
  void initState() {
    super.initState();
    categoryBloc.getMainCategories();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<MainCategoryModel>?>(
      stream: categoryBloc.mainCategoriesSubject.stream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: LinearProgressIndicator());
        }

        var categories = snapshot.data ?? [];

        if (widget.allowedCatIds.isNotEmpty) {
          categories = categories
              .where((element) => widget.allowedCatIds.contains(element.id))
              .toList();
        }

        return Container(
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(width: 0.5, color: Colors.grey),
            color: Colors.white,
          ),
          child: ExpansionTile(
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            title: widget.selectedCategories.isEmpty
                ? Text(
                    S.of(context).ChooseCategories,
                    style: const TextStyle(color: Color(0xffB7B7B7)),
                  )
                : Text(
                    widget.selectedCategories.map((e) => e.name).join(", "),
                    style: const TextStyle(fontSize: 16),
                    overflow: TextOverflow.ellipsis,
                  ),
            children: [
              ListView.builder(
                shrinkWrap: true,
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  final isSelected = widget.selectedCategories
                      .any((element) => element.id == category.id);

                  return CheckboxListTile(
                    activeColor: GlobalColors.primaryColor,
                    title: Text(category.name ?? ""),
                    value: isSelected,
                    onChanged: (bool? value) {
                      List<MainCategoryModel> updatedSelection =
                          List.from(widget.selectedCategories);

                      if (value == true) {
                        if (!isSelected) {
                          updatedSelection.add(category);
                        }
                      } else {
                        updatedSelection.removeWhere(
                            (element) => element.id == category.id);
                      }

                      widget.onChanged(updatedSelection);
                    },
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
