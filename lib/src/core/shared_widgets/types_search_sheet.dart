import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../generated/l10n.dart';
import '../../features/bloc/auth_blok.dart';
import '../../features/bloc/other_settings_bloc.dart';
import '../../features/models/other_settings.dart';
import '../../features/response/other_settings_response.dart';
import '../../features/views/account/other_settings/types/add_type.dart';
import '../utils/resources.dart';

class TypesSearchSheet extends HookWidget {
  final OtherSettingsModel? selectedValue;
  final String? label;
  final void Function(OtherSettingsModel?)? onChanged;
  final bool isRequired;
  final String? category;

  const TypesSearchSheet({
    super.key,
    required this.onChanged,
    required this.label,
    required this.selectedValue,
    this.isRequired = true,
    this.category,
  });

  @override
  Widget build(BuildContext context) {
    final isDropdownOpen = useState(false);

    return Material(
      borderRadius: isDropdownOpen.value
          ? const BorderRadius.only(
              topLeft: Radius.circular(12), topRight: Radius.circular(12))
          : BorderRadius.circular(12),
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _showBottomSheet(context, isDropdownOpen),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  selectedValue != null && selectedValue!.name != null
                      ? selectedValue!.name!
                      : label ?? S.of(context).Type,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              const Icon(Icons.arrow_drop_down),
            ],
          ),
        ),
      ),
    );
  }

  void _showBottomSheet(
      BuildContext context, ValueNotifier<bool> isDropdownOpen) {
    isDropdownOpen.value = true;
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) {
        return _buildBottomSheetContent(context, isDropdownOpen);
      },
    ).whenComplete(() => isDropdownOpen.value = false);
  }

  Widget _buildBottomSheetContent(
      BuildContext context, ValueNotifier<bool> isDropdownOpen) {
    return HookBuilder(builder: (context) {
      final searchController = useTextEditingController();
      final filteredData = useState<List<OtherSettingsModel>>([]);
      final allData = useState<List<OtherSettingsModel>>([]);
      final isLoading = useState(true);

      // Load data
      useEffect(() {
        _loadTypes(allData, filteredData, isLoading);
        return null;
      }, []);

      // Search functionality
      useEffect(() {
        searchController.addListener(() {
          final query = searchController.text.toLowerCase();
          filteredData.value = allData.value.where((item) {
            return item.name?.toLowerCase().contains(query) ?? false;
          }).toList();
        });
        return null;
      }, [searchController]);

      return Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: searchController,
              style: const TextStyle(color: Colors.black),
              decoration: InputDecoration(
                hintText: AuthBloc.isEnglish ? "Search" : "بحث",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          // Add New Button
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  Navigator.pop(context); // Close the bottom sheet first
                  await Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const AddType()),
                  );
                  // Refresh data after adding
                  _loadTypes(allData, filteredData, isLoading);
                },
                icon: const Icon(Icons.add, color: Colors.white),
                label: Text(
                  S.of(context).Addnewtype,
                  style: const TextStyle(color: Colors.white),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: GlobalColors.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : filteredData.value.isEmpty
                    ? Center(child: Text(S.of(context).Therearenoitems))
                    : ListView.builder(
                        itemCount: filteredData.value.length,
                        itemBuilder: (context, index) {
                          final item = filteredData.value[index];
                          final isSelected = selectedValue?.id == item.id;

                          return ListTile(
                            selected: isSelected,
                            title: Text(item.name ?? ''),
                            onTap: () {
                              onChanged?.call(item);
                              Navigator.pop(context);
                            },
                          );
                        },
                      ),
          ),
        ],
      );
    });
  }

  void _loadTypes(
    ValueNotifier<List<OtherSettingsModel>> allData,
    ValueNotifier<List<OtherSettingsModel>> filteredData,
    ValueNotifier<bool> isLoading,
  ) async {
    isLoading.value = true;
    try {
      // Trigger the bloc to fetch types
      othersettingsbloc.gettypes(1, 100, '', category ?? '8');

      // Listen to the stream for the response
      othersettingsbloc.types.stream.listen((OtherSettingsResponse? response) {
        if (response != null && response.code == 1) {
          allData.value = response.results;
          filteredData.value = response.results;
        }
        isLoading.value = false;
      });
    } catch (e) {
      isLoading.value = false;
    }
  }
}
