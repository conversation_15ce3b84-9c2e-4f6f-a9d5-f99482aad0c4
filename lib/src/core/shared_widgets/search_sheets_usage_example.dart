import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../generated/l10n.dart';
import '../../features/bloc/auth_blok.dart';
import '../../features/models/other_settings.dart';
import '../../features/models/price_plan_model.dart';
import '../../features/models/property_status_model.dart';
import 'price_plans_search_sheet.dart';
import 'property_status_search_sheet.dart';
import 'types_search_sheet.dart';

/// Example usage of the new search sheets
/// This file demonstrates how to use the three new search sheet widgets
class SearchSheetsUsageExample extends HookWidget {
  const SearchSheetsUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    // State for selected values
    final selectedType = useState<OtherSettingsModel?>(null);
    final selectedPropertyStatus = useState<PropertyStatusModel?>(null);
    final selectedPricePlan = useState<PricePlanModel?>(null);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Sheets Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Types Search Sheet Example
            Text(
              S.of(context).Type,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TypesSearchSheet(
              label: S.of(context).Type,
              selectedValue: selectedType.value,
              onChanged: (value) {
                selectedType.value = value;
              },
              category: 'Properties', // Optional category filter
            ),
            const SizedBox(height: 24),

            // Property Status Search Sheet Example
            const Text(
              'Property Status',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            PropertyStatusSearchSheet(
              label: 'Property Status',
              selectedValue: selectedPropertyStatus.value,
              onChanged: (value) {
                selectedPropertyStatus.value = value;
              },
            ),
            const SizedBox(height: 24),

            // Price Plans Search Sheet Example
            const Text(
              'Price Plan',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            PricePlansSearchSheet(
              label: 'Price Plan',
              selectedValue: selectedPricePlan.value,
              onChanged: (value) {
                selectedPricePlan.value = value;
              },
            ),
            const SizedBox(height: 24),

            // Display selected values
            const Text(
              'Selected Values:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('Type: ${selectedType.value?.name ?? 'None'}'),
            Text(
                'Property Status: ${_getPropertyStatusName(selectedPropertyStatus.value)}'),
            Text('Price Plan: ${_getPricePlanName(selectedPricePlan.value)}'),
          ],
        ),
      ),
    );
  }

  String _getPropertyStatusName(PropertyStatusModel? status) {
    if (status == null) return 'None';
    return AuthBloc.isEnglish
        ? (status.nameEn ?? status.name ?? '')
        : (status.nameAr ?? status.name ?? '');
  }

  String _getPricePlanName(PricePlanModel? plan) {
    if (plan == null) return 'None';
    return AuthBloc.isEnglish
        ? (plan.nameEn ?? plan.name ?? '')
        : (plan.nameAr ?? plan.name ?? '');
  }
}

/// Alternative usage with the enhanced BaseSearchSheet
/// This shows how to use the BaseSearchSheet with the new "Add New" functionality
class BaseSearchSheetExample extends HookWidget {
  const BaseSearchSheetExample({super.key});

  @override
  Widget build(BuildContext context) {
    final selectedCity = useState<String?>(null);
    final cities = useState<List<String>>(['Dubai', 'Abu Dhabi', 'Sharjah']);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Base Search Sheet Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Example using the enhanced BaseSearchSheet with "Add New" functionality
            // BaseSearchSheet(
            //   label: S.of(context).city,
            //   data: cities.value,
            //   itemModelAsName: (city) => city,
            //   selectedValue: selectedCity.value,
            //   isEng: AuthBloc.isEnglish,
            //   showAddNew: true,
            //   addNewText: AuthBloc.isEnglish ? "Add New City" : "إضافة مدينة جديدة",
            //   onChanged: (value) {
            //     selectedCity.value = value;
            //   },
            //   onAddNew: () async {
            //     // Navigate to add new city page
            //     // await Navigator.push(context, MaterialPageRoute(builder: (context) => AddCityPage()));
            //   },
            //   onDataRefresh: () {
            //     // Refresh the cities list
            //     // cities.value = await fetchCities();
            //   },
            // ),

            Text('Selected City: ${selectedCity.value ?? 'None'}'),
          ],
        ),
      ),
    );
  }
}
