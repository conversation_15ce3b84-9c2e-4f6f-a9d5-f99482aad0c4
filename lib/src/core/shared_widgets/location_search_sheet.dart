import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../generated/l10n.dart';
import '../../features/bloc/other_settings_bloc.dart';
import '../../features/models/other_settings.dart';
import '../../features/views/account/other_settings/areas/add_area.dart';
import '../utils/resources.dart';

class LocationSearchSheet extends HookWidget {
  final OtherSettingsModel? selectedValue;
  final String? label;
  final void Function(OtherSettingsModel?)? onChanged;
  final bool isRequired;

  const LocationSearchSheet({
    super.key,
    required this.onChanged,
    required this.label,
    required this.selectedValue,
    this.isRequired = true,
  });

  @override
  Widget build(BuildContext context) {
    final isDropdownOpen = useState(false);

    return GestureDetector(
      onTap: () {
        isDropdownOpen.value = true;
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) =>
              _buildBottomSheetContent(context, isDropdownOpen),
        );
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.grey),
          color: Colors.white,
        ),
        height: 50,
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                selectedValue?.name ?? label ?? S.of(context).selectLocation,
                style: const TextStyle(fontSize: 16, color: Colors.black),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(
              Icons.keyboard_arrow_down,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSheetContent(
      BuildContext context, ValueNotifier<bool> isDropdownOpen) {
    return HookBuilder(builder: (context) {
      final searchController = useTextEditingController();
      final filteredData = useState<List<OtherSettingsModel>>([]);
      final allData = useState<List<OtherSettingsModel>>([]);
      final isLoading = useState(true);

      // Load data
      useEffect(() {
        _loadLocations(allData, filteredData, isLoading);
        return null;
      }, []);

      // Search functionality
      useEffect(() {
        _searchLocations(searchController.text, allData.value, filteredData);
        return null;
      }, [searchController.text]);

      return Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border:
                    Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label ?? S.of(context).selectLocation,
                    style: const TextStyle(
                        fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Search Field
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: searchController,
                decoration: InputDecoration(
                  hintText: S.of(context).searchRmsArea,
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Colors.grey),
                  ),
                ),
              ),
            ),

            // Content
            Expanded(
              child: isLoading.value
                  ? const Center(child: CircularProgressIndicator())
                  : filteredData.value.isEmpty
                      ? Center(child: Text(S.of(context).Therearenoitems))
                      : ListView.builder(
                          itemCount: filteredData.value.length + 1,
                          itemBuilder: (context, index) {
                            if (index == filteredData.value.length) {
                              // Add new location button
                              return _buildAddNewButton(
                                  context, isDropdownOpen);
                            }

                            final location = filteredData.value[index];
                            return ListTile(
                              title: Text(location.name ?? ''),
                              onTap: () {
                                onChanged?.call(location);
                                Navigator.pop(context);
                              },
                              trailing: selectedValue?.id == location.id
                                  ? const Icon(Icons.check, color: Colors.green)
                                  : null,
                            );
                          },
                        ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildAddNewButton(
      BuildContext context, ValueNotifier<bool> isDropdownOpen) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: ElevatedButton.icon(
        onPressed: () async {
          Navigator.pop(context);
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddArea()),
          );
          if (result != null && result is OtherSettingsModel) {
            onChanged?.call(result);
          }
        },
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          S.of(context).AddnewArea,
          style: const TextStyle(color: Colors.white),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: GlobalColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  void _loadLocations(
    ValueNotifier<List<OtherSettingsModel>> allData,
    ValueNotifier<List<OtherSettingsModel>> filteredData,
    ValueNotifier<bool> isLoading,
  ) async {
    try {
      othersettingsbloc.getLocations();
      othersettingsbloc.locations.stream.listen((response) {
        if (response != null && response.code == 1) {
          allData.value = response.results;
          filteredData.value = response.results;
          isLoading.value = false;
        }
      });
    } catch (e) {
      isLoading.value = false;
    }
  }

  void _searchLocations(
    String query,
    List<OtherSettingsModel> allData,
    ValueNotifier<List<OtherSettingsModel>> filteredData,
  ) {
    if (query.isEmpty) {
      filteredData.value = allData;
    } else {
      filteredData.value = allData
          .where((location) =>
              location.name?.toLowerCase().contains(query.toLowerCase()) ??
              false)
          .toList();
    }
  }
}
