import 'package:flutter/material.dart';

class ADDropDownButtonFormField<T> extends StatelessWidget {
  const ADDropDownButtonFormField(
      {Key? key,
      required this.label,
      required this.onSaved,
      required this.value,
      required this.items})
      : super(key: key);
  final String label;
  final void Function(T?) onSaved;
  final T value;
  final List<DropdownMenuItem<T>> items;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),
        Container(
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(width: 0.5, color: Colors.grey),
              color: Colors.white),
          height: 50,
          child: DropdownButtonFormField<T>(
            isExpanded: true,
            hint: Container(
              padding: const EdgeInsets.only(left: 5, right: 5),
              child: Text(
                label,
                style: const TextStyle(color: Color(0xffB7B7B7)),
              ),
            ),
            onSaved: onSaved,
            value: value,
            iconEnabledColor: Colors.black,
            items: items,
            onChanged: (_) {},
          ),
        ),
      ],
    );
  }
}
