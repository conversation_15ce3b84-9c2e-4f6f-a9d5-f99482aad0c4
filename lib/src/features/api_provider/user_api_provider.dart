import 'package:admin_dubai/src/core/data/network/handle_errors.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:admin_dubai/src/features/views/users_page/widgets/filter_by_country.dart';
import 'package:admin_dubai/src/features/views/users_page/widgets/filter_by_date.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../bloc/auth_blok.dart';
import '../response/user_list_response.dart';

class UserApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<UserListResponse> getUser(String filter, String value,
      [int? page, int? size]) async {
    final filterData =
        filter == 'All Users' || filter.isEmpty ? '' : '&Device_Type=$filter';

    final countryFilter = selectedCountryFilter.value == null
        ? ''
        : '&country_id=${selectedCountryFilter.value!.id}';

    final startDateFilter = startDateFilterController.text.isEmpty
        ? ''
        : '&start_date=${startDateFilterController.text}';

    final endDateFilter = endDateFilterController.text.isEmpty
        ? ''
        : '&end_date=${endDateFilterController.text}';

    final searchFilter = '?name=$value';
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      String url;
      url =
          '${_endpoint}device-type$searchFilter$filterData&all_countries=1$countryFilter$startDateFilter$endDateFilter';
      print(url);

      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return UserListResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return UserListResponse.withError(handleError(error));
    }
  }
}
