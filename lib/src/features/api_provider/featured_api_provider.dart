import 'dart:developer';

import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/featued_video_response.dart';
import '../response/generalResponse.dart';

class FeaturedApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<FeaturedVideoResponse> getFeaturedVideos(
      {int? page,
      int? size = 20,
      int? filteras,
      int? category,
      String? key}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      final filterAs = filteras == -1
          ?
          // '&featuredHome=1&featuredCategory=1'
          ''
          : filteras == 0
              ? '&featuredHome=1'
              : '&featuredCategory=1';
      String url = _endpoint +
          'getfeaturedvideo?page=$page&size=$size' +
          "$filterAs&category=${category ?? ''}&key=${key ?? ''}";
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      return FeaturedVideoResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return FeaturedVideoResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addfeaturedvideotohome({int? id}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}addfeaturedvideotohome?video_id=$id';

      log('url: $url');
      Response response = await _dio.post(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> removefeaturedvideofromhome({int? id}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}removefeaturedvideofromhome?video_id=$id';
      Response response = await _dio.post(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addfeaturedvideotocategory({int? id}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}addfeaturedvideotocategory?video_id=$id';
      Response response = await _dio.post(
        url,
        // data: {"id": id},
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> removefeaturedvideofromcategory({int? id}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}removefeaturedvideofromcategory?video_id=$id';
      Response response = await _dio.post(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
