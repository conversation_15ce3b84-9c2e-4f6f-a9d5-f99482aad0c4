import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';

import '../../core/data/network/handle_errors.dart';
import '../response/auth_response.dart';

class AuthApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<AuthResponse> login(String email, String password, phoneToken) async {
    print("====================== Token==================");
    print(phoneToken);

    try {
      Map<String, String> data = {
        "email": email,
        "password": password,
        "fcmtoken": phoneToken ?? ""
      };

      Response response = await _dio.post(
        '${_endpoint}login',
        data: data,
        options: Options(
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json",
          },
          // "Content-Type": "application/x-www-form-urlencoded",
          followRedirects: false,
          validateStatus: (status) {
            return status! < 500;
          },
        ),
      );
      print("qqqqqqq ${_endpoint}login");
      print(response);

      if (response.data['status'] == false) {
        return AuthResponse.withError(response.data['message']);
      }

      if (response.data['data']['user_type'] != 'ADMIN') {
        return AuthResponse.withError("You are not admin");
      }

      return AuthResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  Future<AuthResponse> sendrequestpassword(String email) async {
    try {
      Map<String, String> data = {"email": email, 'user_type': 'ADMIN'};

      Response response = await _dio.post(
        '${_endpoint}sendrequestpassword',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      print("qqqqqqq");
      print(response);

      return AuthResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  Future<AuthResponse> verfiycode(String email, String code) async {
    try {
      Map<String, String> data = {"email": email, "code": code};

      Response response = await _dio.post(
        '${_endpoint}verfiycodeadmin',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      print("qqqqqqq");
      print(response);

      return AuthResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }

  Future<AuthResponse> resetpassword(String email, String password) async {
    try {
      Map<String, String> data = {"email": email, 'password': password};

      Response response = await _dio.post(
        '${_endpoint}resetpasswordadmin',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      print("qqqqqqq");
      print(response);

      return AuthResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return AuthResponse.withError(handleError(error));
    }
  }
}
