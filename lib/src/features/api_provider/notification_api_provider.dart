import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../response/notifications_response.dart';

class NotificationApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<NotificationsResponse> getNotification(
      {int? page, int? size = 500}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = 'en';
      String url = _endpoint + 'getnotifications' + "?page=$page&size=$size";
      Response response = await _dio.get(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      if (response.data["code"] == 1) {
        return NotificationsResponse.fromJson(response.data);
      } else {
        return NotificationsResponse.withError(response.data["msg"]);
      }
    } catch (error, stacktrace) {
      return NotificationsResponse.withError(handleError(error));
    }
  }
}
