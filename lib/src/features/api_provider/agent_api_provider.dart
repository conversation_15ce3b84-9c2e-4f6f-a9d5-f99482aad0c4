import 'dart:developer';
import 'dart:io';

import 'package:admin_dubai/src/core/data/network/handle_errors.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../bloc/auth_blok.dart';
import '../response/agent_details_response.dart';
import '../response/agent_list_response.dart';
import '../response/generalResponse.dart';

class AgentApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<AgentListResponse> getAgent(String filter,
      [int? page, int? size]) async {
    final category = filter == "holidayHome"
        ? AppConstants.holidayHomesId
        : filter == 'carRent'
            ? AppConstants.carRentalsId
            : filter;
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}getagents?category=$category&page=$page&size=$size';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return AgentListResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return AgentListResponse.withError(handleError(error));
    }
  }

  Future<AgentDetailsResponse> getAgentDetails(String id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}getagentsdetails?id=$id';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return AgentDetailsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return AgentDetailsResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addAgent(
      String companyname,
      String categ,
      String datev,
      String name,
      String nameAr,
      String email,
      String password,
      String mobile,
      String code,
      File? image) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      FormData data = FormData.fromMap({
        "company_name": companyname,
        "category_id": 8,
        // categ == "holidayHome"
        //     ? AppConstants.holidayHomesId
        //     : AppConstants.carRentalsId,
        // "available_until": datev,
        "full_name": name,
        "agent_name[ar]": nameAr,
        "agent_name[en]": name,
        "email": email,
        "phone": mobile,
        "password": password,
        "phone_code": code,
        "user_type": "agent",
      });

      log('DDDDD data: ${{
        "company_name": companyname,
        "category_id": 8,
        // categ == "holidayHome"
        //     ? AppConstants.holidayHomesId
        //     : AppConstants.carRentalsId,
        // "available_until": datev,
        "full_name": name,
        "agent_name[ar]": nameAr,
        "agent_name[en]": name,
        "email": email,
        "phone": mobile,
        "password": password,
        "phone_code": code,
        "user_type": "agent",
      }}');

      if (image != null) {
        data.files.add(MapEntry(
          "image",
          await MultipartFile.fromFile(image!.path),
        ));
      }

      Response response = await _dio.post(
        '${_endpoint}addagent',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      print("qqqqqqq");
      print(response);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        print(response.data["errors"]);
        return GeneralResponse.withError(response.data["errors"].toString());
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError("something not working");
    }
  }

  Future<GeneralResponse> updateAgent(
      String companyname,
      String categ,
      String datev,
      String name,
      String nameAr,
      String email,
      String password,
      String mobile,
      String code,
      String id,
      File? image) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      final catId = categ == "holidayHome" || categ == 'Holiday Homes'
          ? AppConstants.holidayHomesId
          : AppConstants.carRentalsId;
      FormData data = FormData.fromMap({
        "company_name": companyname,
        "category_id": 8,
        // catId,
        "available_until": datev,
        "agent_name[ar]": nameAr,
        "agent_name[en]": name,
        "full_name": name,
        "email": email,
        "id": id,
        "phone": mobile,
        "phone_code": code,
        "password": password
      });

      if (image != null) {
        data.files.add(MapEntry(
          "image",
          await MultipartFile.fromFile(image!.path),
        ));
      }

      print("qwrqwrfqwfwfwqrqwr ${{
        "company_name": companyname,
        "category_id": catId,
        "available_until": datev,
        "full_name": name,
        "agent_name[ar]": nameAr,
        "agent_name[en]": name,
        "email": email,
        "id": id,
        "phone": mobile,
        "phone_code": code,
        "password": password
      }}");

      Response response = await _dio.post(
        '${_endpoint}updateagent',
        // '?id=$id&company_name=$companyname&category_id=$catId&available_until=$datev&full_name=$name&email=$email&phone=$mobile&phone_code=$code&password=$password',
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      print("qqqqqqq");
      print(response);

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteAgent(String id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      // Map<String, String> data = {
      //   "id": id,
      // };

      Response response = await _dio.delete(
        '${_endpoint}deleteagent?id=$id',
        // data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );

      print("qqqqqqq");
      print(response);

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
