import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/discussions_response.dart';
import '../response/generalResponse.dart';

class DiscussionsApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<DiscussionsResponse> getDiscussions(
      {int? id, int? page, int? size = 50, String? categoryName}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      print("====================== $categoryName =========================");
      switch (categoryName) {
        case "HolidayHomes":
          url = _endpoint +
              'getholidayhomediscussions' +
              "?id=$id&page=$page&size=$size";
          break;
        case "CarRents":
          url = _endpoint +
              'getcarrentdiscussions' +
              "?id=$id&page=$page&size=$size";
          break;
        case "Properties":
          url = _endpoint +
              'getluxurydiscussions' +
              "?id=$id&page=$page&size=$size";
          break;
        default:
          url = _endpoint +
              'getmaincategorydiscussions' +
              "?id=$id&page=$page&size=$size";
      }
      Response response = await _dio.get(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      return DiscussionsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return DiscussionsResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteDiscussions({int? id}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}deletediscussion?id=$id';
      Response response = await _dio.delete(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      if (response.statusCode! < 200) {
        return GeneralResponse.withError(response.data);
      }
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> replycommentdiscussion(
      {int? id, String? reply}) async {
    try {
      print(
        {
          "discussion_id": "$id",
          "reply": "$reply",
        },
      );
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}replycommentdiscussion';
      Response? response = await _dio.post(
        url,
        data: {
          "discussion_id": "$id",
          "reply": "$reply",
        },
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      if (response.statusCode! < 200) {
        return GeneralResponse.withError(response.data);
      }
      print("============== data ====================");
      print(response.data ?? "NOOOOOOO DATA");
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deletediscussion({int? id}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}deletediscussion?id=$id';
      Response? response = await _dio.delete(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      if (response.statusCode! < 200) {
        return GeneralResponse.withError(response.data);
      }
      print("============== data ====================");
      print(response.data ?? "NOOOOOOO DATA");
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> sendDiscussions(
      {int? id, String? comment, String? categoryName}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      Map data;
      switch (categoryName) {
        case "HolidayHomes":
          url = '${_endpoint}sendholidayhomediscussion';
          data = {"holiday_home_id": id, "comment": comment};

          break;
        case "CarRents":
          url = '${_endpoint}sendcarrentdiscussion';
          data = {"car_rent_id": id, "comment": comment};
          break;
        case "Properties":
          url = '${_endpoint}sendluxurydiscussion';
          data = {"luxury_id": id, "comment": comment};
          break;
        default:
          url = '${_endpoint}sendmaincategorydiscussion';
          data = {"main_cateogry_id": id, "comment": comment};
      }

      Response response = await _dio.post(
        url,
        data: data,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      return GeneralResponse.fromJson({"code": 1, "msg": "success"});
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editComment(
      {int? id, String? comment, String? categoryName}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      Map data;
      url = '${_endpoint}editdiscussion';
      data = {"id": id, "comment": comment};

      Response response = await _dio.put(
        url,
        data: data,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      return GeneralResponse.fromJson({"code": 1, "msg": "success"});
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
