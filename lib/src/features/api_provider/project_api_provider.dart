import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/generalResponse.dart';

class ProjectApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<GeneralResponse> addProject(FormData data) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("add project");
      print(token);
      _dio.options.headers['Authorization'] = 'Bearer $token';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      print('FormData ${data.fields.map((e) => '${e.key}: ${e.value}')}');

      Response response = await _dio.post('${_endpoint}project/add',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));

      print("add project response");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editProject(FormData data) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("edit project");
      print(token);
      _dio.options.headers['Authorization'] = 'Bearer $token';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      print('FormData ${data.fields.map((e) => '${e.key}: ${e.value}')}');

      Response response = await _dio.post('${_endpoint}project/update',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));

      print("edit project response");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteProject(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("delete project");
      print(token);
      _dio.options.headers['Authorization'] = 'Bearer $token';

      Response response = await _dio.delete('${_endpoint}project/delete?id=$id',
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));

      print("delete project response");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
