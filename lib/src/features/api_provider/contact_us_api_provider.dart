import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/contact_user_response.dart';

class ContactUsApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<ContactUserResponse> getContactUs({int? page, int? size = 20}) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = _endpoint + 'getcontactusrequest' + "?page=$page&size=$size";
      Response response = await _dio.get(
        url,
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
      return ContactUserResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return ContactUserResponse.withError(handleError(error));
    }
  }

  //readMessage
  readMessage(int? id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      Response response = await _dio.post(
        '${_endpoint}contactusread',
        data: {"id": id},
        options: Options(
          headers: {"Content-Type": "application/json"},
          followRedirects: false,
          validateStatus: (status) {
            return status! < 600;
          },
        ),
      );
      print(response);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
    }
  }
}
