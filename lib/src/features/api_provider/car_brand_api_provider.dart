import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/car_brand_deatils.dart';
import '../response/car_brand_response.dart';
import '../response/generalResponse.dart';

class CarBrandApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<CarBrandResponse> getCarBrands([int? page, int? size]) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}getbrands?page=$page&size=$size';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print(response);
      return CarBrandResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return CarBrandResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addCarBrand(
      String? arabicName, String? englishName) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      FormData form = FormData.fromMap({
        'name[en]': englishName,
        "name[ar]": arabicName,
        "category": 6,
      });
      Response response = await _dio.post('${_endpoint}addcarbrand',
          data: form,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print(response);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<CarBrandDeatils> getCarBrandById(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}getbrands?id=$id';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print(response);
      if (response.data["code"] == 1) {
        return CarBrandDeatils.fromJson(response.data);
      } else {
        return CarBrandDeatils.withError(response.data["msg"]);
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return CarBrandDeatils.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editCarBrand(
      int id, String? arabicName, String? englishName) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}updatecarbrand';
      print(url);
      FormData form = FormData.fromMap({
        'name[en]': englishName,
        "name[ar]": arabicName,
        "category": 6,
        "id": id,
      });
      Response response = await _dio.post(
        url,
        data: form,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print(response);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteCarBrand(
    int id,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      Response response = await _dio.delete(
        '${_endpoint}deletecarbrand?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }
}
