import 'dart:developer';

import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/generalResponse.dart';

class PropertyApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<GeneralResponse> addProperty(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer $token';

      // print('Dataa ${data.fields.map((e) => e.value).toList()}');

      Response response = await _dio.post('${_endpoint}video/add',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));

      print("qqqqqqq");
      print(response);

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editProperty(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print('Dataa ${data.fields.map((e) => e.value).toList()}');

      Response response = await _dio.post('${_endpoint}video/edit',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));

      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deletePropertie(
    int id,
  ) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var token = _prefs.getString('token');

    _dio.options.headers['Authorization'] = 'Bearer ${token!}';
    _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
    // Map<String, String> data = {
    //   "id": id.toString(),
    // };

    log('sdasdsad ${_endpoint}video/delete?id=$id');

    Response response = await _dio.delete(
      '${_endpoint}video/delete?id=$id',
      options: Options(
          headers: {"Content-Type": "application/x-www-form-urlencoded"},
          // "Content-Type": "application/x-www-form-urlencoded",
          followRedirects: false,
          validateStatus: (status) {
            return status! < 500;
          }),
    );
    print("daaaaaaaaaaaaaaaaaaaaataaaaaaaaaaaaaaaaaaaaaaa");
    print(response.data);
    if (response.data["code"] == 1) {
      return GeneralResponse.fromJson(response.data);
    } else {
      return GeneralResponse.withError(response.data["msg"]);
    }
  }

  Future<GeneralResponse> editStartEndPrice(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      print(data);
      Response response =
          await _dio.post('${_endpoint}video/editprice', //holidayhome/editprice
              data: data,
              options: Options(
                  headers: {"Content-Type": "application/json"},
                  followRedirects: false,
                  validateStatus: (status) {
                    return status! < 600;
                  }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
