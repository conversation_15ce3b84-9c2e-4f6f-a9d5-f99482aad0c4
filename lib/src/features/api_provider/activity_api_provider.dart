import 'package:admin_dubai/src/core/data/network/handle_errors.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../response/generalResponse.dart';

class ActivityApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();
  // String handleError(dynamic error) {
  //   String errorDescription = "";
  //     switch (error.type) {
  //       case DioErrorType.cancel:
  //         errorDescription = "Request to API server was cancelled";
  //         break;
  //
  //       case DioErrorType.sendTimeout:
  //         errorDescription =
  //             "Connection to API server failed due to internet connection";
  //         break;
  //       case DioErrorType.receiveTimeout:
  //         errorDescription = "Receive timeout in connection with API server";
  //         break;
  //       case DioErrorType.connectionTimeout:
  //         errorDescription = "Connection timeout with API server";
  //         break;
  //       case DioErrorType.badCertificate:
  //         errorDescription = "Bad Certificate";
  //         break;
  //       case DioErrorType.badResponse:
  //         errorDescription = "Bad Response";
  //         break;
  //       case DioErrorType.connectionError:
  //         errorDescription = "Connection Error";
  //         break;
  //       case DioErrorType.unknown:
  //         errorDescription = "Unknown Error";
  //         break;
  //         case DioErrorType.response:
  //     }
  //   } else {
  //     errorDescription = "Unexpected error occured";
  //   }
  //   return errorDescription;
  // }

  Future<GeneralResponse> addActivity(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print(data);
      Response response = await _dio.post('${_endpoint}video/add',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));

      print("qqqqqqq");
      print(response);

      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editActivity(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print(data);
      Response response = await _dio.post('${_endpoint}video/edit',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
