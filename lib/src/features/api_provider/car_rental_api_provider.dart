import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/car_rental_list_response.dart';
import '../response/generalResponse.dart';

class CarRentalApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<CarRentalListResponse> getCarRentals(
      [int? page, int? size, String? key]) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url =
          '${_endpoint}getmaincategory?category=${AppConstants.carRentalsId}&page=$page&size=$size&key=${key ?? ''}';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return CarRentalListResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return CarRentalListResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addCarRental(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print(data);
      Response response = await _dio.post('${_endpoint}video/add',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editCarRental(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print(data);
      Response response = await _dio.post('${_endpoint}video/edit',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteCarRent(
    int id,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      Response response = await _dio.delete(
        '${_endpoint}video/delete?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editStartEndPrice(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      print(data);
      Response response = await _dio.post('${_endpoint}video/editprice',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
