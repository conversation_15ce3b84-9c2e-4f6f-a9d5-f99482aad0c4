import 'dart:developer';

import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/configuration_response.dart';
import '../response/generalResponse.dart';
import '../response/profile_response.dart';

class ProfileApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();
  static String? fullName;

  Future<ProfileResponse> getProfile() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      Response response = await _dio.get(
        '${_endpoint}profile',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      fullName = response.data["data"]["fullname"];
      return ProfileResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return ProfileResponse.withError(handleError(error));
    }
  }

  Future<ProfileResponse> editProfile(
      String name, String email, String phone) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      Map<String, dynamic> data;

      data = {
        "name": name,
        "fullname": name,
        "email": email,
        "id": userid,
        "phone": phone,
        'user_type': 'ADMIN'
      };

      print(data);
      Response response = await _dio.post('${_endpoint}update_profile',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 500;
              }));
      print("qqqqqqq");
      print(response);
      return ProfileResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return ProfileResponse.withError(handleError(error));
    }
  }

  Future<ConfigurationResponse> getConfig() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}configuration';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print(response);
      return ConfigurationResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return ConfigurationResponse.withError(handleError(error));
    }
  }

  Future<ConfigurationResponse> editConfig(Map info) async {
    // FormData form = new FormData();
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print('Dataa $info');
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}updateconfiguration';
      print(url);
      Response response = await _dio.post(
        url,
        data: info,
        options: Options(
            headers: {"Content-Type": "application/json"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );

      log('asdjknsadkjlbn ${response.data}');
      if (response.data["code"] == 1) {
        return ConfigurationResponse.fromJson(response.data);
      } else {
        return ConfigurationResponse.withError("you can't update now");
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return ConfigurationResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> changePassword(
      String old_password, String new_password) async {
    // FormData form = new FormData();
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}change_password';
      print(url);
      Response response = await _dio.post(
        url,
        data: {"old_password": old_password, "new_password": new_password},
        options: Options(
            headers: {"Content-Type": "application/json"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print(response);
      if (response.data["message"] == 'Password updated successfully') {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError("you can't update now");
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }
}
