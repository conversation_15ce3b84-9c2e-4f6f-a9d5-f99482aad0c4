import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/car_year_response.dart';
import '../response/generalResponse.dart';

class CarYearApiProvider {
  // final String _endpoint = "https://www.demo.dubaipage.ae/api";
  final String _endpoint = AppConstants.endpoint; //? Demo
  final Dio _dio = Dio();

  Future<CarYearResponse> getCarYears([int? page, int? size]) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}getyears?page=$page&size=$size';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print(response);
      return CarYearResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return CarYearResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addCarYear(String? year) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      FormData form = FormData.fromMap({
        'year': year,
      });
      Response response = await _dio.post('${_endpoint}addyear',
          data: form,
          options: Options(
              headers: {"Content-Type": "application/json"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print(response);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<CarYearResponse> getCarYearById(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}getyears?id=$id';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print(response);
      if (response.data["code"] == 1) {
        return CarYearResponse.fromJson(response.data);
      } else {
        return CarYearResponse.withError(response.data["msg"]);
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return CarYearResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editCarYear(int id, String? year) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}updateyear?id=$id&year=$year';
      print(url);
      Response response = await _dio.post(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print(response);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteCarYear(
    int id,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      Response response = await _dio.delete(
        '${_endpoint}deleteyear?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }
}
