import 'dart:developer';

import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/album_response.dart';
import '../response/generalResponse.dart';

class HotelApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<GeneralResponse> addHotel(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print(data);
      Response response = await _dio.post('${_endpoint}video/add',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editHotel(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print(data);
      Response response = await _dio.post('${_endpoint}video/edit',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteHotel(
    int id,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      log('asdjasdka ${_endpoint}video/delete?id=$id');
      Response response = await _dio.delete(
        '${_endpoint}video/delete?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> uploadCategoryImages(
      FormData data, String? categoryName) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      print('Dieefff ${data.fields}');
      String url = '${_endpoint}addImageToAlbum';

      Response response = await _dio.post(url,
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // headers: {"Content-Type": "application/x-www-form-urlencoded"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print(response);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addAlbum(FormData data) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      print(data.fields);
      String url;
      url = '${_endpoint}addalbum';
      Response response = await _dio.post(url,
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // headers: {"Content-Type": "application/x-www-form-urlencoded"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print(response);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteAlbum(id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      String url;
      url = _endpoint + 'deletealbum' + "?id=$id";
      print(url);
      print(token);
      Response response = await _dio.delete(url,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // headers: {"Content-Type": "application/x-www-form-urlencoded"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print(response);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> updateAlbum(FormData data) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}updatealbum';
      log('aFassad ${url}\nFields ${data}');
      Response response = await _dio.post(url,
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // headers: {"Content-Type": "application/x-www-form-urlencoded"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print('aFassdasdsadad ${response}');
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<AlbumsResponse> getAlbums(categoryId) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      String url;
      url = _endpoint + 'getalbums' + "?video_id=$categoryId";
      print(url);
      Response response = await _dio.get(url,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // headers: {"Content-Type": "application/x-www-form-urlencoded"},
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print(response);
      if (response.data["code"] == 1) {
        return AlbumsResponse.fromJson(response.data);
      } else {
        return AlbumsResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      print("ERRRRRRRRRRRRRRRRRRORRRRRRRRRRRRR");
      print(error);
      return AlbumsResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> uploadCategoryVideo(FormData data) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    var token = _prefs.getString('token');
    _dio.options.headers['Authorization'] = 'Bearer ${token!}';
    _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
    print(data.fields);
    String url = '${_endpoint}addreel';

    Response response = await _dio.post(url,
        data: data,
        options: Options(
            headers: {"Content-Type": "application/json"},
            // headers: {"Content-Type": "application/x-www-form-urlencoded"},
            followRedirects: false,
            validateStatus: (status) {
              print(status);
              return status! < 600;
            }));
    print(response.data);
    if (response.data["code"] == 1) {
      print("code${response.data["msg"]}");
      return GeneralResponse.fromJson(response.data);
    } else {
      print("EEEEEEEEEEEERRRRRRRORRRRRRRRRR${response.data["msg"]}");
      return GeneralResponse.withError(response.data["msg"]);
    }
  }

  Future<GeneralResponse> deleteImage(
      int id, String? categoryName, int albumId) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      Map<String, String> data = {
        "id": id.toString(),
        "album_id": albumId.toString()
      };
      String url = '${_endpoint}deleteImageFromAlbum?id=$id';

      print(url);
      Response response = await _dio.get(
        url,
        // data: data,
        options: Options(
          headers: {"Content-Type": "application/x-www-form-urlencoded"},
          // "Content-Type": "application/x-www-form-urlencoded",
          followRedirects: false,
          validateStatus: (status) {
            return status! < 500;
          },
        ),
      );
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deleteVideo(int id, String? categoryName) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      Map<String, String> data = {
        "id": id.toString(),
      };
      String url;
      switch (categoryName) {
        case "HolidayHomes":
          url = '${_endpoint}deleteholidayhomereel';
          break;
        case "CarRents":
          url = '${_endpoint}deletecarrentreel';
          break;
        case "Properties":
          url = '${_endpoint}deletepropertyreel';
          break;
        default:
          url = '${_endpoint}deletereel';
      }
      print(url);

      Response response = await _dio.delete(
        url,
        data: data,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      print(response.data);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }
}
