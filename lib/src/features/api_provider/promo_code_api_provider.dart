import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/generalResponse.dart';
import '../response/promo_code_list_response.dart';

class PromoCodeApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<PromoCodeListResponse> getPromoCodes([int? page, int? size]) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      if (page != null) {
        url = '${_endpoint}getpromocodes?page=$page&size=$size';
      } else {
        url = '${_endpoint}getpromocodes';
      }
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqqRRR");
      print(response);
      return PromoCodeListResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return PromoCodeListResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editPromoCode(
      int id,
      String code,
      String discount,
      String finishat,
      int agent,
      String? title,
      String? titleAr,
      String? description,
      String? descriptionAr,
      bool? isPublished,
      int? rmsDiscountId) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}updatepromocode';
      print(url);
      Response response = await _dio.post(
        url,
        data: {
          "id": id,
          "code": code,
          "discount": discount,
          "expiration_date": finishat,
          "agent": agent,
          "title[ar]": titleAr,
          "title[en]": title,
          "description[ar]": descriptionAr,
          "description[en]": description,
          "is_published": isPublished == true ? 1 : 2,
          "rms_promo_id": rmsDiscountId
        },
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print(response);
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addPromoCode(
      String code,
      String discount,
      String finishAt,
      int agentId,
      String? titleEn,
      String? titleAr,
      String? descriptionEn,
      String? descriptionAr,
      bool? isPublished,
      int? rmsDiscountId) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}addpromocode';
      print(url);
      Response response = await _dio.post(url,
          options: Options(
              headers: {"Content-Type": "application/x-www-form-urlencoded"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }),
          data: FormData.fromMap({
            'code': code,
            'discount': discount,
            'expiration_date': finishAt,
            "agent": agentId,
            "title[ar]": titleAr,
            "title[en]": titleEn,
            "description[ar]": descriptionAr,
            "description[en]": descriptionEn,
            "is_published": isPublished == true ? 1 : 2,
            "rms_promo_id": rmsDiscountId
          }));
      print("qqqqqqq ${{
        'code': code,
        'discount': discount,
        'expiration_date': finishAt,
        "agent": agentId,
        "title[ar]": titleAr,
        "title[en]": titleEn,
        "description[ar]": descriptionAr,
        "description[en]": descriptionEn,
        "is_published": isPublished == true ? 1 : 2,
        "rms_promo_id": rmsDiscountId
      }}");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deletePromoCode(
    int id,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';

      Response response = await _dio.delete(
        '${_endpoint}deletepromocode?id=$id',
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 500;
            }),
      );
      if (response.data["code"] == 1) {
        return GeneralResponse.fromJson(response.data);
      } else {
        return GeneralResponse.withError(response.data["msg"]);
      }
    } catch (error, _) {
      return GeneralResponse.withError(handleError(error));
    }
  }
}
