import 'image_model.dart';

class Property {
  Property({
    this.id,
    this.name,
    this.startprice,
    this.images,
  });

  Property.fromJson(dynamic json) {
    id = json['id'];
    name = json['name'];
    startprice = json['startprice'];
    if (json['images'] != null) {
      images = [];
      json['images'].forEach((v) {
        images?.add(ImageModel.fromJson(v));
      });
    }
  }
  int? id;
  String? name;
  int? startprice;
  List<ImageModel>? images;

  Map<String?, dynamic> toJson() {
    final map = <String?, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    map['startprice'] = startprice;
    if (images != null) {
      map['images'] = images?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}
