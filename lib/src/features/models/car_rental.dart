import 'image_model.dart';

class CarRental {
  CarRental({
    this.id,
    this.name,
    this.price,
    this.year,
    this.images,
  });

  int? id;
  String? name;
  int? price;
  int? year;
  List<ImageModel>? images;

  CarRental.fromJson(dynamic json) {
    id = json['id'];
    name = json['name'];
    price = json['price'];
    year = json['year'];
    if (json['images'] != null) {
      images = [];
      json['images'].forEach((v) {
        images?.add(ImageModel.fromJson(v));
      });
    }
  }
}
