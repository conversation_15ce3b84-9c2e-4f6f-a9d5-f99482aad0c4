class AgentDetailsModel {
  int? id;
  String? fullname;
  String? image;
  String? fullnameAr;
  String? category, email, phone, company_name, avaliableuntil, phone_code;

  AgentDetailsModel(
      {this.id,
      this.fullname,
      this.fullnameAr,
      this.category,
      this.email,
      this.avaliableuntil,
      this.company_name,
      this.phone,
      this.phone_code});

  AgentDetailsModel.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    fullname = json['full_name'];
    fullnameAr =
        json['agent_name'] != null ? json['agent_name']['ar'] : fullname;

    category = json['category'] != null ? json['category']['name']['en'] : null;
    email = json['email'];
    phone = json['phone'];
    image = json['image'] ?? '';
    company_name = json['company_name'];
    avaliableuntil = json['available_until'];
    phone_code = json['phone_code'] ?? '+971';
  }
}
