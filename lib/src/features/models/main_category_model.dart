import 'package:equatable/equatable.dart';

class MainCategoryModel extends Equatable {
  final int? id;
  final String? name;
  final String? icon;

  const MainCategoryModel({
    this.id,
    this.name,
    this.icon,
  });

  factory MainCategoryModel.fromJson(Map<String?, dynamic> json) =>
      MainCategoryModel(
        id: json['id'] as int?,
        name:
            // json['name'] != null ? json['name']['en'] : null,
            json['name'].toString().startsWith('{')
                ? json['name']['en']
                : json['name'].toString(),
        icon: json['icon'] as String?,
      );

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = <String?, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['icon'] = icon;
    return data;
  }

  @override
  List<Object?> get props => [id, name, icon];
}
