import 'package:admin_dubai/src/core/utils/app_constants.dart';

class AgentModel {
  int? id;
  String? name;
  String? email;
  String? phone;
  bool? isHolidayHomeAgent;

  AgentModel({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.isHolidayHomeAgent,
  });

  AgentModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['full_name'] == null || json['full_name'] == ''
        ? json['name']
        : json['full_name'];
    email = json['email'];
    phone = json['phone'];
    isHolidayHomeAgent = json['category'] != null &&
            json['category']['id'] == AppConstants.holidayHomesId
        ? true
        : false;
  }
}
