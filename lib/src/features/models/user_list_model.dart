class UserListModel {
  int? id;
  String? fullname;
  String? email;
  String? phone;
  String? phoneCode;
  String? countryName;
  String? code;
  String? createdAt;
  String? deviceType;

  UserListModel(
      {this.id,
      this.fullname,
      this.email,
      this.phone,
      this.phoneCode,
      this.code,
      this.deviceType,
      this.countryName,
      this.createdAt});

  UserListModel.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    fullname = json['name'];
    email = json['email'];
    phone = json['phone'];
    countryName = json['country_name'];
    phoneCode = json['phone_code'];
    code = json['code'];
    createdAt = json['created_at'];
    deviceType = json['device_type'];
  }
}

class CountryModel {
  int? id;
  String? countryName;
  String? countryCode;

  CountryModel({this.id, this.countryName, this.countryCode});

  CountryModel.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    countryName = json['name'];
    countryCode = json['country_code'];
  }
}
