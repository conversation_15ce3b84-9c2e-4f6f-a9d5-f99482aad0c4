class PromoCode {
  PromoCode.fromJson(dynamic json) {
    id = json['id'];
    code = json['code'];
    discount = int.parse(json['discount'].toString());
    title = json['title_en'];
    titleEn = json['title_en'];
    titleAr = json['title_ar'];
    description = json['description'];
    descriptionEn = json['description_en'];
    descriptionAr = json['description_ar'];
    numberofusage = json['number_usage'];
    finishat = json["expiration_date"];
    isPublished = json["is_published"];
    agent = json["agent"] != null ? json["agent"]["id"] : null;
    rmsDiscountId = json["rms_promo_id"];
  }

  int? id;
  String? code;
  String? title;
  String? titleEn;
  String? titleAr;
  String? description;
  String? descriptionEn;
  String? descriptionAr;
  int? discount;
  int? rmsDiscountId;
  int? numberofusage;
  int? isPublished;
  String? finishat;
  int? agent;
}
