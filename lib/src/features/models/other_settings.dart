import 'dart:developer';

import 'package:equatable/equatable.dart';

import 'main_category_model.dart';

class OtherSettingsModel extends Equatable {
  final int? id;
  final String? name;
  final String? image;
  final MainCategoryModel? category;
  final List<MainCategoryModel>? categories;

  const OtherSettingsModel({
    this.id,
    this.name,
    this.image,
    this.category,
    this.categories,
  });

  // OtherSettingsModel.fromJson(Map<String?, dynamic> json) {
  //   id = json['id'];
  //   name = json['name'];
  //   category =
  //       json['category'].runtimeType == String || json['category'] == null
  //           ? null
  //           : MainCategoryModel.fromJson(json['category']);
  // }

  factory OtherSettingsModel.fromJson(Map<String?, dynamic> json) {
    log('asdfasfsaf ${json['image']}');

    return OtherSettingsModel(
      id: json['id'] as int?,
      name: json['name'].toString(),
      image: json['image'].toString(),
      category: const MainCategoryModel(
        id: 8,
        name: 'Properties',
      ),
      categories: json['categories'] != null
          ? (json['categories'] as List)
              .map((e) => MainCategoryModel.fromJson(e))
              .toList()
          : null,
    );
  }

  @override
  List<Object?> get props => [id, name, category, image];
}
