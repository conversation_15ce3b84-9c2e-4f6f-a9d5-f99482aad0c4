import 'dart:developer';

import 'package:admin_dubai/src/features/models/agnet_deails_model.dart';

class RequestListModel {
  int? id;
  String? name;
  String? status;
  String? fullname;
  String? requestedat;
  String? locationName;
  String? usernote;
  int? numberofpeople;
  String? numOfRooms;
  num? total;
  num? finalAmount;
  int? holidayHomeId;

  AgentDetailsModel? agent;

  String? image;
  String? phone;
  String? startdate;
  String? enddate;
  int? days;
  num? subtotal;
  num? vat;
  num? fee;
  num? discount;
  num? driverPrice;
  num? dropOff;

  int? numNormalDays;
  num? normalPrice;
  num? normalDriverPrice;

  List? periods;

  RequestListModel.fromJson(Map<String?, dynamic> json) {
    final videoData = json['video_id'];
    final userData = json['user_id'];
    final amount = json['amount'];

    log('asdkasjdlak ${json['number_room']}');

    id = json['id'];
    name = videoData['name'];

    agent = videoData['agent'] != null
        ? AgentDetailsModel.fromJson(videoData['agent'])
        : null;
    status = json['status'];
    fullname = userData == null ? '' : userData['name'];
    phone = userData == null ? '' : userData['phone'];

    requestedat = json['created_at'].toString().split('T')[0];
    locationName = json['location'] != null ? json['location']['name'] : null;

    numberofpeople = json['number_people'] ?? 0;
    dropOff = json['drop_off'] ?? 0;

    id = json['id'];
    name = videoData['name'];
    status = json['status'];
    locationName = json['location'] != null
        ? json['location']['name']
        : videoData['location'] != null
            ? videoData['location']['name']
            : null;
    image = videoData['images'] != null && videoData['images'].length > 0
        ? videoData['images'][0]['url']
        : null;
    startdate = json['start_date'];
    enddate = json['end_date'];

    vat = amount['vat'] ?? 0;
    discount = amount['discount'] ?? 0;
    fee = amount['fee'] ?? 0;
    total = amount['total_amount'] ?? 0;
    driverPrice = amount['price_driver'] ?? 0;

    days = int.tryParse(amount['num_days']?.toString() ?? '0') ?? 0;

    subtotal = amount['subtotal'] ?? 0;
    finalAmount = amount['final_amount'] ?? 0;

    normalPrice = amount['normal_price'] ?? 0;
    normalDriverPrice = amount['normal_driver_price'] ?? 0;
    numNormalDays = amount['num_not_common_days'] ?? 0;

    periods = json['periods'];

    requestedat = json['created_at'].toString().split('T')[0];

    usernote = json['user_note'];

    numOfRooms = json['number_room'].toString();
  }
}

// class RequestListModel {
//   int? id;
//   String? name;
//   String? fullname, requestedat, location_name, numberofpeople, total;
//   String? status, agentname;
//   RequestListModel(
//       {this.id,
//       this.fullname,
//       this.name,
//       this.location_name,
//       this.numberofpeople,
//       this.requestedat,
//       this.status,
//       this.agentname,
//       this.total});
//
//   RequestListModel.fromJson(Map<String?, dynamic> json) {
//     id = json['id'];
//     fullname = json['user_id'] != null ? json['user_id']['name'] : null;
//
//     name = json['name'];
//     location_name = json['location_name'];
//     numberofpeople = json['numberofpeople'].toString();
//     requestedat = json['requestedat'];
//     total = json['total'] != null ? json['total'].toString() : '0';
//     agentname = json['agentname'];
//
//     status = json['status'];
//   }
// }
