import 'dart:io';
import 'package:equatable/equatable.dart';

class FloorPlanModel extends Equatable {
  final String? nameAr;
  final String? nameEn;
  final File? image;
  final String id; // Unique identifier for each item

  const FloorPlanModel({
    this.nameAr,
    this.nameEn,
    this.image,
    String? id,
  }) : id = id ?? '';

  factory FloorPlanModel.fromJson(Map<String, dynamic> json) {
    return FloorPlanModel(
      nameAr: json['name']?['ar'] as String?,
      nameEn: json['name']?['en'] as String?,
      id: json['id'] as String? ?? DateTime.now().millisecondsSinceEpoch.toString(),
      // Note: image is not loaded from JSON as it's a File object for uploads
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = {
      'ar': nameAr,
      'en': nameEn,
    };
    // Note: ID is not included in JSON as it's only for UI purposes
    return data;
  }

  FloorPlanModel copyWith({
    String? nameAr,
    String? nameEn,
    File? image,
    String? id,
  }) {
    return FloorPlanModel(
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      image: image ?? this.image,
      id: id ?? this.id,
    );
  }

  @override
  List<Object?> get props => [nameAr, nameEn, image, id];
}
