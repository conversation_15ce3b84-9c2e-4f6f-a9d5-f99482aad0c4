import 'package:admin_dubai/src/features/models/main_category_model.dart';

class AgentListModel {
  int? id;
  String? fullname;
  String? image;
  MainCategoryModel? category;

  AgentListModel({this.id, this.fullname, this.image, this.category});

  AgentListModel.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    fullname = json['full_name'] ?? '';
    image = json['image'] ?? '';

    category = json['category'] != null
        ? MainCategoryModel.fromJson(json['category'])
        : null;
  }
}
