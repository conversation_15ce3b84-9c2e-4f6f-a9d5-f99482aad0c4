import 'image_model.dart';

class HolidayHome {
  HolidayHome({
    this.id,
    this.name,
    this.startprice,
    this.endprice,
    this.currency,
    this.endpricecurrency,
    this.images,
  });

  HolidayHome.fromJson(dynamic json) {
    id = json['id'];
    name = json['name'];
    startprice = json['startprice'];
    endprice = json['endprice'];
    currency = json['currency'];
    endpricecurrency = json['endpricecurrency'];
    if (json['images'] != null) {
      images = [];
      json['images'].forEach((v) {
        images?.add(ImageModel.fromJson(v));
      });
    }
  }
  int? id;
  String? name;
  int? startprice;
  int? endprice;
  String? currency;
  String? endpricecurrency;
  List<ImageModel>? images;

  Map<String?, dynamic> toJson() {
    final map = <String?, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    map['startprice'] = startprice;
    map['endprice'] = endprice;
    map['currency'] = currency;
    map['endpricecurrency'] = endpricecurrency;
    if (images != null) {
      map['images'] = images?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}
