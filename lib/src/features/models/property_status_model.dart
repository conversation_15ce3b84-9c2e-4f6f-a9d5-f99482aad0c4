import 'package:equatable/equatable.dart';

class PropertyStatusModel extends Equatable {
  final int? id;
  final String? nameEn;
  final String? nameAr;
  final String? name;

  const PropertyStatusModel({
    this.id,
    this.nameEn,
    this.nameAr,
    this.name,
  });

  factory PropertyStatusModel.fromJson(Map<String?, dynamic> json) {
    return PropertyStatusModel(
      id: json['id'] as int?,
      nameEn: json['name_en'] as String?,
      nameAr: json['name_ar'] as String?,
      name: json['name'] as String?,
    );
  }

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = <String?, dynamic>{};
    data['id'] = id;
    data['name_en'] = nameEn;
    data['name_ar'] = nameAr;
    data['name'] = name;
    return data;
  }

  @override
  List<Object?> get props => [id, nameEn, nameAr, name];
}
