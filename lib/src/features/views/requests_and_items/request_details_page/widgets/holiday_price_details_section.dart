import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/models/request_mode_list.dart';
import 'package:flutter/material.dart';

import '../../../../../core/utils/screen_utils.dart';
import '../../../../bloc/auth_blok.dart';

class HolidayPriceDetails extends StatelessWidget {
  final RequestListModel priceRequest;

  const HolidayPriceDetails({
    super.key,
    required this.priceRequest,
  });

  @override
  Widget build(BuildContext context) {
    final periods = priceRequest.periods ?? [];

    final String currency = S.of(context).AED;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: ScreensHelper.fromWidth(3),vertical: 10),
      child: Column(
        children: [
          ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: periods.length,
              itemBuilder: (context, index) {
                final period = periods[index];

                return Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                          border:
                              Border.all(color: Colors.black12, width: 1.0)),
                      child: Text(
                        period['start_date'] +
                            ' - ' +
                            period['end_date'] +
                            " (${period['num_days']} ${S.of(context).days})",
                        style: const TextStyle(
                            color: Colors.black,
                            fontSize: 13,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Row(
                      textDirection: AuthBloc.isEnglish
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          S.of(context).DayPrice,
                          style: const TextStyle(),
                        ),
                        Text(
                          '${period['price_day']?.toString() ?? '0'} $currency',
                          style: const TextStyle(
                              color: Color(0xff51565B),
                              fontSize: 13,
                              fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ],
                );
              },
              separatorBuilder: (context, index) => const SizedBox(
                    height: 20,
                  )),
          const SizedBox(
            height: 20,
          ),
          _NormalDaysPriceDetails(
            currency: currency,
            priceRequest: priceRequest,
          ),
        ],
      ),
    );
  }
}

class _NormalDaysPriceDetails extends StatelessWidget {
  final String currency;
  final RequestListModel priceRequest;

  const _NormalDaysPriceDetails({
    required this.currency,
    required this.priceRequest,
  });

  @override
  Widget build(BuildContext context) {
    final numNormalDays = priceRequest.numNormalDays ?? 0;
    final normalPrice = priceRequest.normalPrice ?? 0;

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.black12, width: 1.0)),
          child: Text(
            "${S.of(context).NormalDays} ($numNormalDays ${S.of(context).days})",
            style: const TextStyle(
                color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(
          height: 20,
        ),
        Row(
          textDirection:
              AuthBloc.isEnglish ? TextDirection.ltr : TextDirection.rtl,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.of(context).NightPrice,
              style: const TextStyle(),
            ),
            Text(
              '$normalPrice $currency',
              style: const TextStyle(
                  color: Color(0xff51565B),
                  fontSize: 13,
                  fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ],
    );
  }
}
