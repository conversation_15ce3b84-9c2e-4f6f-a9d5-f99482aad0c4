import 'package:admin_dubai/src/features/bloc/auth_blok.dart';
import 'package:admin_dubai/src/features/models/request_mode_list.dart';
import 'package:flutter/material.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/utils/fade_tans_animation.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../../core/utils/screen_utils.dart';
import '../../request_and_items_page/requests_and_items.dart';
import '../../request_details_widget/request_details_widget.dart';

class TitleRequestDetailsWidget extends StatelessWidget {
  final RequestListModel? data;
  final ItemsPageType? status;

  const TitleRequestDetailsWidget({Key? key, this.status, this.data})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: ScreensHelper.fromWidth(2),
          vertical: ScreensHelper.fromWidth(3)),
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(color: GlobalColors.borderGrayColor),
            borderRadius: BorderRadius.circular(ScreensHelper.fromWidth(2))),
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: ScreensHelper.fromWidth(4),
              vertical: ScreensHelper.fromWidth(3)),
          child: Row(
            textDirection:
                AuthBloc.isEnglish ? TextDirection.ltr : TextDirection.rtl,
            children: [
              FadeTransAnimation(
                delayInMillisecond: 700,
                child: Container(
                  decoration: const BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(10))),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: Image.network(
                      "${data!.image}",
                      height: ScreensHelper.fromWidth(35),
                      width: ScreensHelper.fromWidth(25),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              SizedBox(
                width: ScreensHelper.fromWidth(4),
              ),
              FadeTransAnimation(
                delayInMillisecond: 700,
                child: Column(
                  textDirection: AuthBloc.isEnglish
                      ? TextDirection.ltr
                      : TextDirection.rtl,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                        textDirection: AuthBloc.isEnglish
                            ? TextDirection.ltr
                            : TextDirection.rtl,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: GlobalColors.grayBackground,
                              borderRadius: BorderRadius.circular(
                                  ScreensHelper.fromWidth(2)),
                            ),
                            child: Padding(
                              padding:
                                  EdgeInsets.all(ScreensHelper.fromWidth(3)),
                              child: Text(
                                status == ItemsPageType.HOLIDAY
                                    ? S.of(context).HolidayHome
                                    : S.of(context).Carrental,
                                style: TextStyle(
                                    fontSize: 12,
                                    color: GlobalColors.blackTextColor),
                              ),
                            ),
                          ),
                        ]),
                    SizedBox(
                      height: ScreensHelper.fromWidth(2.5),
                    ),
                    Text(
                      "${S.of(context).Requestedon} ${data!.requestedat}",
                      style: TextStyle(
                          fontSize: 12,
                          color: GlobalColors.textGrayColor,
                          fontStyle: FontStyle.italic),
                    ),
                    SizedBox(
                      height: ScreensHelper.fromWidth(2.5),
                    ),
                    Text(
                      data!.name!,
                      style: TextStyle(
                          fontSize: 14, color: GlobalColors.blackTextColor),
                    ),
                    SizedBox(
                      height: ScreensHelper.fromWidth(2.5),
                    ),
                    if (status == ItemsPageType.HOLIDAY)
                      Text(
                        data!.locationName ?? "",
                        style: const TextStyle(
                            fontSize: 13, color: Color(0xFF51565B)),
                      ),
                    SizedBox(
                      height: ScreensHelper.fromWidth(2.5),
                    ),
                    Text(
                      "${S.of(context).Status}: ${data!.status!}",
                      style: const TextStyle(
                          fontSize: 13, color: Color(0xFF51565B)),
                      textDirection: AuthBloc.isEnglish
                          ? TextDirection.ltr
                          : TextDirection.rtl,
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  T getStatusData<T>(RequestStatus status, isText) {
    // bool isText = false;
    // if (T is String) isText = true;
    // print('is text ${T is String}');
    if (status == RequestStatus.ACCEPTED) {
      return !isText ? Colors.green as T : "Accepted" as T;
    }
    if (status == RequestStatus.PENDING) {
      return !isText ? Colors.orange as T : "Pending" as T;
    }
    if (status == RequestStatus.CANCELED) {
      return !isText ? Colors.blueGrey as T : "Canceled" as T;
    }
    if (status == RequestStatus.DENIED) {
      return !isText ? Colors.red as T : "Denied" as T;
    }
    return Colors.transparent as T;
  }
}
