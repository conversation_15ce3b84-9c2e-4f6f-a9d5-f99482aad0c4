import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/bloc/auth_blok.dart';
import 'package:admin_dubai/src/features/models/request_mode_list.dart';
import 'package:admin_dubai/src/features/views/requests_and_items/request_details_page/widgets/holiday_price_details_section.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/utils/fade_tans_animation.dart';
import '../../../../core/utils/resources.dart';
import '../../../../core/utils/screen_utils.dart';
import '../request_and_items_page/requests_and_items.dart';
import '../request_details_widget/request_details_widget.dart';
import 'widgets/car_rent_price_details_section.dart';
import 'widgets/title_request_details_widget.dart';

class RequestDetailsPage extends StatefulWidget {
  const RequestDetailsPage(
      {Key? key, this.requestStatus, this.type, required this.request})
      : super(key: key);
  final RequestListModel request;
  final RequestStatus? requestStatus;
  final ItemsPageType? type;

  @override
  _RequestDetailsPageState createState() => _RequestDetailsPageState();
}

class _RequestDetailsPageState extends State<RequestDetailsPage> {
  @override
  Widget build(BuildContext context) {
    ScreensHelper(context);
    if (ItemsPageType.HOLIDAY == widget.type) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                widget.request.name ?? '',
                style: const TextStyle(fontSize: 17),
              ),
              SizedBox(
                width: ScreensHelper.fromWidth(10),
              )
            ],
          ),
          centerTitle: true,
        ),
        body: Stack(
          children: [
            Positioned.fill(
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        children: [
                          TitleRequestDetailsWidget(
                            status: widget.type,
                            data: widget.request,
                          ),
                          _buildClientSection(
                            widget.request,
                          ),
                          _buildRequestDetailsSection(
                            widget.request,
                          ),
                          _buildPriceSection(
                            widget.request,
                          ),
                          SizedBox(
                            height: ScreensHelper.fromHeight(10),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      return Scaffold(
          appBar: AppBar(
            backgroundColor: GlobalColors.primaryColor,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  widget.request.name ?? '',
                  style: const TextStyle(fontSize: 17),
                ),
                SizedBox(
                  width: ScreensHelper.fromWidth(10),
                )
              ],
            ),
            centerTitle: true,
          ),
          body: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: Column(
                          children: [
                            TitleRequestDetailsWidget(
                              status: widget.type,
                              data: widget.request,
                            ),
                            _buildClientSection(
                              widget.request,
                            ),
                            _buildRequestDetailsSection(
                              widget.request,
                            ),
                            _buildPriceSection(
                              widget.request,
                            ),
                            SizedBox(
                              height: ScreensHelper.fromHeight(10),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          )
          // StreamBuilder<DeatilsResonse>(
          //     stream: requestDeatilsBloc.deatilsStream.stream,
          //     builder: (context, snapshot) {
          //       if (snapshot.hasData) {
          //         return ;
          //       } else {
          //         return const Center(
          //           child: ADCircularProgressIndicator(),
          //         );
          //       }
          //     }),
          );
    }
  }

  _buildClientSection(RequestListModel data) {
    return FadeTransAnimation(
      delayInMillisecond: 900,
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: ScreensHelper.fromWidth(2),
            vertical: ScreensHelper.fromWidth(3)),
        child: Container(
          decoration: BoxDecoration(
              border: Border.all(color: GlobalColors.borderGrayColor),
              borderRadius: BorderRadius.circular(ScreensHelper.fromWidth(2))),
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: ScreensHelper.fromWidth(4),
                vertical: ScreensHelper.fromWidth(3)),
            child: Row(
              textDirection:
                  AuthBloc.isEnglish ? TextDirection.ltr : TextDirection.rtl,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(data.agent?.fullname ?? ''),
                InkWell(
                  onTap: () async {
                    var uri = "tel:${data.agent?.phone}";
                    if (await canLaunchUrl(Uri.parse(uri))) {
                      await launchUrl(Uri.parse(uri));
                    } else {
                      throw 'Could not launch $uri';
                    }
                  },
                  child: Text(
                    S.of(context).Callclient,
                    style: TextStyle(color: GlobalColors.blue),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  _buildRequestDetailsSection(RequestListModel data) {
    return FadeTransAnimation(
      delayInMillisecond: 1000,
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: ScreensHelper.fromWidth(2),
            vertical: ScreensHelper.fromWidth(3)),
        child: Container(
          decoration: BoxDecoration(
              border: Border.all(color: GlobalColors.borderGrayColor),
              borderRadius: BorderRadius.circular(ScreensHelper.fromWidth(2))),
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: ScreensHelper.fromWidth(4),
                vertical: ScreensHelper.fromWidth(3)),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: ScreensHelper.fromWidth(90),
                    decoration: BoxDecoration(
                      color: GlobalColors.grayBackground,
                      borderRadius:
                          BorderRadius.circular(ScreensHelper.fromWidth(2)),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(ScreensHelper.fromWidth(3)),
                      child: Center(
                        child: Text(
                          widget.type == ItemsPageType.HOLIDAY
                              ? S.of(context).HolidayHome
                              : S.of(context).Carrental,
                          style: TextStyle(
                              fontSize: 14,
                              color: GlobalColors.labelBlueTextColor),
                        ),
                      ),
                    ),
                  ),
                  _buildTitleWithValue(
                      S.of(context).Requesteddate, data.requestedat!),
                  if (widget.type == ItemsPageType.HOLIDAY) ...[
                    _buildTitleWithValue(S.of(context).NumberofPeople,
                        data.numberofpeople.toString()),
                  ],
                  if (widget.type == ItemsPageType.CAR) ...[
                    _buildTitleWithValue(S.of(context).PrivateDriver,
                        data.driverPrice.toString()),
                    _buildTitleWithValue(
                        S.of(context).PickupDropOff, data.dropOff.toString()),
                  ],
                  _buildTitleWithValue(S.of(context).Note, data.usernote ?? "")
                ]),
          ),
        ),
      ),
    );
  }

  _buildPriceSection(RequestListModel data) {
    return FadeTransAnimation(
      delayInMillisecond: 1100,
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: ScreensHelper.fromWidth(2),
            vertical: ScreensHelper.fromWidth(3)),
        child: Container(
          decoration: BoxDecoration(
              border: Border.all(color: GlobalColors.borderGrayColor),
              borderRadius: BorderRadius.circular(ScreensHelper.fromWidth(2))),
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: ScreensHelper.fromWidth(4),
                vertical: ScreensHelper.fromWidth(3)),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: ScreensHelper.fromWidth(90),
                    decoration: BoxDecoration(
                      color: GlobalColors.grayBackground,
                      borderRadius:
                          BorderRadius.circular(ScreensHelper.fromWidth(2)),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(ScreensHelper.fromWidth(3)),
                      child: Center(
                        child: Text(
                          S.of(context).PriceBreakdown,
                          style: TextStyle(
                              fontSize: 14,
                              color: GlobalColors.labelBlueTextColor),
                        ),
                      ),
                    ),
                  ),
                  _buildTitleWithValue(
                      S.of(context).NumberOfDays, data.days.toString()),
                  _buildTitleWithValue(
                      S.of(context).startDate, data.startdate ?? ''),
                  _buildTitleWithValue(
                      S.of(context).endDate, data.enddate ?? ''),
                  _buildTitleWithValue(S.of(context).PromoSubtotal,
                      "${data.subtotal} ${S.of(context).AED}"),
                  _buildTitleWithValue(
                      S.of(context).Vat, "${data.vat} ${S.of(context).AED}"),
                  if (widget.type == ItemsPageType.HOLIDAY) ...[
                    _buildTitleWithValue(S.of(context).Tourismfee,
                        "${data.fee} ${S.of(context).AED}"),
                    HolidayPriceDetails(priceRequest: data),
                  ] else ...[
                    CarRentPriceDetails(priceRequest: data),
                  ],
                  _buildTitleWithValue(S.of(context).TotalPrice,
                      "${data.total} ${S.of(context).AED}"),
                  _buildTitleWithValue(S.of(context).AgreedPrice,
                      "${data.finalAmount} ${S.of(context).AED}"),
                ]),
          ),
        ),
      ),
    );
  }

  _buildTitleWithValue(String title, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: ScreensHelper.fromWidth(3),
          vertical: ScreensHelper.fromWidth(2.5)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        textDirection:
            AuthBloc.isEnglish ? TextDirection.ltr : TextDirection.rtl,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style:
                    TextStyle(fontSize: 14, color: GlobalColors.textGrayColor),
              ),
            ],
          ),
          Text(
            value,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(fontSize: 14, color: GlobalColors.textGrayColor),
          )
        ],
      ),
    );
  }
}
