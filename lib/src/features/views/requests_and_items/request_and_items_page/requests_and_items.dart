import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/fade_tans_animation.dart';
import '../../../../core/utils/screen_utils.dart';
import '../../../../core/utils/status_filter_sheet.dart';
import '../../../../core/utils/tab_bar_button.dart';
import '../../../bloc/other_settings_bloc.dart';
import '../../../models/request_mode_list.dart';
import '../../../repository/request_repository.dart';
import '../../../response/request_list_response.dart';
import '../request_details_page/request_details_page.dart';
import '../request_details_widget/request_details_widget.dart';

enum RequestPageType { REQUESTS_PAGE, ITEMS_PAGE }

enum ItemsPageType { HOLIDAY, CAR }

class RequestsAndItemsPage extends StatefulWidget {
  const RequestsAndItemsPage({Key? key, this.itemsType = ItemsPageType.HOLIDAY})
      : super(key: key);
  final ItemsPageType itemsType;

  @override
  _RequestsAndItemsPageState createState() => _RequestsAndItemsPageState();
}

class _RequestsAndItemsPageState extends State<RequestsAndItemsPage> {
  RequestPageType currentPage = RequestPageType.REQUESTS_PAGE;
  final _controller =
      PageController(initialPage: RequestPageType.REQUESTS_PAGE.index);

  bool loadHome = false;

  bool loadCar = false;
  String valvheck = '';
  List<RequestListModel> reqListHome = [];
  List<RequestListModel> reqListCar = [];
  TextEditingController searchController = TextEditingController();
  int pagenumHome = 1;
  int pagesizeHome = 2000;
  int pagenumcar = 1;
  int pagesizecar = 2000;

  String nodata = '0';

  @override
  void initState() {
    super.initState();
    getRequestListHome(category: 'holidayHome', val: '');
    // getRequestLisCAr(category: 'carRent', val: '');
  }

  getRequestListHome(
      {String? category, String? val, String? date, String? agentId}) async {
    reqListHome.clear();
    setState(() {
      loadHome = false;
    });
    final RequestRepository _repository = RequestRepository();
    try {
      RequestListResponse res = await _repository.getRequestList(
          category, val, date ?? '', agentId ?? '', pagenumHome, pagesizeHome);
      print(res);

      if (res.code.toString() == '1') {
        setState(() {
          reqListHome.addAll(res.reqList);
          loadHome = true;
        });
      } else {
        setState(() {
          loadHome = true;
          nodata = '1';

          //error = res.error.toString();
        });
      }
    } catch (e) {
      setState(() {
        //  loadHome=true;
        nodata = '1';
      });
    }
  }

  getRequestLisCAr(
      {String? category, String? val, String? date, String? agentId}) async {
    final RequestRepository _repository = RequestRepository();
    reqListCar.clear();
    setState(() {
      loadCar = false;
    });
    RequestListResponse res = await _repository.getRequestList(
        category, val, date ?? '', agentId ?? '', pagenumcar, pagesizecar);
    print(res);

    if (res.code.toString() == '1') {
      setState(() {
        reqListCar.addAll(res.reqList);
        loadCar = true;
      });
    } else {
      setState(() {
        loadCar = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    ScreensHelper(context);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              S.of(context).Requests,
              style: const TextStyle(fontSize: 17),
            ),
            SizedBox(
              width: ScreensHelper.fromWidth(10),
            )
          ],
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Center(
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: ScreensHelper.fromWidth(2),
                  vertical: ScreensHelper.fromWidth(3)),
              child: Container(
                width: ScreensHelper.fromWidth(93),
                decoration: BoxDecoration(
                    color: Colors.transparent,
                    border: Border.all(color: GlobalColors.primaryGoldColor),
                    borderRadius: BorderRadius.circular(5)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TabBarButton(
                      title: S.of(context).HolidayHomes,
                      isSelected: RequestPageType.REQUESTS_PAGE.index ==
                          currentPage.index,
                      callBack: () {
                        getRequestListHome(category: 'holidayHome', val: '');
                        searchController.clear();

                        _goTo(RequestPageType.REQUESTS_PAGE);
                      },
                    ),
                    TabBarButton(
                      title: S.of(context).CarRental,
                      isSelected:
                          RequestPageType.ITEMS_PAGE.index == currentPage.index,
                      callBack: () {
                        getRequestLisCAr(category: 'carRent', val: '');
                        searchController.clear();
                        _goTo(RequestPageType.ITEMS_PAGE);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          // TabBarRequestsWidget(
          //   firstSelected: true,
          //   secondSelected: false,
          //   endCallBack: () {
          //     _goTo(RequestPageType.ITEMS_PAGE);
          //   },
          //   startCallBack: () {
          //     _goTo(RequestPageType.REQUESTS_PAGE);
          //   },
          // ),
          // Expanded(child: _buildScaffoldBody(currentPage)),
          Expanded(
              child: PageView(
            controller: _controller,
            children: [
              buildRequestPage(ItemsPageType.HOLIDAY),
              buildRequestPage(ItemsPageType.CAR)
            ],
            onPageChanged: (value) {
              setState(() {
                _goTo(RequestPageType.values[value]);
              });
            },
          ))
        ],
      ),
    );
  }

  _goTo(RequestPageType page) {
    if (page == currentPage) return;
    setState(() {
      _controller.jumpToPage(page.index);
      currentPage = page;
    });
  }

  buildRequestPage(ItemsPageType itemsPageType) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: itemsPageType == ItemsPageType.HOLIDAY
          ? loadHome == false
              ? const ADLinearProgressIndicator()
              : nodata == '0'
                  ? Column(
                      children: [
                        _buildSearchBarWithRequests(itemsPageType),
                      ],
                    )
                  : nodatafound('No Features to show')
          : loadCar == false
              ? const ADLinearProgressIndicator()
              : Column(
                  children: [
                    _buildSearchBarWithRequests(itemsPageType),
                  ],
                ),
    );
  }

  // buildItemsPage(ItemsPageType itemsPageType) {
  //   return RequestsPage(
  //     pageType: itemsPageType,
  //     requests:
  //         itemsPageType == ItemsPageType.HOLIDAY ? reqListHome : reqListCar,
  //   );
  // }

  _buildSearchBarWithRequests(ItemsPageType itemsPageType) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: ScreensHelper.fromWidth(3),
      ),
      child: Column(
        children: [
          FadeTransAnimation(
            delayInMillisecond: 400,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      S.of(context).AllRequests,
                      style: const TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ],
                ),
                InkWell(
                  onTap: () {
                    othersettingsbloc.agents.sink.add(null);
                    itemsPageType == ItemsPageType.HOLIDAY
                        ? othersettingsbloc.getAgents('holidayHome', 0, 200)
                        : othersettingsbloc.getAgents('carRent', 0, 200);
                    showModalBottomSheet(
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                                top: Radius.circular(25))),
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: GlobalColors.fillDialogColor,
                        builder: (ctx) {
                          return StatusFilterSheet(
                            applyFilter: (date, agentId) {
                              itemsPageType == ItemsPageType.HOLIDAY
                                  ? getRequestListHome(
                                      category: 'holidayHome',
                                      val: '',
                                      date: date,
                                      agentId: agentId,
                                    )
                                  : getRequestLisCAr(
                                      category: 'carRent',
                                      val: '',
                                      date: date,
                                      agentId: agentId,
                                    );
                            },
                          );
                        });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    child: SvgPicture.asset(
                      DubaiPagesIcons.filterIcon,
                      allowDrawingOutsideViewBox: false,
                    ),
                  ),
                )
              ],
            ),
          ),
          SizedBox(
            height: ScreensHelper.fromWidth(3),
          ),
          FadeTransAnimation(
            delayInMillisecond: 600,
            child: Container(
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                    color: const Color(0xffF1F1F1),
                    borderRadius: BorderRadius.circular(3)),
                child: Container(
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(5)),
                  ),
                  child: TextFormField(
                    textInputAction: TextInputAction.search,
                    onFieldSubmitted: (value) {
                      getRequestListHome(category: 'holidayHome', val: value);
                      getRequestLisCAr(category: 'carRent', val: value);
                    },
                    controller: searchController,
                    decoration: InputDecoration(
                        prefixIcon: const Icon(
                          Icons.search,
                          color: Color(0xff8B959E),
                        ),
                        contentPadding:
                            const EdgeInsets.only(left: 20, right: 20, top: 5),
                        hintText: S.of(context).Search,
                        hintStyle: const TextStyle(
                            color: Color(0xff8B959E), fontSize: 13),
                        border: InputBorder.none),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(
            height: ScreensHelper.fromWidth(3),
          ),
          itemsPageType == ItemsPageType.HOLIDAY
              ? reqListHome.isEmpty
                  ? Center(child: Text(S.of(context).Therearenoitems))
                  : ListView.builder(
                      itemCount: reqListHome.length,
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: () {
                            Navigator.push(context,
                                MaterialPageRoute(builder: (ctx) {
                              return RequestDetailsPage(
                                requestStatus: RequestStatus.ACCEPTED,
                                type: itemsPageType,
                                request: reqListHome[index],
                              );
                            }));
                          },
                          child: RequestDetailsWidget(
                            status: RequestStatus.ACCEPTED,
                            type: itemsPageType,
                            items: reqListHome[index],
                          ),
                        );
                      },
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                    )
              : reqListCar.isEmpty
                  ? Center(child: Text(S.of(context).Therearenoitems))
                  : ListView.builder(
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: () {
                            Navigator.push(context,
                                MaterialPageRoute(builder: (ctx) {
                              return RequestDetailsPage(
                                requestStatus: RequestStatus.ACCEPTED,
                                type: itemsPageType,
                                request: reqListCar[index],
                              );
                            }));
                          },
                          child: RequestDetailsWidget(
                              status: RequestStatus.ACCEPTED,
                              type: itemsPageType,
                              items: reqListCar[index]),
                        );
                      },
                      itemCount: reqListCar.length,
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                    )
        ],
      ),
    );
  }
}
