import 'package:flutter/material.dart';

import '../../../../../generated/l10n.dart';
import '../../../../core/utils/fade_tans_animation.dart';
import '../../../../core/utils/resources.dart';
import '../../../../core/utils/screen_utils.dart';
import '../../../models/request_mode_list.dart';
import '../request_and_items_page/requests_and_items.dart';

class RequestDetailsWidget extends StatefulWidget {
  const RequestDetailsWidget(
      {Key? key, this.status, this.type, required this.items})
      : super(key: key);

  final RequestStatus? status;
  final ItemsPageType? type;
  final RequestListModel items;

  @override
  _RequestDetailsWidgetState createState() => _RequestDetailsWidgetState();
}

class _RequestDetailsWidgetState extends State<RequestDetailsWidget> {
  @override
  Widget build(BuildContext context) {
    return FadeTransAnimation(
      delayInMillisecond: 800,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: ScreensHelper.fromHeight(1)),
        child: Container(
          decoration: BoxDecoration(
              border: Border.all(color: GlobalColors.borderGrayColor),
              borderRadius: BorderRadius.circular(ScreensHelper.fromWidth(2))),
          child: Column(
            children: [
              _buildTitleWithStatus(),
              Divider(
                height: ScreensHelper.fromHeight(0.5),
                color: GlobalColors.borderGrayColor,
              ),
              _buildRequestedDateTapSection(),
              Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: ScreensHelper.fromWidth(3),
                    vertical: ScreensHelper.fromWidth(2.5)),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.items.name!,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
              // _buildTitleWithValue(
              //     S.of(context).Agent, widget.items.agentname!),
              // widget.type == ItemsPageType.CAR
              //     ? const Visibility(
              //         visible: false,
              //         child: Text(''),
              //       )
              //     : _buildTitleWithValue(
              //         S.of(context).Location, widget.items.location_name!),
              // widget.type == ItemsPageType.CAR
              //     ? const Visibility(
              //         child: Text(''),
              //         visible: false,
              //       )
              //     : _buildTitleWithValue(S.of(context).NumberofPeople,
              //         widget.items.numberofpeople!),
              // _buildTitleWithValue(
              //     S.of(context).TotalPrice, widget.items.total!),
            ],
          ),
        ),
      ),
    );
  }

  _buildRequestedDateTapSection() {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: ScreensHelper.fromWidth(3),
          vertical: ScreensHelper.fromWidth(2.5)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: GlobalColors.grayBackground,
                  borderRadius:
                      BorderRadius.circular(ScreensHelper.fromWidth(2)),
                ),
                child: Padding(
                  padding: EdgeInsets.all(ScreensHelper.fromWidth(3)),
                  child: Text(
                    widget.type == ItemsPageType.HOLIDAY
                        ? S.of(context).HolidayHome
                        : S.of(context).CarRental,
                    style: TextStyle(
                        fontSize: 12, color: GlobalColors.textGrayColor),
                  ),
                ),
              ),
              SizedBox(
                width: ScreensHelper.fromWidth(2),
              ),
              /*       Container(
                decoration: BoxDecoration(
                  color: GlobalColors.grayBackground,
                  borderRadius:
                      BorderRadius.circular(ScreensHelper.fromWidth(2)),
                ),
                child: Padding(
                  padding: EdgeInsets.all(ScreensHelper.fromWidth(3)),
                  child: Text(
                    "Villa",
                    style: TextStyle(
                        fontSize: 12, color: GlobalColors.textGrayColor),
                  ),
                ),
              ),*/
            ],
          ),
          Text(
            S.of(context).Requestedon + widget.items.requestedat!,
            style: TextStyle(
                fontSize: 12,
                color: GlobalColors.textGrayColor,
                fontStyle: FontStyle.italic),
          )
        ],
      ),
    );
  }

  _buildTitleWithValue(String title, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: ScreensHelper.fromWidth(3),
          vertical: ScreensHelper.fromWidth(2.5)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style:
                    TextStyle(fontSize: 14, color: GlobalColors.textGrayColor),
              ),
            ],
          ),
          Text(
            value,
            style: TextStyle(fontSize: 14, color: GlobalColors.textGrayColor),
          )
        ],
      ),
    );
  }

  _buildTitleWithStatus() {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: ScreensHelper.fromWidth(3),
          vertical: ScreensHelper.fromWidth(4)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.items.fullname == '' ? '' : widget.items.fullname ?? '',
                style: const TextStyle(fontSize: 16, color: Colors.black),
              ),
            ],
          ),
          Row(
            children: [
              Text("${S.of(context).Status}: "),
              Text(
                widget.items.status.toString() == 'cancled'
                    ? S.of(context).Cancel
                    : widget.items.status.toString() == 'pending'
                        ? S.of(context).Pending
                        : widget.items.status.toString() == 'accepted'
                            ? S.of(context).Accepted
                            : S.of(context).Denied,
                style: TextStyle(
                    color: widget.items.status.toString() == 'cancled'
                        ? Colors.blueGrey
                        : widget.items.status.toString() == 'pending'
                            ? Colors.orange
                            : widget.items.status.toString() == 'accepted'
                                ? Colors.green
                                : Colors.red),
              )
            ],
          )
        ],
      ),
    );
  }

  T getStatusData<T>(RequestStatus status, isText) {
    // bool isText = false;
    // if (T is String) isText = true;
    // print('is text ${T is String}');
    if (status == RequestStatus.ACCEPTED)
      return !isText ? Colors.green as T : "accepted" as T;
    if (status == RequestStatus.PENDING)
      return !isText ? Colors.orange as T : "pending" as T;
    if (status == RequestStatus.CANCELED)
      return !isText ? Colors.blueGrey as T : "cancled" as T;
    if (status == RequestStatus.DENIED)
      return !isText ? Colors.red as T : "denied" as T;
    return Colors.transparent as T;
  }
}

enum RequestStatus { ACCEPTED, PENDING, CANCELED, DENIED }
