import 'dart:developer';
import 'dart:io';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:admin_dubai/src/features/models/category_model.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:get/utils.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:queen_validators/queen_validators.dart';

import '../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/currency_container.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/resources.dart';
import '../../../bloc/other_settings_bloc.dart';
import '../../../models/agent_list_model.dart';
import '../../../models/other_settings.dart';
import '../../../repository/holiday_homs_repository.dart';
import '../../../response/agent_list_response.dart';
import '../../../response/other_settings_response.dart';
import '../../open_map/open_map.dart';
import '../../properties/properties/properties_page.dart';
import 'widgets/add-holiday_home_butotn.dart';

class AddHolidayHome extends StatefulWidget {
  final CategoryModel? holidayHome;

  const AddHolidayHome({super.key, this.holidayHome});

  @override
  _AddHolidayHome createState() => _AddHolidayHome();
}

class _AddHolidayHome extends State<AddHolidayHome> {
  List<String> currency = ['AED', 'USD', 'EUR'];
  bool switchOn1 = false;
  bool switchOn2 = false;
  final _formKey = GlobalKey<FormState>();
  String? _arabicName;
  String? _englishName;
  String? _arabicDescription;
  String? _englishDescription;
  String? _startPrice;
  String? _endPrice;

  // OtherSettingsModel? _features;
  List<int> f = [];

  OtherSettingsModel? _location;
  File? _mainVideo;
  List<File>? _reels;
  List<File>? _images;
  String? _phone;
  String? _website;
  String? _instagram;
  AgentListModel? _agent;
  OtherSettingsModel? _type;
  String? _size;
  String? _numberOfRooms;

  bool _isLoading = false;
  Set<Marker>? _marker;

  @override
  void initState() {
    othersettingsbloc.adminFeatures.sink.add(null);
    othersettingsbloc.locations.sink.add(null);
    othersettingsbloc.agents.sink.add(null);
    othersettingsbloc.types.sink.add(null);

    othersettingsbloc.getLocations();
    othersettingsbloc.getAgents(AppConstants.holidayHomesId.toString(), 0, 200);
    othersettingsbloc.getAdminFeatures(AppConstants.holidayHomesId.toString());
    othersettingsbloc.gettypes(
        0, 20, '', AppConstants.holidayHomesId.toString());

    if (widget.holidayHome != null) {
      final holidayHome = widget.holidayHome!;
      f = holidayHome.features!;

      _arabicName = holidayHome.nameAr;
      _englishName = holidayHome.nameEn;
      _arabicDescription = holidayHome.descriptionAr;
      _englishDescription = holidayHome.descriptionEn;
      _startPrice = holidayHome.startPrice?.toString();
      _endPrice = holidayHome.endPrice?.toString();
      _size = holidayHome.size?.toString();
      _numberOfRooms = widget.holidayHome?.rooms
          .toString()
          .replaceAll('[', '')
          .replaceAll(']', '');
      _phone = holidayHome.phone;
      _website = holidayHome.website;
      _instagram = holidayHome.instagram;
      _agent = holidayHome.agent;
      _marker = {
        Marker(
            markerId: const MarkerId('1'),
            position: LatLng(holidayHome.lat!, holidayHome.lng!))
      };

      switchOn1 = holidayHome.featuredHome == 1 ? true : false;

      switchOn2 = holidayHome.featuredCategory == 1 ? true : false;

      _location = OtherSettingsModel(
          id: holidayHome.location?.id,
          name: holidayHome.location?.name,
          category: holidayHome.category);

      _type = OtherSettingsModel(
          id: holidayHome.typeId,
          name: holidayHome.type,
          category: holidayHome.category);
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(
          widget.holidayHome == null
              ? S.of(context).AddnewProperty
              : S.of(context).Edit,
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).Basicinformation,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).arapro,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 20, right: 20, top: 15, bottom: 15),
                    child: TextFormField(
                      initialValue: _arabicName,
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: S.of(context).arapro,
                          hintStyle: const TextStyle(
                              color: Color(0xffB7B7B7), fontSize: 14)),
                      validator: qValidator([IsRequired()]),
                      onSaved: (value) => _arabicName = value,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).enpro,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _englishName,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).enpro,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _englishName = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).Agents,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<AgentListResponse?>(
                  stream: othersettingsbloc.agents.stream,
                  builder: (BuildContext context,
                      AsyncSnapshot<AgentListResponse?> snapshot) {
                    if (snapshot.hasData &&
                        snapshot.connectionState != ConnectionState.waiting) {
                      if (snapshot.data!.code != 1) {
                        snackbar(snapshot.data?.msg ?? '');
                        return const SizedBox();
                      }
                      if (snapshot.data!.agentList.isEmpty) {
                        return const SizedBox();
                      }
                      return Container(
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(width: 0.5, color: Colors.grey),
                            color: Colors.white),
                        height: 50,
                        child: DropdownButtonFormField<AgentListModel>(
                          isExpanded: true,
                          hint: Container(
                            padding: const EdgeInsets.only(left: 5, right: 5),
                            child: Text(
                              S.of(context).Agents,
                              style: const TextStyle(color: Color(0xffB7B7B7)),
                            ),
                          ),
                          onSaved: (value) => _agent = value,
                          value: snapshot.data!.agentList.firstWhere(
                              (element) => element.id == _agent?.id,
                              orElse: () => snapshot.data!.agentList.first),
                          // value: snapshot.data!.agentList.first,
                          iconEnabledColor: Colors.black,
                          items: snapshot.data!.agentList
                              .map((AgentListModel value) {
                            return DropdownMenuItem<AgentListModel>(
                              value: value,
                              child: Padding(
                                padding: const EdgeInsetsDirectional.only(
                                    start: 10.0),
                                child: Text(
                                  value.fullname!,
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: (_) {},
                        ),
                      );
                    }
                    return const Center(child: CircularProgressIndicator());
                  },
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).desara,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 120,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      child: TextFormField(
                        initialValue: _arabicDescription,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).desara,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _arabicDescription = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).deaen,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 120,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      child: TextFormField(
                        initialValue: _englishDescription,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).deaen,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _englishDescription = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).AskingPrice,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Row(
                    children: [
                      const CurrencyContainer(),
                      Container(
                        width: 1,
                        height: 30,
                        color: Colors.grey,
                      ),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.only(
                              left: 20, right: 20, top: 15, bottom: 15),
                          child: TextFormField(
                            initialValue: _startPrice,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).AskingPrice,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                            validator: qValidator([IsRequired()]),
                            onSaved: (value) => _startPrice = value,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  S.of(context).Features,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                  stream: othersettingsbloc.adminFeatures.stream,
                  builder: (BuildContext context,
                      AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                    if (snapshot.hasData &&
                        snapshot.connectionState != ConnectionState.waiting) {
                      if (snapshot.data!.code != 1) {
                        snackbar(snapshot.data!.msg!);
                        return const SizedBox();
                      }
                      if (snapshot.data!.results.isEmpty) {
                        return Text(S.of(context).Therearenoitems);
                      }
                      return StatefulBuilder(
                        builder: (BuildContext context, setStateF) {
                          return SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                for (var item in snapshot.data!.results) ...[
                                  InkWell(
                                    onTap: () {
                                      if (f.contains(item.id)) {
                                        f.remove(item.id);
                                      } else {
                                        f.add(item.id!);
                                      }
                                      setStateF(() {});
                                    },
                                    child: Card(
                                      color: f.contains(item.id)
                                          ? Colors.blue
                                          : Colors.white,
                                      child: Container(
                                        height: 30,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10),
                                        child: Center(
                                            child: Text(
                                          item.name!,
                                          style: TextStyle(
                                              color: f.contains(item.id)
                                                  ? Colors.white
                                                  : Colors.black),
                                        )),
                                      ),
                                    ),
                                  ),
                                ]
                              ],
                            ),
                          );
                        },
                      );
                    }
                    return const Center(child: CircularProgressIndicator());
                  },
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).Type,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                    stream: othersettingsbloc.types.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.results.isEmpty) {
                          return Text(S.of(context).Therearenoitems);
                        }
                        return Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(width: 0.5, color: Colors.grey),
                                color: Colors.white),
                            height: 50,
                            child: DropdownButtonFormField<OtherSettingsModel>(
                              isExpanded: true,
                              hint: Text(
                                S.of(context).Type,
                                style:
                                    const TextStyle(color: Color(0xffB7B7B7)),
                              ),
                              onSaved: (value) => _type = value,
                              value: snapshot.data!.results.firstWhere(
                                  (element) => element.id == _type?.id,
                                  orElse: () => snapshot.data!.results.first),
                              iconEnabledColor: Colors.black,
                              items: snapshot.data!.results
                                  .map((OtherSettingsModel value) {
                                return DropdownMenuItem<OtherSettingsModel>(
                                  value: value,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.only(
                                        start: 10.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.only(
                                          start: 10.0),
                                      child: Text(
                                        '${value.name}  ',
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (_) {},
                            ));
                      }
                      return const Center(child: CircularProgressIndicator());
                    }),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).numroom,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 20, right: 20, top: 15, bottom: 15),
                    child: TextFormField(
                      initialValue: _numberOfRooms,
                      decoration: const InputDecoration(
                          border: InputBorder.none,
                          hintText: 'eg. 2',
                          hintStyle: TextStyle(
                              color: Color(0xffB7B7B7), fontSize: 14)),
                      keyboardType: TextInputType.text,
                      validator: qValidator([IsRequired()]),
                      onSaved: (value) => _numberOfRooms = value,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).holidaysi,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 20, right: 20, top: 15, bottom: 15),
                    child: TextFormField(
                      initialValue: _size,
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: S.of(context).holidaysi,
                          hintStyle: const TextStyle(
                              color: Color(0xffB7B7B7), fontSize: 14)),
                      keyboardType: TextInputType.number,
                      validator: qValidator([IsRequired()]),
                      onSaved: (value) => _size = value,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).Locationation,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                    stream: othersettingsbloc.locations.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.code != 1) {
                          snackbar(snapshot.data?.msg ?? '');
                          return const SizedBox();
                        }
                        if (snapshot.data!.results.isEmpty) {
                          return Text(S.of(context).Therearenoitems);
                        }
                        return Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(width: 0.5, color: Colors.grey),
                                color: Colors.white),
                            height: 50,
                            child: DropdownButtonFormField<OtherSettingsModel>(
                              isExpanded: true,
                              hint: Text(
                                S.of(context).Location,
                                style:
                                    const TextStyle(color: Color(0xffB7B7B7)),
                              ),
                              onSaved: (value) => _location = value,
                              value: snapshot.data!.results.firstWhereOrNull(
                                (element) => element.id == _location?.id,
                              ),
                              // snapshot.data!.results.first,
                              iconEnabledColor: Colors.black,
                              items: snapshot.data!.results
                                  .map((OtherSettingsModel value) {
                                return DropdownMenuItem<OtherSettingsModel>(
                                  value: value,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.only(
                                        start: 10.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.only(
                                          start: 10.0),
                                      child: Text(
                                        '${value.name}  ',
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (_) {},
                            ));
                      }
                      return const Center(child: CircularProgressIndicator());
                    }),
                const SizedBox(
                  height: 20,
                ),
                Center(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (BuildContext context) => OpenMap(
                            onSave: (markers) {
                              setState(() {
                                _marker = markers;
                              });
                            },
                          ),
                        ),
                      );
                    },
                    child: Text(S.of(context).SetLocationonmap),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).MediaUpload,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).UploadMainVideo,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  title: S.of(context).Tabheretouploadmainvideo,
                  onSingleFileSelected: (video) => _mainVideo = video,
                  isMultiple: false,
                  type: FileType.video,
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 15, bottom: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context).Assignasfeaturedvideoinhomepage,
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 13)),
                          Switch(
                            inactiveTrackColor: Colors.grey[200],
                            activeColor: Colors.grey[200],
                            activeTrackColor: const Color(0xff556477),
                            onChanged: (value) {
                              setState(() {
                                switchOn1 = !switchOn1;
                              });
                            },
                            value: switchOn1,
                          ),
                        ],
                      )),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 15, bottom: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context).Assignasfeaturedvideoincatpage,
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 13)),
                          Switch(
                            inactiveTrackColor: Colors.grey[200],
                            activeColor: Colors.grey[200],
                            activeTrackColor: const Color(0xff556477),
                            onChanged: (value) {
                              setState(() {
                                switchOn2 = !switchOn2;
                              });
                            },
                            value: switchOn2,
                          ),
                        ],
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                const SizedBox(
                  height: 20,
                ),
                const Text(
                  "Upload Cover",
                  style: TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  onSingleFileSelected: (images) => _images = [images],
                  title: S.of(context).Tabheretouploadimage,
                  type: FileType.media,
                  isMultiple: false,
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).ExtraInformation,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).PhoneNumber,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _phone,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).PhoneNumber,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        keyboardType: TextInputType.number,
                        onSaved: (value) => _phone = value,
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).website,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _website,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).website,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _website = value,
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).insta,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _instagram,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).insta,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _instagram = value,
                      )),
                ),
                const SizedBox(
                  height: 10,
                ),
                HookBuilder(builder: (context) {
                  final selectedRMSProperty =
                      useState<int?>(widget.holidayHome?.rmsPropertyId);
                  final selectedRMSCategory =
                      useState<int?>(widget.holidayHome?.rmsCategoryId);
                  final selectedRMSArea =
                      useState<int?>(widget.holidayHome?.rmsAreaId);

                  final unitPriceCtrl = useTextEditingController(
                      text: widget.holidayHome?.startPrice?.toString() ?? '');

                  log('Property: ${selectedRMSProperty.value} - Category: ${selectedRMSCategory.value} - Area: ${selectedRMSArea.value}');
                  return Column(
                    children: [
                      _isLoading
                          ? const ADLinearProgressIndicator()
                          : AddHolidayHomeButton(
                              isEdit: widget.holidayHome != null,
                              onTap: () async {
                                final isEdit = widget.holidayHome != null;

                                if (_formKey.currentState!.validate()) {
                                  if (_images == null && !isEdit) {
                                    ScaffoldMessenger.of(context)
                                        .showSnackBar(SnackBar(
                                      content: Text(S.of(context).selectimg),
                                    ));
                                  }
                                  if (_mainVideo == null && !isEdit) {
                                    ScaffoldMessenger.of(context)
                                        .showSnackBar(SnackBar(
                                      content: Text(S.of(context).selectvid),
                                    ));
                                  } else {
                                    _formKey.currentState!.save();
                                    if (_marker == null ||
                                        _location == null ||
                                        f.isEmpty) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(SnackBar(
                                        content: Text(S.of(context).fill),
                                      ));
                                    } else {
                                      FormData form = FormData.fromMap({
                                        if (isEdit)
                                          'id': widget.holidayHome!.id,
                                        'name[en]': _englishName,
                                        "name[ar]": _arabicName,
                                        "description[en]": _englishDescription,
                                        "description[ar]": _arabicDescription,
                                        "price": _startPrice,
                                        "start_price": _startPrice,
                                        "end_price": _startPrice,
                                        "location_id": _location!.id,
                                        "features[]": f,
                                        "currency": 'AED',
                                        "endpricecurrency": 'AED',
                                        "featuredCategory": switchOn2 ? 1 : 0,
                                        "featuredHome": switchOn1 ? 1 : 0,
                                        "latitude":
                                            _marker!.first.position.latitude,
                                        "longitude":
                                            _marker!.first.position.longitude,
                                        "phone": _phone,
                                        "website": _website,
                                        "instagram": _instagram,
                                        "agent_id": _agent?.id,
                                        "type_id": _type!.id,
                                        "size": _size,
                                        "rooms": _numberOfRooms,
                                        "category_id": 8,
                                        // AppConstants.holidayHomesId,
                                        // 'rms_property_id':
                                        //     selectedRMSProperty.value,
                                        // 'rms_category_id':
                                        //     selectedRMSCategory.value,
                                        // "rms_area_id": selectedRMSArea.value,
                                      });
                                      if (_mainVideo != null) {
                                        form.files.add(MapEntry(
                                          "video",
                                          await MultipartFile.fromFile(
                                              _mainVideo!.path),
                                        ));
                                      }
                                      if (_images != null) {
                                        for (int i = 0;
                                            i < _images!.length;
                                            i++) {
                                          form.files.add(MapEntry(
                                            "image[]",
                                            await MultipartFile.fromFile(
                                                _images![i].path),
                                          ));
                                        }
                                      }

                                      setState(() {
                                        _isLoading = true;
                                      });

                                      var response;

                                      if (isEdit) {
                                        response =
                                            await HolidayHomesRepository()
                                                .editHolidayHome(form);
                                      } else {
                                        response =
                                            await HolidayHomesRepository()
                                                .addHolidayHome(form);
                                      }

                                      if (response.code == -1) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(SnackBar(
                                                backgroundColor: Colors.red,
                                                content: Text(
                                                  response.msg ?? '',
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                )));
                                      } else {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(SnackBar(
                                                backgroundColor: Colors.green,
                                                content:
                                                    Text(S.of(context).added)));
                                        Navigator.pop(context);
                                        Navigator.pushReplacement(
                                            context,
                                            MaterialPageRoute(
                                                builder:
                                                    (BuildContext context) =>
                                                        Properties()));
                                      }
                                      setState(() {
                                        _isLoading = false;
                                      });
                                    }
                                  }
                                } else {
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(SnackBar(
                                          backgroundColor: Colors.red,
                                          content: Text(
                                            S.of(context).fill,
                                            style: const TextStyle(
                                                color: Colors.white),
                                          )));
                                }
                              },
                            ),
                    ],
                  );
                })
              ],
            ),
          ),
        ),
      ),
    ));
  }
}
