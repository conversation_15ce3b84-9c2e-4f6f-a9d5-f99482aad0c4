import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/ad_utils.dart';
import '../../../../core/utils/resources.dart';
import '../../../bloc/category_bloc.dart';
import '../../../response/category_response.dart';
import '../../video_details/video_details.dart';
import '../add_restaurants/add_resturant.dart';
import 'widgets/delete_restaurant.dart';

class Restaurants extends StatefulWidget {
  const Restaurants({Key? key}) : super(key: key);

  @override
  _Restaurants createState() => _Restaurants();
}

class _Restaurants extends State<Restaurants> {
  TextEditingController searchController = TextEditingController();
  String? currentvalue2;
  String? currentvalue3;
  String? currentvalue4;
  List<String> filteredAs = ['Filtered As'];
  List<String> category = ['Restaurants', 'Shopd'];
  List<String> feature = ['Feature', 'not Feature'];
  bool isLoading = false;

  @override
  void initState() {
    categoryBloc.getCategories(AppConstants.restaurantsId.toString(), 0, 200);
    super.initState();
  }

  final TextEditingController startPriceController = TextEditingController();

  Widget _buildCategoryWidget() {
    return StreamBuilder<CategoryResponse?>(
        stream: categoryBloc.subject.stream,
        builder: (context, snapshot) {
          if (snapshot.hasData &&
              snapshot.connectionState != ConnectionState.waiting) {
            if (snapshot.data!.code != 1) {
              snackbar(snapshot.data!.msg!);
              return const SizedBox();
            }
            if (snapshot.data!.category.isEmpty) {
              return Container(
                height: MediaQuery.of(context).size.height * 0.7,
                child: Center(
                  child: Text(
                    S.of(context).Therearenoitems,
                    style: const TextStyle(fontSize: 25),
                  ),
                ),
              );
            }
            return MasonryGridView.count(
              shrinkWrap: true,
              padding:
                  const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
              crossAxisCount: 2,
              mainAxisSpacing: 8,
              itemCount: snapshot.data!.category.length,
              primary: false,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    const SizedBox(
                      width: 5,
                    ),
                    Stack(
                      children: [
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (BuildContext context) => ChewieDemo(
                                  id: snapshot.data!.category[index].id,
                                  image: snapshot.data!.category[index].image,
                                  video: snapshot.data!.category[index].video,
                                  categoryName: "Restaurants",
                                  withDetails: true,
                                ),
                              ),
                            );
                          },
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(5),
                            child: Image.network(
                              '${snapshot.data!.category[index].image}',
                              height: 288,
                              width: MediaQuery.of(context).size.width * 0.45,
                              fit: BoxFit.fill,
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (BuildContext context) => ChewieDemo(
                                    id: snapshot.data!.category[index].id,
                                    categoryName: "Restaurants",
                                    image: snapshot.data!.category[index].image,
                                    video: snapshot.data!.category[index].video,
                                    withDetails: true),
                              ),
                            );
                          },
                          child: Container(
                            height: 288,
                            width: MediaQuery.of(context).size.width * 0.45,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.white.withOpacity(0.5),
                                  Colors.black.withOpacity(0.5),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                            top: 10,
                            left: 10,
                            child: GestureDetector(
                              onTap: () {
                                // startPriceController.text = snapshot
                                //     .data!.category[index].startprice
                                //     .toString();
                                //
                                // editMainCategoryItemPrice(
                                //     snapshot.data!.category[index].id,
                                //     context: context,
                                //     startPriceController: startPriceController,
                                //     navigateWidget: const Restaurants());

                                Navigator.push(context,
                                    MaterialPageRoute(builder: (context) {
                                  return AddRestaurant(
                                    restaurant: snapshot.data!.category[index],
                                  );
                                }));
                              },
                              child: Container(
                                height: 25,
                                width: 25,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(25),
                                    color: const Color(0xff233549)
                                        .withOpacity(0.2)),
                                child: Center(
                                    child: Icon(Icons.edit,
                                        color: Colors.white.withOpacity(0.5),
                                        size: 16)),
                              ),
                            )),
                        Positioned(
                          top: 10,
                          right: 10,
                          child: GestureDetector(
                            onTap: () {
                              deleteRestaurants(
                                  snapshot.data!.category[index].id, index,
                                  context: context, isLoading: isLoading);
                            },
                            child: Container(
                              height: 25,
                              width: 25,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(25),
                                  color:
                                      const Color(0xff233549).withOpacity(0.2)),
                              child: Center(
                                child: Icon(Icons.delete_outline,
                                    color: Colors.white.withOpacity(0.5),
                                    size: 16),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 12,
                          left: 10,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                ADUtils.trimText(
                                    snapshot.data!.category[index].name!, 20),
                                style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 15,
                                    fontWeight: FontWeight.w500),
                              ),
                              const SizedBox(height: 5),
                              Text(
                                '${snapshot.data!.category[index].startPrice ?? ''} ${snapshot.data!.category[index].currency ?? ''}',
                                style: const TextStyle(
                                    color: Colors.white, fontSize: 13),
                              )
                            ],
                          ),
                        )
                      ],
                    )
                  ],
                );
              },
            );
          }
          return const ADCircularProgressIndicator();
        });
  }

  void filter(BuildContext context) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) =>
            StatefulBuilder(builder: (context, StateSetter stateSetter) {
              return Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  child: Container(
                    height: MediaQuery.of(context).size.height * 0.50,
                    decoration: BoxDecoration(
                        color: const Color(0xffF5F6F7),
                        borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(25.0),
                            topRight: Radius.circular(25.0)),
                        border: Border.all(color: Colors.black, width: 1.0)),
                    child: SingleChildScrollView(
                        child: Column(
                      children: [
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                            height: 5,
                            width: 50,
                            color: const Color(0xffD2D4D6)),
                        const SizedBox(
                          height: 20,
                        ),
                        Container(
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    height: 10,
                                    color: Colors.transparent,
                                  ),
                                  Text(
                                    S.of(context).Filter,
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold),
                                  ),
                                  GestureDetector(
                                      onTap: () {
                                        stateSetter(() {
                                          currentvalue2 = null;
                                          currentvalue3 = null;
                                          currentvalue4 = null;
                                        });
                                      },
                                      child: Text(
                                        S.of(context).Reset,
                                        style: const TextStyle(
                                            color: Color(0xff51565B)),
                                      ))
                                ])),
                        Container(
                          padding: const EdgeInsets.all(15),
                          child: Container(
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(10)),
                            child: Container(
                              padding: const EdgeInsets.all(15),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    S.of(context).filteras,
                                    style: const TextStyle(fontSize: 13),
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Container(
                                      child: Container(
                                          // width: 120,
                                          height: 50,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(5),
                                            border: Border.all(
                                                color: Colors.black12,
                                                width: 1.0),
                                          ),
                                          child: DropdownButton<String?>(
                                              isExpanded: true,
                                              hint: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 20, right: 20),
                                                child: Text(
                                                  S.of(context).filteras,
                                                  style: const TextStyle(
                                                      color: Color(0xffB7B7B7)),
                                                ),
                                              ),
                                              value: currentvalue2,
                                              underline: const SizedBox(),
                                              iconEnabledColor: Colors.black,
                                              items: filteredAs
                                                  .map((String? value) {
                                                return DropdownMenuItem<
                                                    String?>(
                                                  value: value,
                                                  child: Container(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              left: 10,
                                                              right: 10),
                                                      child: Text(
                                                        value!,
                                                        style: const TextStyle(
                                                            fontSize: 16),
                                                      )),
                                                );
                                              }).toList(),
                                              onChanged: (value) {
                                                stateSetter(() {
                                                  currentvalue2 = value;
                                                });
                                              }))),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Text(
                                    S.of(context).Category,
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Container(
                                      child: Container(
                                          // width: 120,
                                          height: 50,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(5),
                                            border: Border.all(
                                                color: Colors.black12,
                                                width: 1.0),
                                          ),
                                          child: DropdownButton<String?>(
                                              isExpanded: true,
                                              hint: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 20, right: 20),
                                                child: Text(
                                                  S.of(context).Category,
                                                  style: const TextStyle(
                                                      color: Color(0xffB7B7B7)),
                                                ),
                                              ),
                                              value: currentvalue3,
                                              underline: const SizedBox(),
                                              iconEnabledColor: Colors.black,
                                              items:
                                                  category.map((String? value) {
                                                return DropdownMenuItem<
                                                    String?>(
                                                  value: value,
                                                  child: Container(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              left: 10,
                                                              right: 10),
                                                      child: Text(
                                                        value!,
                                                        style: const TextStyle(
                                                            fontSize: 16),
                                                      )),
                                                );
                                              }).toList(),
                                              onChanged: (value) {
                                                stateSetter(() {
                                                  currentvalue3 = value;
                                                });
                                              }))),
                                  const SizedBox(
                                    height: 20,
                                  ),
                                  Center(
                                      child: Container(
                                          // padding: EdgeInsets.only(right: 20, left: 20),
                                          child: GestureDetector(
                                              onTap: () async {
                                                // resetsuccess(context);
                                                // _submit(rate.toString?(), _comment.text);
                                              },
                                              child: Container(
                                                height: 50,
                                                width: MediaQuery.of(context)
                                                    .size
                                                    .width,
                                                decoration: BoxDecoration(
                                                    color: GlobalColors
                                                        .primaryColor,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10)),
                                                child: Container(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            10),
                                                    child: Center(
                                                        child: Text(
                                                      S.of(context).ApplyFilter,
                                                      style: const TextStyle(
                                                          color: Colors.white),
                                                    ))),
                                              )))),
                                  const SizedBox(height: 20),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
                  ));
            }));
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).Restaurants),
        ),
        body: SingleChildScrollView(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            const SizedBox(
              height: 20,
            ),
            Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      S.of(context).allres,
                      style: const TextStyle(color: Color(0xff51565B)),
                    ),
                    // GestureDetector(
                    //   onTap: () {
                    //     filter(context);
                    //   },
                    //   child: svg2,
                    // )
                  ],
                )),
            const SizedBox(
              height: 10,
            ),
            Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                        color: const Color(0xffF1F1F1),
                        borderRadius: BorderRadius.circular(3)),
                    child: Container(
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                        ),
                        child: TextFormField(
                          controller: searchController,
                          textInputAction: TextInputAction.search,
                          onFieldSubmitted: (value) {
                            categoryBloc.getCategories(
                                AppConstants.restaurantsId.toString(),
                                0,
                                200,
                                value);
                          },
                          decoration: InputDecoration(
                              prefixIcon: const Icon(
                                Icons.search,
                                color: Color(0xff8B959E),
                              ),
                              contentPadding: const EdgeInsets.only(
                                  left: 20, right: 20, top: 5),
                              hintText: S.of(context).Searchplacesandlocations,
                              hintStyle: const TextStyle(
                                  color: Color(0xff8B959E), fontSize: 13),
                              border: InputBorder.none),
                        )))),
            _buildCategoryWidget(),
            const SizedBox(
              height: 20,
            )
          ]),
        ),
        bottomNavigationBar: Container(
          padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
          // padding: EdgeInsets.only(right: 20, left: 20),
          child: GestureDetector(
            onTap: () async {
              Navigator.push(context, MaterialPageRoute(builder: (context) {
                return AddRestaurant();
              }));
            },
            child: Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: GlobalColors.primaryColor,
                  borderRadius: BorderRadius.circular(5)),
              child: Container(
                padding: const EdgeInsets.all(10),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.add, color: Colors.white),
                      Text(
                        S.of(context).addres,
                        style: const TextStyle(color: Colors.white),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
