import 'package:flutter/material.dart';

import '../../../../../core/utils/resources.dart';
import '../add_project_page.dart';

class AddProjectButton extends StatelessWidget {
  const AddProjectButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const AddProjectPage(),
          ),
        );
      },
      backgroundColor: GlobalColors.primaryColor,
      icon: const Icon(Icons.add, color: Colors.white),
      label: const Text(
        'Add Project',
        style: TextStyle(color: Colors.white),
      ),
    );
  }
}
