import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../models/project_plan_model.dart';

class ProjectPlansSection extends HookWidget {
  final ValueNotifier<List<ProjectPlanModel>> projectPlans;

  const ProjectPlansSection({
    super.key,
    required this.projectPlans,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Project Plans List with Drag and Drop
        ReorderableListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: projectPlans.value.length,
          onReorder: (oldIndex, newIndex) {
            if (newIndex > oldIndex) {
              newIndex -= 1;
            }
            final List<ProjectPlanModel> newList =
                List.from(projectPlans.value);
            final ProjectPlanModel item = newList.removeAt(oldIndex);
            newList.insert(newIndex, item);
            projectPlans.value = newList;
          },
          itemBuilder: (context, index) {
            final plan = projectPlans.value[index];
            return _buildProjectPlanItem(context, index, plan);
          },
        ),

        const SizedBox(height: 10),

        // Add New Project Plan Button
        SizedBox(
          height: 45,
          width: MediaQuery.of(context).size.width,
          child: ElevatedButton.icon(
            onPressed: () {
              projectPlans.value = [
                ...projectPlans.value,
                ProjectPlanModel(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                ),
              ];
            },
            icon: const Icon(Icons.add, size: 16, color: Colors.white),
            label: Text(
              S.of(context).addProjectPlan,
              style: const TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: GlobalColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProjectPlanItem(
      BuildContext context, int index, ProjectPlanModel plan) {
    return HookBuilder(
        key: ValueKey(
            plan.id), // Use unique item ID as key for proper reordering
        builder: (context) {
          final bedroomsArController =
              useTextEditingController(text: plan.bedroomsAr ?? '');
          final bedroomsEnController =
              useTextEditingController(text: plan.bedroomsEn ?? '');
          final priceFromController =
              useTextEditingController(text: plan.priceFrom ?? '');
          final priceToController =
              useTextEditingController(text: plan.priceTo ?? '');
          final spaceSizeArController =
              useTextEditingController(text: plan.spaceSizeAr ?? '');
          final spaceSizeEnController =
              useTextEditingController(text: plan.spaceSizeEn ?? '');

          // Update the plan when controllers change
          useEffect(() {
            void updatePlan() {
              final updatedPlan = plan.copyWith(
                bedroomsAr: bedroomsArController.text,
                bedroomsEn: bedroomsEnController.text,
                priceFrom: priceFromController.text,
                priceTo: priceToController.text,
                spaceSizeAr: spaceSizeArController.text,
                spaceSizeEn: spaceSizeEnController.text,
              );
              final newList = List<ProjectPlanModel>.from(projectPlans.value);
              if (index < newList.length) {
                newList[index] = updatedPlan;
                projectPlans.value = newList;
              }
            }

            bedroomsArController.addListener(updatePlan);
            bedroomsEnController.addListener(updatePlan);
            priceFromController.addListener(updatePlan);
            priceToController.addListener(updatePlan);
            spaceSizeArController.addListener(updatePlan);
            spaceSizeEnController.addListener(updatePlan);

            return () {
              bedroomsArController.removeListener(updatePlan);
              bedroomsEnController.removeListener(updatePlan);
              priceFromController.removeListener(updatePlan);
              priceToController.removeListener(updatePlan);
              spaceSizeArController.removeListener(updatePlan);
              spaceSizeEnController.removeListener(updatePlan);
            };
          }, []);

          return Container(
            margin: const EdgeInsets.only(bottom: 15),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with drag handle and delete button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.drag_handle, color: Colors.grey),
                        const SizedBox(width: 8),
                        Text(
                          'Project Plan ${index + 1}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    if (projectPlans.value.length > 1)
                      IconButton(
                        onPressed: () {
                          final newList =
                              List<ProjectPlanModel>.from(projectPlans.value);
                          newList.removeAt(index);
                          projectPlans.value = newList;
                        },
                        icon: const Icon(Icons.delete, color: Colors.red),
                      ),
                  ],
                ),
                const SizedBox(height: 10),

                // Bedrooms Arabic
                Text(S.of(context).bedroomsArabic,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: bedroomsArController,
                  hintText: S.of(context).enterBedroomsArabic,
                  onChanged: (value) {
                    _updateProjectPlan(index, plan.copyWith(bedroomsAr: value));
                  },
                ),
                const SizedBox(height: 10),

                // Bedrooms English
                Text(S.of(context).bedroomsEnglish,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: bedroomsEnController,
                  hintText: S.of(context).enterBedroomsEnglish,
                  onChanged: (value) {
                    _updateProjectPlan(index, plan.copyWith(bedroomsEn: value));
                  },
                ),
                const SizedBox(height: 10),

                // Price From
                Text(S.of(context).priceFrom,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: priceFromController,
                  hintText: S.of(context).enterStartingPrice,
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _updateProjectPlan(index, plan.copyWith(priceFrom: value));
                  },
                ),
                const SizedBox(height: 10),

                // Price To
                Text(S.of(context).priceTo,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: priceToController,
                  hintText: S.of(context).enterEndingPrice,
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _updateProjectPlan(index, plan.copyWith(priceTo: value));
                  },
                ),
                const SizedBox(height: 10),

                // Space Size Arabic
                Text(S.of(context).spaceSizeArabic,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: spaceSizeArController,
                  hintText: S.of(context).enterSpaceSizeArabic,
                  onChanged: (value) {
                    _updateProjectPlan(
                        index, plan.copyWith(spaceSizeAr: value));
                  },
                ),
                const SizedBox(height: 10),

                // Space Size English
                Text(S.of(context).spaceSizeEnglish,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: spaceSizeEnController,
                  hintText: S.of(context).enterSpaceSizeEnglish,
                  onChanged: (value) {
                    _updateProjectPlan(
                        index, plan.copyWith(spaceSizeEn: value));
                  },
                ),
              ],
            ),
          );
        });
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    TextInputType? keyboardType,
    required Function(String) onChanged,
  }) {
    return Container(
      height: 45,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(width: 0.5, color: Colors.grey[300]!),
        color: Colors.white,
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        onChanged: onChanged,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 10),
          hintText: hintText,
          hintStyle: const TextStyle(
            color: Color(0xffB7B7B7),
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  void _updateProjectPlan(int index, ProjectPlanModel updatedPlan) {
    final newList = List<ProjectPlanModel>.from(projectPlans.value);
    newList[index] = updatedPlan;
    projectPlans.value = newList;
  }
}
