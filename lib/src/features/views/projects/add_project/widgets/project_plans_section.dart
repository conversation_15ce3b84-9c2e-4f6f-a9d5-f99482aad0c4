import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../models/project_plan_model.dart';

class ProjectPlansSection extends HookWidget {
  final ValueNotifier<List<ProjectPlanModel>> projectPlans;

  const ProjectPlansSection({
    super.key,
    required this.projectPlans,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Project Plans List with Drag and Drop
        ...projectPlans.value.asMap().entries.map((entry) {
          final index = entry.key;
          final plan = entry.value;
          return _buildDraggableProjectPlanItem(context, index, plan);
        }).toList(),

        const SizedBox(height: 10),

        // Add New Project Plan Button
        SizedBox(
          height: 45,
          width: MediaQuery.of(context).size.width,
          child: ElevatedButton.icon(
            onPressed: () {
              projectPlans.value = [
                ...projectPlans.value,
                ProjectPlanModel(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                ),
              ];
            },
            icon: const Icon(Icons.add, size: 16, color: Colors.white),
            label: Text(
              S.of(context).addProjectPlan,
              style: const TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: GlobalColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDraggableProjectPlanItem(
      BuildContext context, int index, ProjectPlanModel plan) {
    return DragTarget<ProjectPlanModel>(
      builder: (context, candidateData, rejectedData) {
        return Draggable<ProjectPlanModel>(
          data: plan,
          feedback: Material(
            elevation: 4.0,
            child: Container(
              width: MediaQuery.of(context).size.width - 40,
              child: _buildProjectPlanItem(context, index, plan),
            ),
          ),
          childWhenDragging: Container(
            margin: const EdgeInsets.only(bottom: 15),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(10),
              color: Colors.grey[100],
            ),
            child: Container(
              height: 200,
              child: Center(
                child: Text(
                  'Moving ${S.of(context).projectPlan} ${index + 1}...',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
            ),
          ),
          child: _buildProjectPlanItem(context, index, plan),
        );
      },
      onWillAccept: (data) => data != plan,
      onAccept: (draggedPlan) {
        final draggedIndex = projectPlans.value.indexOf(draggedPlan);
        final targetIndex = index;

        if (draggedIndex != -1 && draggedIndex != targetIndex) {
          final newList = List<ProjectPlanModel>.from(projectPlans.value);
          final item = newList.removeAt(draggedIndex);
          newList.insert(targetIndex, item);
          projectPlans.value = newList;
        }
      },
    );
  }

  Widget _buildProjectPlanItem(
      BuildContext context, int index, ProjectPlanModel plan) {
    return HookBuilder(
        key: ValueKey(plan.id), // Use plan ID as key to maintain state
        builder: (context) {
          final bedroomsArController =
              useTextEditingController(text: plan.bedroomsAr ?? '');
          final bedroomsEnController =
              useTextEditingController(text: plan.bedroomsEn ?? '');
          final priceFromController =
              useTextEditingController(text: plan.priceFrom ?? '');
          final priceToController =
              useTextEditingController(text: plan.priceTo ?? '');
          final spaceSizeArController =
              useTextEditingController(text: plan.spaceSizeAr ?? '');
          final spaceSizeEnController =
              useTextEditingController(text: plan.spaceSizeEn ?? '');

          // Sync controllers with plan data when plan changes
          useEffect(() {
            if (bedroomsArController.text != (plan.bedroomsAr ?? '')) {
              bedroomsArController.text = plan.bedroomsAr ?? '';
            }
            if (bedroomsEnController.text != (plan.bedroomsEn ?? '')) {
              bedroomsEnController.text = plan.bedroomsEn ?? '';
            }
            if (priceFromController.text != (plan.priceFrom ?? '')) {
              priceFromController.text = plan.priceFrom ?? '';
            }
            if (priceToController.text != (plan.priceTo ?? '')) {
              priceToController.text = plan.priceTo ?? '';
            }
            if (spaceSizeArController.text != (plan.spaceSizeAr ?? '')) {
              spaceSizeArController.text = plan.spaceSizeAr ?? '';
            }
            if (spaceSizeEnController.text != (plan.spaceSizeEn ?? '')) {
              spaceSizeEnController.text = plan.spaceSizeEn ?? '';
            }
            return null;
          }, [plan.bedroomsAr, plan.bedroomsEn, plan.priceFrom, plan.priceTo, plan.spaceSizeAr, plan.spaceSizeEn]);

          // Update the plan when controllers change
          useEffect(() {
            void updatePlan() {
              final currentIndex = projectPlans.value.indexWhere((p) => p.id == plan.id);
              if (currentIndex != -1) {
                final updatedPlan = plan.copyWith(
                  bedroomsAr: bedroomsArController.text,
                  bedroomsEn: bedroomsEnController.text,
                  priceFrom: priceFromController.text,
                  priceTo: priceToController.text,
                  spaceSizeAr: spaceSizeArController.text,
                  spaceSizeEn: spaceSizeEnController.text,
                );
                final newList = List<ProjectPlanModel>.from(projectPlans.value);
                newList[currentIndex] = updatedPlan;
                projectPlans.value = newList;
              }
            }

            bedroomsArController.addListener(updatePlan);
            bedroomsEnController.addListener(updatePlan);
            priceFromController.addListener(updatePlan);
            priceToController.addListener(updatePlan);
            spaceSizeArController.addListener(updatePlan);
            spaceSizeEnController.addListener(updatePlan);

            return () {
              bedroomsArController.removeListener(updatePlan);
              bedroomsEnController.removeListener(updatePlan);
              priceFromController.removeListener(updatePlan);
              priceToController.removeListener(updatePlan);
              spaceSizeArController.removeListener(updatePlan);
              spaceSizeEnController.removeListener(updatePlan);
            };
          }, [plan.id]);

          return Container(
            margin: const EdgeInsets.only(bottom: 15),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with title and delete button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.drag_handle, color: Colors.grey),
                        const SizedBox(width: 8),
                        Text(
                          '${S.of(context).projectPlan} ${index + 1}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    if (projectPlans.value.length > 1)
                      IconButton(
                        onPressed: () {
                          final newList = List<ProjectPlanModel>.from(projectPlans.value);
                          newList.removeWhere((p) => p.id == plan.id);
                          projectPlans.value = newList;
                        },
                        icon: const Icon(Icons.delete, color: Colors.red),
                      ),
                  ],
                ),
                const SizedBox(height: 10),

                // Bedrooms Arabic
                Text(S.of(context).bedroomsArabic,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: bedroomsArController,
                  hintText: S.of(context).enterBedroomsArabic,
                  onChanged: (value) {
                    _updateProjectPlanById(plan.id, plan.copyWith(bedroomsAr: value));
                  },
                ),
                const SizedBox(height: 10),

                // Bedrooms English
                Text(S.of(context).bedroomsEnglish,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: bedroomsEnController,
                  hintText: S.of(context).enterBedroomsEnglish,
                  onChanged: (value) {
                    _updateProjectPlanById(plan.id, plan.copyWith(bedroomsEn: value));
                  },
                ),
                const SizedBox(height: 10),

                // Price From
                Text(S.of(context).priceFrom,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: priceFromController,
                  hintText: S.of(context).enterStartingPrice,
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _updateProjectPlanById(plan.id, plan.copyWith(priceFrom: value));
                  },
                ),
                const SizedBox(height: 10),

                // Price To
                Text(S.of(context).priceTo,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: priceToController,
                  hintText: S.of(context).enterEndingPrice,
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _updateProjectPlanById(plan.id, plan.copyWith(priceTo: value));
                  },
                ),
                const SizedBox(height: 10),

                // Space Size Arabic
                Text(S.of(context).spaceSizeArabic,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: spaceSizeArController,
                  hintText: S.of(context).enterSpaceSizeArabic,
                  onChanged: (value) {
                    _updateProjectPlanById(plan.id, plan.copyWith(spaceSizeAr: value));
                  },
                ),
                const SizedBox(height: 10),

                // Space Size English
                Text(S.of(context).spaceSizeEnglish,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                _buildTextField(
                  controller: spaceSizeEnController,
                  hintText: S.of(context).enterSpaceSizeEnglish,
                  onChanged: (value) {
                    _updateProjectPlanById(plan.id, plan.copyWith(spaceSizeEn: value));
                  },
                ),
              ],
            ),
          );
        });
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    TextInputType? keyboardType,
    required Function(String) onChanged,
  }) {
    return Container(
      height: 45,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(width: 0.5, color: Colors.grey[300]!),
        color: Colors.white,
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        onChanged: onChanged,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 10),
          hintText: hintText,
          hintStyle: const TextStyle(
            color: Color(0xffB7B7B7),
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  void _updateProjectPlanById(String planId, ProjectPlanModel updatedPlan) {
    final newList = List<ProjectPlanModel>.from(projectPlans.value);
    final index = newList.indexWhere((p) => p.id == planId);
    if (index != -1) {
      newList[index] = updatedPlan;
      projectPlans.value = newList;
    }
  }
}
