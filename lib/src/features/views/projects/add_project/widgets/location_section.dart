import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/location_search_sheet.dart';
import '../../../../models/other_settings.dart';
import '../../../open_map/open_map.dart';

class LocationSection extends HookWidget {
  final ValueNotifier<Set<Marker>?> markers;
  final ValueNotifier<OtherSettingsModel?> selectedLocation;

  const LocationSection({
    super.key,
    required this.markers,
    required this.selectedLocation,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Location Selection
        Text(
          S.of(context).Location,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),
        LocationSearchSheet(
          label: S.of(context).selectLocation,
          selectedValue: selectedLocation.value,
          onChanged: (value) {
            selectedLocation.value = value;
          },
        ),
        const SizedBox(height: 20),

        // Map Selection Button
        Center(
          child: GestureDetector(
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (BuildContext context) => OpenMap(
                    onSave: (selectedMarkers) {
                      markers.value = selectedMarkers;
                    },
                  ),
                ),
              );
            },
            child: Text(S.of(context).SetLocationonmap),
          ),
        ),
        const SizedBox(height: 20),

        // Display selected location with map preview
        if (markers.value != null && markers.value!.isNotEmpty)
          Container(
            height: 200,
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: GoogleMap(
                initialCameraPosition: CameraPosition(
                  target: markers.value!.first.position,
                  zoom: 15.0,
                ),
                markers: markers.value!,
                mapType: MapType.normal,
                zoomControlsEnabled: false,
                scrollGesturesEnabled: false,
                zoomGesturesEnabled: false,
                tiltGesturesEnabled: false,
                rotateGesturesEnabled: false,
                myLocationButtonEnabled: false,
                onMapCreated: (GoogleMapController controller) {
                  // Map created
                },
              ),
            ),
          ),

        // Location confirmation text
        if (markers.value != null && markers.value!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.green[50],
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      S.of(context).locationSelected,
                      style: const TextStyle(color: Colors.green),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
