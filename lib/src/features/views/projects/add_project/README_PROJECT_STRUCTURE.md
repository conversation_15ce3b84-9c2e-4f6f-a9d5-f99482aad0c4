# Add Project Page Documentation

This document describes the structure and implementation of the Add Project page.

## Overview

The Add Project page is organized into modular sections using HookWidget components. Each section handles a specific part of the project creation form, making the code maintainable and reusable.

## File Structure

```
lib/src/features/views/projects/add_project/
├── add_project_page.dart (main page)
├── widgets/
│   ├── basic_information_section.dart
│   ├── location_section.dart
│   ├── project_plans_section.dart
│   ├── floor_plans_section.dart
│   └── featured_settings_section.dart
└── README_PROJECT_STRUCTURE.md (this file)
```

## API Structure

```
lib/src/features/
├── api_provider/
│   └── project_api_provider.dart
├── repository/
│   └── project_repository.dart
└── models/
    ├── project_plan_model.dart
    └── floor_plan_model.dart
```

## Sections Overview

### 1. Basic Information Section
**File**: `widgets/basic_information_section.dart`

**Fields**:
- Arabic Name (`name[ar]`)
- English Name (`name[en]`)
- Arabic Description (`description[ar]`)
- English Description (`description[en]`)
- Type Selection (using TypesSearchSheet)
- Property Status Selection (using PropertyStatusSearchSheet)
- Price Plan Selection (using PricePlansSearchSheet)

### 2. Location Section
**File**: `widgets/location_section.dart`

**Fields**:
- Latitude (`latitude`)
- Longitude (`longitude`)
- Map Selection Button (integrates with OpenMap)

### 3. Project Plans Section
**File**: `widgets/project_plans_section.dart`

**Dynamic Array Fields** (project_plans[0], project_plans[1], etc.):
- Bedrooms Arabic (`project_plans[i][bedrooms][ar]`)
- Bedrooms English (`project_plans[i][bedrooms][en]`)
- Price From (`project_plans[i][price_from]`)
- Price To (`project_plans[i][price_to]`)
- Space Size Arabic (`project_plans[i][space_size][ar]`)
- Space Size English (`project_plans[i][space_size][en]`)

**Features**:
- Add/Remove project plans dynamically
- Validation for each plan
- Auto-indexing for API submission

### 4. Floor Plans Section
**File**: `widgets/floor_plans_section.dart`

**Dynamic Array Fields** (floor_plans[0], floor_plans[1], etc.):
- Name Arabic (`floor_plans[i][name][ar]`)
- Name English (`floor_plans[i][name][en]`)

**Features**:
- Add/Remove floor plans dynamically
- Simple name-based structure

### 5. Featured Settings Section
**File**: `widgets/featured_settings_section.dart`

**Fields**:
- Featured Home (`featuredHome`: 0 or 1)
- Featured Category (`featuredCategory`: 0 or 1)

**Features**:
- Toggle switches for boolean values
- Clear descriptions for each setting

## API Integration

### Endpoint
- **Add Project**: `POST /project/add`
- **Edit Project**: `POST /project/update`
- **Delete Project**: `DELETE /project/delete?id={id}`

### Request Format
The form data matches the API specification:

```
name[ar]: Arabic project name
name[en]: English project name
description[ar]: Arabic description
description[en]: English description
category_id: 8 (Properties category)
price_plan_id: Selected price plan ID
latitude: Location latitude
longitude: Location longitude
featuredHome: 0 or 1
featuredCategory: 0 or 1
type_id: Selected type ID
property_status: Selected property status ID

// Dynamic project plans
project_plans[0][bedrooms][ar]: Bedroom description in Arabic
project_plans[0][bedrooms][en]: Bedroom description in English
project_plans[0][price_from]: Starting price
project_plans[0][price_to]: Ending price
project_plans[0][space_size][ar]: Space size in Arabic
project_plans[0][space_size][en]: Space size in English

// Dynamic floor plans
floor_plans[0][name][ar]: Floor plan name in Arabic
floor_plans[0][name][en]: Floor plan name in English
```

## Key Features

### 1. Modular Architecture
- Each section is a separate widget
- Easy to maintain and test
- Reusable components

### 2. HookWidget Implementation
- Uses Flutter Hooks for state management
- Reactive UI updates
- Clean controller management

### 3. Dynamic Arrays
- Add/remove project plans and floor plans
- Proper indexing for API submission
- Validation for each item

### 4. Integration with Search Sheets
- Uses the new TypesSearchSheet
- Uses PropertyStatusSearchSheet
- Uses PricePlansSearchSheet
- Consistent UI/UX across the app

### 5. Location Integration
- Integrates with existing OpenMap component
- Visual feedback for selected location
- Coordinate validation

### 6. Form Validation
- Required field validation
- Type checking for numeric fields
- User-friendly error messages

## Usage Example

```dart
// Navigate to Add Project page
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const AddProjectPage(),
  ),
);
```

## State Management

The page uses Flutter Hooks for state management:

```dart
// Controllers
final nameArController = useTextEditingController();
final nameEnController = useTextEditingController();

// Selection states
final selectedType = useState<OtherSettingsModel?>(null);
final selectedPropertyStatus = useState<PropertyStatusModel?>(null);

// Dynamic arrays
final projectPlans = useState<List<ProjectPlanModel>>([const ProjectPlanModel()]);
final floorPlans = useState<List<FloorPlanModel>>([const FloorPlanModel()]);

// Boolean states
final featuredHome = useState<bool>(false);
final isLoading = useState<bool>(false);
```

## Benefits

1. **Organized Structure**: Clear separation of concerns
2. **Maintainable Code**: Each section is independent
3. **Reusable Components**: Widgets can be used in edit mode
4. **Consistent UI**: Follows the app's design patterns
5. **Type Safety**: Strong typing with custom models
6. **Dynamic Content**: Support for variable-length arrays
7. **Integration Ready**: Works with existing search sheets and map components
