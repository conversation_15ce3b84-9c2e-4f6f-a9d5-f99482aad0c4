import 'package:flutter/material.dart';

import '../../../../core/utils/resources.dart';

TextEditingController albumNameController = TextEditingController();

void addAlbumimages(BuildContext context, {album, onAdd, isEdit = false}) {
  if (album != null) albumNameController.text = album.year;

  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.40,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  isEdit ? "Edit Album" : "New Album",
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  margin: const EdgeInsets.all(20),
                  child: TextFormField(
                    style: const TextStyle(
                        decorationThickness: 0,
                        decorationColor: Color(0xFF),
                        fontSize: 14),
                    controller: albumNameController,
                    textInputAction: TextInputAction.go,
                    keyboardType: TextInputType.text,
                    decoration: InputDecoration(
                      fillColor: GlobalColors.lightGrey,
                      focusedErrorBorder: InputBorder.none,
                      focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: GlobalColors.darkGrey)),
                      errorBorder: InputBorder.none,
                      enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                        color: GlobalColors.darkGrey,
                      )),
                      errorStyle: const TextStyle(height: 0.8),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                            color: GlobalColors.darkGrey, width: 2.0),
                      ),
                      isDense: true,
                      hintText: 'Enter Album Name',
                    ),
                    onChanged: (value) {},
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                  // padding: EdgeInsets.only(right: 20, left: 20),
                  child: GestureDetector(
                    onTap: onAdd,
                    child: Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          color: GlobalColors.primaryColor,
                          borderRadius: BorderRadius.circular(5)),
                      child: Container(
                          padding: const EdgeInsets.all(10),
                          child: Center(
                              child: Text(
                            isEdit ? "Edit" : "Add",
                            style: const TextStyle(color: Colors.white),
                          ))),
                    ),
                  ),
                ),
              ],
            )),
          )));
}

void deleteAlbum(BuildContext context, album, {required onDelete, onEdit}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                const Center(
                    child: Text(
                  "Delete Album",
                  style: TextStyle(fontWeight: FontWeight.bold),
                )),
                const SizedBox(
                  height: 10,
                ),
                const Text("Are you sure you want to delete this album"),
                const SizedBox(height: 20),
                Container(
                  padding:
                      const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                  // padding: EdgeInsets.only(right: 20, left: 20),
                  child: GestureDetector(
                    onTap: onDelete,
                    child: Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(5)),
                      child: Container(
                          padding: const EdgeInsets.all(10),
                          child: const Center(
                              child: Text(
                            "Yes Delete",
                            style: TextStyle(color: Colors.white),
                          ))),
                    ),
                  ),
                ),
              ],
            )),
          )));
}
