import 'dart:developer';
import 'dart:io';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:dio/dio.dart';
import 'package:expandable/expandable.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/svg.dart';

import '../../../core/shared_widgets/ad_file_picker.dart';
import '../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../core/shared_widgets/snack_bar.dart';
import '../../../core/utils/resources.dart';
import '../../repository/hotels_reposotiry.dart';
import '../../response/album_response.dart';
import 'widgets/actions.dart';

class AlbumsScreen extends StatefulWidget {
  final int id;
  final categoryName;

  const AlbumsScreen({Key? key, required this.id, this.categoryName})
      : super(key: key);
  @override
  _AlbumsScreen createState() => _AlbumsScreen();
}

class _AlbumsScreen extends State<AlbumsScreen> {
  TextEditingController searchController = TextEditingController();
  bool isLoading = false;
  AlbumsResponse? albumsResponse;

  @override
  void initState() {
    super.initState();
    getAlbums();
  }

  getAlbums() async {
    isLoading = true;
    albumsResponse = await HotelsRepository().getAlbums(widget.id);
    if (albumsResponse!.code == 1) {
      isLoading = false;
    }

    setState(() {});
  }

  void longPressAlbum(
      {AlbumsData? album,
      required uploadimages,
      required onDelete,
      required onEdit}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Container(
          decoration: BoxDecoration(
              color: const Color(0xffF5F6F7),
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25.0),
                  topRight: Radius.circular(25.0)),
              border: Border.all(color: Colors.black, width: 1.0)),
          child: SingleChildScrollView(
              child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.add, color: Colors.green),
                title: const Text("Add New Images"),
                onTap: () {
                  Navigator.pop(context);
                  uploadimages();
                },
              ),
              ListTile(
                leading: const Icon(Icons.edit, color: Colors.orange),
                title: const Text("Edit"),
                onTap: () {
                  Navigator.pop(context);

                  addAlbumimages(context, album: album, onAdd: () async {
                    isLoading = true;

                    await HotelsRepository().updateAlbum(FormData.fromMap({
                      'id': album?.id,
                      "name": albumNameController.text,
                      "name[en]": albumNameController.text,
                      "name[ar]": albumNameController.text,
                    }));

                    Navigator.pop(context);

                    getAlbums();
                  }, isEdit: true);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text("Delete"),
                onTap: () {
                  Navigator.pop(context);
                  deleteAlbum(context, album,
                      onDelete: onDelete, onEdit: onEdit);
                },
              ),
            ],
          )),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: const Text("Albums"),
      ),
      body: !isLoading && albumsResponse != null
          ? SingleChildScrollView(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var i = 0; i < albumsResponse!.data!.length; i++)
                      Padding(
                        padding: const EdgeInsets.all(15),
                        child: ExpandablePanel(
                          collapsed: const SizedBox(),
                          header: GestureDetector(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    albumsResponse!.data![i].name!,
                                    style: const TextStyle(fontSize: 18),
                                  ),
                                ),
                                IconButton(
                                  onPressed: () {
                                    longPressAlbum(
                                      album: albumsResponse!.data![i],
                                      onDelete: () async {
                                        isLoading = true;
                                        Navigator.pop(context);
                                        await HotelsRepository().deleteAlbum(
                                            albumsResponse!.data![i].id);
                                        getAlbums();
                                      },
                                      onEdit: () async {
                                        isLoading = true;
                                        Navigator.pop(context);
                                        await HotelsRepository()
                                            .addalbum(FormData.fromMap({
                                          "name": albumNameController.text,
                                          "video_id": widget.id,
                                          "type": widget.categoryName
                                              .toString()
                                              .toLowerCase(),
                                        }));
                                        getAlbums();
                                      },
                                      uploadimages: () => uploadimages(
                                          context, albumsResponse!.data![i]),
                                    );
                                  },
                                  icon: SvgPicture.asset(
                                    'assets/Group 6507.svg',
                                    color: GlobalColors.primaryColor,
                                    height: 17,
                                    width: 10,
                                  ),
                                )
                              ],
                            ),
                          ),
                          expanded: Builder(
                            builder: (context) {
                              return StatefulBuilder(
                                builder: (BuildContext context, setStateDots) {
                                  return Column(
                                    children: [
                                      _buildCategoryWidget(
                                          albumsResponse!.data![i].photos,
                                          albumsResponse!.data![i].id)
                                    ],
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ),
                  ]),
            )
          : const Center(
              child: CircularProgressIndicator(),
            ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
        // padding: EdgeInsets.only(right: 20, left: 20),
        child: GestureDetector(
          onTap: () async {
            addAlbumimages(
              context,
              onAdd: () async {
                isLoading = true;
                Navigator.pop(context);
                await HotelsRepository().addalbum(FormData.fromMap({
                  "name": albumNameController.text,
                  "video_id": widget.id,
                  "type": widget.categoryName.toString().toLowerCase()
                }));
                getAlbums();
              },
            );
          },
          child: Container(
            height: 50,
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
                color: GlobalColors.primaryColor,
                borderRadius: BorderRadius.circular(5)),
            child: Container(
                padding: const EdgeInsets.all(10),
                child: const Center(
                    child: Text(
                  "Add New Album",
                  style: TextStyle(color: Colors.white),
                ))),
          ),
        ),
      ),
    ));
  }

  Widget _buildCategoryWidget(List<Photos>? images, albumId) {
    if (images == null) return const SizedBox.shrink();
    return MasonryGridView.count(
      shrinkWrap: true,
      // padding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 5.0),
      crossAxisCount: 2,
      mainAxisSpacing: 8,
      itemCount: images.length,
      primary: false,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.all(5),
          child: Column(
            children: [
              const SizedBox(width: 5),
              Stack(children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(5),
                  child: Image.network(
                    '${images[index].image}',
                    height: 288,
                    width: MediaQuery.of(context).size.width * 0.45,
                    fit: BoxFit.fill,
                  ),
                ),
                Container(
                  height: 288,
                  width: MediaQuery.of(context).size.width * 0.45,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.white.withOpacity(0.5),
                            Colors.black.withOpacity(0.5),
                          ])),
                ),
                Positioned(
                  top: 10,
                  right: 10,
                  child: InkWell(
                    onTap: () {
                      deleteImage(images[index].id, index, albumId);
                    },
                    child: Container(
                      height: 25,
                      width: 25,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(25),
                          color: const Color(0xff233549).withOpacity(0.2)),
                      child: const Center(
                          child: Icon(Icons.delete_outline,
                              color: Colors.white, size: 16)),
                    ),
                  ),
                ),
                // Positioned(
                //   bottom: 12,
                //   left: 10,
                //   child: Column(
                //     children: [
                //       Text(
                //         images[index].createdat!,
                //         style:
                //             const TextStyle(color: Colors.white, fontSize: 10),
                //       ),
                //     ],
                //   ),
                // ),
              ])
            ],
          ),
        );
      },
    );
  }

  void deleteImage(id, index, albumId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.4,
          decoration: BoxDecoration(
              color: const Color(0xffF5F6F7),
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25.0),
                  topRight: Radius.circular(25.0)),
              border: Border.all(color: Colors.black, width: 1.0)),
          child: Column(
            children: [
              const SizedBox(
                height: 10,
              ),
              Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
              const SizedBox(
                height: 20,
              ),
              Center(
                  child: Text(
                S.of(context).deleteimage,
                style: const TextStyle(fontWeight: FontWeight.bold),
              )),
              Container(
                padding: const EdgeInsets.all(15),
                child: Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10)),
                  child: Container(
                    padding: const EdgeInsets.all(15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(
                          height: 20,
                        ),
                        Text(S.of(context).areyousureyouwanttodeletethisimage),
                        const SizedBox(
                          height: 20,
                        ),
                        StatefulBuilder(
                          builder: (BuildContext context, refrechState) {
                            return Center(
                              child: GestureDetector(
                                onTap: () async {
                                  refrechState(() {
                                    isLoading = true;
                                  });

                                  var response = await HotelsRepository()
                                      .deleteImage(
                                          id, widget.categoryName, albumId);
                                  print(response.code);
                                  if (response.code == 1) {
                                    Navigator.pop(context);
                                  } else {
                                    Navigator.pop(context);
                                    snackbar(response.msg ?? '');
                                  }
                                  refrechState(() {
                                    isLoading = false;
                                  });
                                  getAlbums();
                                },
                                child: Container(
                                  height: 50,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                      color: const Color(0xffE04E4D),
                                      borderRadius: BorderRadius.circular(10)),
                                  child: Container(
                                    padding: const EdgeInsets.all(10),
                                    child: Center(
                                      child: isLoading
                                          ? const ADLinearProgressIndicator()
                                          : Text(
                                              S.of(context).yesde,
                                              style: const TextStyle(
                                                color: Colors.white,
                                              ),
                                            ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void uploadimages(BuildContext context, album) {
    List<File>? _images = [];
    log('asdusad');

    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
              height: MediaQuery.of(context).size.height * 0.40,
              decoration: BoxDecoration(
                  color: const Color(0xffF5F6F7),
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25.0),
                      topRight: Radius.circular(25.0)),
                  border: Border.all(color: Colors.black, width: 1.0)),
              child: SingleChildScrollView(
                  child: Column(
                children: [
                  const SizedBox(
                    height: 10,
                  ),
                  Container(
                      height: 5, width: 50, color: const Color(0xffD2D4D6)),
                  const SizedBox(
                    height: 20,
                  ),
                  Center(
                      child: Text(
                    S.of(context).Uploadimages,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  )),
                  Container(
                    width: MediaQuery.of(context).size.width,
                    padding: const EdgeInsets.all(15),
                    child: Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10)),
                      child: Container(
                        padding: const EdgeInsets.all(15),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(
                              height: 10,
                            ),
                            const Text(
                              "Upload Cover",
                              style: TextStyle(fontSize: 13),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            ADFilePicker(
                              onFilesSelected: (files) => _images = files,
                              title: S.of(context).Tabheretouploadimage,
                              type: FileType.image,
                              isMultiple: true,
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            StatefulBuilder(
                              builder: (BuildContext context, refrechState) {
                                return Center(
                                  child: GestureDetector(
                                    onTap: () async {
                                      print(widget.id.toString());
                                      print(album.id.toString());
                                      refrechState(() {
                                        isLoading = true;
                                      });
                                      print(
                                          "================ ${widget.categoryName} =============");

                                      FormData form = FormData.fromMap({
                                        "album_id": album.id,
                                        "video_id": widget.id,
                                      });

                                      if (_images != null) {
                                        for (int i = 0;
                                            i < _images!.length;
                                            i++) {
                                          form.files.add(
                                            MapEntry(
                                              "image[]",
                                              await MultipartFile.fromFile(
                                                  _images![i].path),
                                            ),
                                          );
                                        }
                                      }
                                      var response = await HotelsRepository()
                                          .uploadCategoryImages(
                                              form, widget.categoryName);
                                      if (response.code == 1) {
                                        await getAlbums();
                                        // categoryBloc.getMainCategoryImages(
                                        //     widget.id, widget.categoryName);
                                        Navigator.pop(context);
                                      } else {
                                        Navigator.pop(context);
                                        snackbar(response.msg ?? '');
                                      }
                                      refrechState(() {
                                        isLoading = false;
                                      });
                                      getAlbums();
                                    },
                                    child: Container(
                                      height: 50,
                                      width: MediaQuery.of(context).size.width,
                                      decoration: BoxDecoration(
                                          color: GlobalColors.primaryColor,
                                          borderRadius:
                                              BorderRadius.circular(10)),
                                      child: Container(
                                        padding: const EdgeInsets.all(10),
                                        child: Center(
                                          child: isLoading
                                              ? const ADLinearProgressIndicator()
                                              : Text(
                                                  S.of(context).UploadPhotos,
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                  ),
                                                ),
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              )),
            )));
  }
}
