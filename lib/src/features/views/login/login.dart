import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../core/shared_widgets/snack_bar.dart';
import '../../bloc/auth_blok.dart';
import '../account/account.dart';
import 'widgets/forger_password/forget_password.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  _Login createState() => _Login();
}

bool isLoading = false;

class _Login extends State<Login> {
  String? phoneToken;

  @override
  void initState() {
    init();
    progrsss(context);
    super.initState();
  }

  void init() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    await messaging.getToken().then((value) => phoneToken = value);
    print('User granted permission: ${settings.authorizationStatus}');
  }

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  void submit(
    String email,
    String password,
  ) async {
    print("fssfsf");
    print(emailController.text.isEmpty);
    if (emailController.text == "") {
      snackbar(S.of(context).entere);

      return;
    }
    if (passwordController.text.isEmpty) {
      snackbar(S.of(context).enterp);

      return;
    } else {
      setState(() {
        isLoading = true;
      });

      // pr.show();
      final Map<String, dynamic> successInformation =
          await bloc.login(email, password, phoneToken);
      print("success");
      print(successInformation);
      // pr.hide();
      if (successInformation['code'] == 1) {
        SharedPreferences _prefs = await SharedPreferences.getInstance();
        String token = successInformation['data']['access_token'];
        _prefs.setInt('user_id', successInformation['data']['id']);
        _prefs.setString('token', token);
        _prefs.setBool('is_logged', true);

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => Account(),
          ),
        );
      } else {
        if (successInformation['msg'] == null) {
          snackbar(S.of(context).wrong);
        } else {
          snackbar(successInformation['msg']);
        }
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  bool _validatephone = false;
  bool isvisible = false;
  TextEditingController emailController =
      TextEditingController(text: kDebugMode ? "<EMAIL>" : null);
  TextEditingController passwordController =
      TextEditingController(text: kDebugMode ? "123456" : null);
  TextEditingController phoneController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  TextEditingController newpassword = TextEditingController();
  TextEditingController confirmpassword = TextEditingController();
  TextEditingController emailresetpasswordController = TextEditingController();
  TextEditingController confirmpasswordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final Widget svg1 = Image.asset(
      'assets/splash.png',
      width: 150,
    );

    final Widget svg2 = SizedBox(
        width: 30,
        height: 10,
        child: SvgPicture.asset(
          'assets/right-arrow.svg',
          semanticsLabel: 'Acme Logo',
          // fit: BoxFit.cover,
        ));
    return SafeArea(
        child: Scaffold(
            body: Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: SingleChildScrollView(
                    child: Column(
                        textDirection: AuthBloc.isEnglish
                            ? TextDirection.ltr
                            : TextDirection.rtl,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                      // GestureDetector(
                      //   onTap: () {
                      //     Navigator.pop(context);
                      //   },
                      //   child: Container(
                      //     padding: EdgeInsets.only(top: 10),
                      //     child: Icon(Icons.arrow_back),
                      //   ),
                      // ),

                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                        child: svg1,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Text(
                        S.of(context).EmailAddress,
                        style: const TextStyle(fontSize: 13),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Form(
                        key: _formKey,
                        child: Directionality(
                          textDirection: AuthBloc.isEnglish
                              ? TextDirection.ltr
                              : TextDirection.rtl,
                          child: Column(
                            textDirection: AuthBloc.isEnglish
                                ? TextDirection.ltr
                                : TextDirection.rtl,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(3)),
                                  child: Container(
                                      decoration: BoxDecoration(
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(5)),
                                          border: Border.all(
                                              color: Colors.black12,
                                              width: 1.0)),
                                      child: TextFormField(
                                        keyboardType:
                                            TextInputType.emailAddress,
                                        controller: emailController,
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 20, right: 20),
                                            errorText:
                                                _validatephone ? '' : null,
                                            hintText:
                                                S.of(context).EmailAddress,
                                            hintStyle: const TextStyle(
                                                color: Colors.grey,
                                                fontSize: 14),
                                            border: InputBorder.none),
                                      ))),
                              const SizedBox(
                                height: 20,
                              ),
                              Text(
                                S.of(context).Password,
                                style: const TextStyle(
                                  fontSize: 13,
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(3)),
                                  child: Container(
                                      decoration: BoxDecoration(
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(5)),
                                          border: Border.all(
                                              color: Colors.black12,
                                              width: 1.0)),
                                      child: TextFormField(
                                        obscureText: isvisible ? false : true,
                                        controller: passwordController,
                                        decoration: InputDecoration(
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 20,
                                                    right: 20,
                                                    top: 12),
                                            errorText: _validatephone
                                                ? S.of(context).enterp
                                                : null,
                                            hintText: S.of(context).Password,
                                            suffixIcon: IconButton(
                                                onPressed: () {
                                                  setState(() {
                                                    isvisible = !isvisible;
                                                  });
                                                },
                                                icon: const Icon(Icons
                                                    .remove_red_eye_rounded)),
                                            hintStyle: const TextStyle(
                                                color: Colors.grey,
                                                fontSize: 14),
                                            border: InputBorder.none),
                                      ))),
                              const SizedBox(
                                height: 20,
                              ),
                              Center(
                                child: GestureDetector(
                                  onTap: () {
                                    forgetpassword(context,
                                        newpassword: newpassword,
                                        emailresetpasswordController:
                                            emailresetpasswordController,
                                        validatephone: _validatephone,
                                        setState: setState,
                                        codeController: codeController,
                                        confirmpasswordController:
                                            confirmpasswordController);
                                  },
                                  child: Text(
                                    S.of(context).ForgotPassword,
                                    style: const TextStyle(
                                        fontSize: 16,
                                        color: Color(0xff0852AB),
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              const SizedBox(
                                height: 40,
                              ),
                              !isLoading
                                  ? Center(
                                      child: Container(
                                          height: 45,
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(3)),
                                          child: GestureDetector(
                                              onTap: () {
                                                submit(emailController.text,
                                                    passwordController.text);
                                              },
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 20, right: 20),
                                                  decoration: BoxDecoration(
                                                      color: GlobalColors
                                                          .primaryColor,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              5),
                                                      border: Border.all(
                                                          color: Colors.black12,
                                                          width: 1.0)),
                                                  child: Center(
                                                      child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Text(
                                                        S.of(context).Login,
                                                        style: const TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 16),
                                                      ),
                                                      const SizedBox(
                                                        width: 15,
                                                      ),
                                                      if (AuthBloc.isEnglish)
                                                        svg2
                                                      else
                                                        const Icon(
                                                          Icons.arrow_forward,
                                                          color: Colors.white,
                                                        )
                                                    ],
                                                  ))))),
                                    )
                                  : const ADLinearProgressIndicator(),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                    ]))),
            extendBody: true,
            resizeToAvoidBottomInset: true));
  }
}
