import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../bloc/auth_blok.dart';
import '../../login.dart';
import 'verify_code.dart';

void sendrequestpassword(
  BuildContext context,
  emailresetpasswordController,
  codeController,
  newpassword,
  validatephone,
  confirmpasswordController,
  setState,
) async {
  print("fssfsf");
  print(isLoading);
  print(emailresetpasswordController.text.isEmpty);
  if (emailresetpasswordController.text.isEmpty) {
    snackbar(S.of(context).entere);

    return;
  } else {
    setState(() {
      isLoading = true;
    });
    print("oooooo");
    print(isLoading);
    pr.show();
    final Map<String, dynamic> successInformation =
        await bloc.sendrequestpassword(emailresetpasswordController.text);
    print("success");
    print(successInformation);
    pr.hide();
    if (successInformation['code'] == 1) {
      verfiycode(context,
          setState: setState,
          emailresetpasswordController: emailresetpasswordController,
          codeController: codeController,
          newpassword: newpassword,
          validatephone: validatephone,
          confirmpasswordController: confirmpasswordController);
    } else {
      if (successInformation['msg'] == null) {
        snackbar(S.of(context).wrong);
      } else {
        snackbar(successInformation['msg']);
      }
    }
  }

  setState(() {
    isLoading = false;
  });
}

void requestforgetpassword(
  BuildContext context, {
  required newpassword,
  required emailresetpasswordController,
  required validatephone,
  required confirmpasswordController,
  setState,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.50,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  S.of(context).EnterNewPassword,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(S.of(context).EnterNewPassword),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            S.of(context).EnterNewPassword,
                            style: const TextStyle(
                                fontSize: 16, color: Color(0xff8B959E)),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                              height: 40,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(3)),
                              child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(5)),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0)),
                                  child: TextFormField(
                                    controller: newpassword,
                                    decoration: InputDecoration(
                                        contentPadding: const EdgeInsets.only(
                                            left: 20, right: 20, bottom: 10),
                                        errorText: validatephone
                                            ? S.of(context).EnterNewPassword
                                            : null,
                                        hintText:
                                            S.of(context).EnterNewPassword,
                                        hintStyle: const TextStyle(
                                            color: Color(0xffB7B7B7),
                                            fontSize: 16),
                                        border: InputBorder.none),
                                  ))),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            S.of(context).ConfirmNewPassword,
                            style: const TextStyle(
                                fontSize: 16, color: Color(0xff8B959E)),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                              height: 40,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(3)),
                              child: Container(
                                  decoration: BoxDecoration(
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(5)),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0)),
                                  child: TextFormField(
                                    controller: confirmpasswordController,
                                    decoration: InputDecoration(
                                        contentPadding: const EdgeInsets.only(
                                            left: 20, right: 20, bottom: 10),
                                        errorText: validatephone
                                            ? S.of(context).ConfirmNewPassword
                                            : null,
                                        hintText:
                                            S.of(context).ConfirmNewPassword,
                                        hintStyle: const TextStyle(
                                            color: Color(0xffB7B7B7),
                                            fontSize: 16),
                                        border: InputBorder.none),
                                  ))),
                          const SizedBox(height: 20),
                          // !isLoading
                          //     ?

                          Center(
                              child: GestureDetector(
                                  onTap: () async {
                                    // resetpassword(
                                    //     emailresetpasswordController.text,
                                    //     newpassword.text,
                                    //     confirmpasswordController.text);
                                    resetpassword(
                                        context,
                                        emailresetpasswordController.text,
                                        newpassword.text,
                                        confirmpasswordController.text,
                                        emailresetpasswordController:
                                            emailresetpasswordController,
                                        confirmpasswordController:
                                            confirmpasswordController,
                                        newpassword: newpassword,
                                        setState: setState,
                                        isLoading: isLoading);
                                  },
                                  child: Container(
                                    height: 50,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                        color: GlobalColors.primaryColor,
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    child: Container(
                                        padding: const EdgeInsets.all(10),
                                        child: Center(
                                            child: Text(
                                          S.of(context).Resetpassword,
                                          style: const TextStyle(
                                              color: Colors.white),
                                        ))),
                                  ))),
                          // : Center(
                          //     child: Lottie.asset(
                          //         'assets/59218-progress-indicator.json',
                          //         height: 50,
                          //         width: 50)),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}

void resetpassword(
  BuildContext context,
  String email,
  String password,
  String confirmpassword, {
  required emailresetpasswordController,
  required confirmpasswordController,
  required newpassword,
  required setState,
  required isLoading,
}) async {
  print("fssfsf");
  print(emailresetpasswordController.text.isEmpty);
  if (emailresetpasswordController.text.isEmpty) {
    snackbar(S.of(context).entere);

    return;
  }
  if (newpassword.text.isEmpty) {
    snackbar(S.of(context).enterp);

    return;
  }
  if (confirmpasswordController.text.isEmpty) {
    snackbar(S.of(context).enterpc);

    return;
  }
  if (confirmpasswordController.text != newpassword.text) {
    snackbar(S.of(context).enterpc);

    return;
  } else {
    setState(() {
      isLoading = true;
    });

    pr.show();
    final Map<String, dynamic> successInformation =
        await bloc.resetpassword(email, password);
    print("success");
    print(successInformation);
    pr.hide();
    if (successInformation['code'] == 1) {
      resetsuccess(context);
    } else {
      if (successInformation['msg'] == null) {
        snackbar(S.of(context).wrong);
      } else {
        snackbar(successInformation['msg']);
      }
    }
  }

  setState(() {
    isLoading = false;
  });
}

void resetsuccess(BuildContext context) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.50,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.only(top: 30),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            height: 40,
                            width: 40,
                            decoration: const BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(40)),
                                color: Colors.green),
                            child: const Center(
                              child: Icon(
                                Icons.check,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            S.of(context).passwordchangedsuccessfullly,
                            style: const TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 16),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          Text(
                            S.of(context).pleaseusenewpasswordtologin,
                            style: const TextStyle(
                                fontSize: 13, color: Color(0xff51565B)),
                          ),
                          const SizedBox(
                            height: 30,
                          ),
                          Center(
                              child: Container(
                                  padding: const EdgeInsets.only(
                                      right: 20, left: 20),
                                  child: GestureDetector(
                                      onTap: () async {
                                        // submit(emailController.text,
                                        //     passwordController.text);
                                        Navigator.of(context).pushReplacement(
                                            MaterialPageRoute(
                                                builder:
                                                    (BuildContext context) =>
                                                        Login()));
                                        // verfiycode(context);
                                        // _submit(rate.toString(), _comment.text);
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: GlobalColors.primaryColor,
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                            padding: const EdgeInsets.all(10),
                                            child: Center(
                                                child: Text(
                                              S.of(context).Login,
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ))),
                                      )))),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}
