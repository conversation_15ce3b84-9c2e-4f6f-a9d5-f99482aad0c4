import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../bloc/auth_blok.dart';
import '../../login.dart';
import 'reset_password.dart';

void verfiycode(
  BuildContext context, {
  required setState,
  required emailresetpasswordController,
  required codeController,
  required newpassword,
  required validatephone,
  required confirmpasswordController,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.50,
                decoration: BoxDecoration(
                    color: const Color(0xffF5F6F7),
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        height: 5, width: 50, color: const Color(0xffD2D4D6)),
                    const SizedBox(
                      height: 20,
                    ),
                    Center(
                        child: Text(
                      S.of(context).EnterdigitsOTP,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    )),
                    Container(
                      padding: const EdgeInsets.all(15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(S.of(context).checkemail),
                              const SizedBox(
                                height: 20,
                              ),
                              Container(
                                padding:
                                    const EdgeInsets.only(left: 60, right: 60),
                                height: 50,
                                child: Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: PinCodeTextField(
                                    keyboardType: TextInputType.number,
                                    autoFocus: true,
                                    length: 4,
                                    obscureText: false,
                                    animationType: AnimationType.fade,
                                    pinTheme: PinTheme(
                                        shape: PinCodeFieldShape.box,
                                        activeFillColor:
                                            const Color(0xffF1F1F1),
                                        activeColor: const Color(0xffF1F1F1),
                                        inactiveColor: const Color(0xffF1F1F1),
                                        inactiveFillColor:
                                            const Color(0xffF1F1F1),
                                        selectedFillColor: Colors.white,
                                        selectedColor: Colors.white,
                                        fieldHeight: 50,
                                        fieldWidth: 50,
                                        // disabledColor:
                                        //     Color(0xff009695),
                                        borderWidth: 1),
                                    cursorColor: const Color(0xff00579F),
                                    animationDuration:
                                        const Duration(milliseconds: 300),
                                    enableActiveFill: true,
                                    controller: codeController,
                                    onCompleted: (_) => null,
                                    appContext: context,
                                    onChanged: (value) => () {},
                                  ),
                                ),
                              ),
                              const SizedBox(height: 20),
                              // !isLoading
                              //     ?
                              Center(
                                  child: Container(
                                      // padding: EdgeInsets.only(right: 20, left: 20),
                                      child: GestureDetector(
                                          onTap: () async {
                                            verfiycodeuser(
                                                context, codeController.text,
                                                emailresetpasswordController:
                                                    emailresetpasswordController,
                                                confirmpasswordController:
                                                    confirmpasswordController,
                                                validatephone: validatephone,
                                                newpassword: newpassword,
                                                setState: setState);
                                            // verfiycodeuser(
                                            //     emailresetpasswordController
                                            //         .text,
                                            //     codeController.text);
                                            // requestforgetpassword(context);
                                          },
                                          child: Container(
                                            height: 50,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            decoration: BoxDecoration(
                                                color:
                                                    GlobalColors.primaryColor,
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            child: Container(
                                                padding:
                                                    const EdgeInsets.all(10),
                                                child: Center(
                                                    child: Text(
                                                  S.of(context).Continue,
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                ))),
                                          )))),
                              // : Center(
                              //     child: Lottie.asset(
                              //         'assets/59218-progress-indicator.json',
                              //         height: 50,
                              //         width: 50)),
                              const SizedBox(height: 10),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                )),
          ));
}

void verfiycodeuser(
  BuildContext context,
  String code, {
  required emailresetpasswordController,
  required confirmpasswordController,
  required validatephone,
  required newpassword,
  required setState,
}) async {
  print("fssfsf");
  print(emailresetpasswordController.text.isEmpty);
  if (emailresetpasswordController.text.isEmpty) {
    snackbar(S.of(context).entere);

    return;
  } else {
    setState(() {
      isLoading = true;
    });

    pr.show();
    final Map<String, dynamic> successInformation =
        await bloc.verfiycode(emailresetpasswordController.text, code);
    print("success");
    print(successInformation);
    pr.hide();
    if (successInformation['code'] == 1) {
      requestforgetpassword(context,
          newpassword: newpassword,
          emailresetpasswordController: emailresetpasswordController,
          validatephone: validatephone,
          confirmpasswordController: confirmpasswordController);
    } else {
      if (successInformation['msg'] == null) {
        snackbar(S.of(context).wrong);
      } else {
        snackbar(successInformation['msg']);
      }
    }
  }

  setState(() {
    isLoading = false;
  });
}
