import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../bloc/auth_blok.dart';
import '../../login.dart';
import 'verify_code.dart';

void forgetpassword(
  BuildContext context, {
  required newpassword,
  required emailresetpasswordController,
  required validatephone,
  required setState,
  required codeController,
  required confirmpasswordController,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                  height: MediaQuery.of(context).size.height * 0.50,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        S.of(context).ForgotPassword,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(S.of(context).enteremail),
                                const SizedBox(
                                  height: 20,
                                ),
                                Text(
                                  S.of(context).EmailAddress,
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Container(
                                    height: 60,
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(3)),
                                    child: Container(
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                const BorderRadius.all(
                                                    Radius.circular(5)),
                                            border: Border.all(
                                                color: Colors.black12,
                                                width: 1.0)),
                                        child: TextFormField(
                                          controller:
                                              emailresetpasswordController,
                                          decoration: InputDecoration(
                                              contentPadding:
                                                  const EdgeInsets.only(
                                                      left: 20,
                                                      right: 20,
                                                      top: 10),
                                              errorText: validatephone
                                                  ? '<EMAIL>'
                                                  : null,
                                              hintText:
                                                  S.of(context).EmailAddress,
                                              hintStyle: const TextStyle(
                                                  color: Colors.grey,
                                                  fontSize: 16),
                                              border: InputBorder.none),
                                        ))),
                                const SizedBox(height: 20),
                                // !isLoading
                                //     ?

                                Center(
                                    child: Container(
                                        // padding: EdgeInsets.only(right: 20, left: 20),
                                        child: GestureDetector(
                                            onTap: () async {
                                              // verfiycode(context);
                                              sendrequestpassword(
                                                  context,
                                                  emailresetpasswordController,
                                                  confirmpasswordController,
                                                  newpassword,
                                                  validatephone,
                                                  codeController,
                                                  setState);
                                            },
                                            child: Container(
                                              height: 50,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              decoration: BoxDecoration(
                                                  color:
                                                      GlobalColors.primaryColor,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10)),
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  child: Center(
                                                      child: Text(
                                                    S.of(context).SenddigitsOTP,
                                                    style: const TextStyle(
                                                        color: Colors.white),
                                                  ))),
                                            )))),
                                // : Center(
                                //     child: Lottie.asset(
                                //         'assets/59218-progress-indicator.json',
                                //         height: 50,
                                //         width: 50)),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
            );
          }));
}

void sendrequestpassword(
  BuildContext context,
  emailresetpasswordController,
  confirmpasswordController,
  newpassword,
  validatephone,
  codeController,
  setState,
) async {
  print("fssfsf");
  print(isLoading);
  print(emailresetpasswordController.text.isEmpty);
  if (emailresetpasswordController.text.isEmpty) {
    snackbar(S.of(context).entere);

    return;
  } else {
    setState(() {
      isLoading = true;
    });
    print("oooooo");
    print(isLoading);
    pr.show();
    final Map<String, dynamic> successInformation =
        await bloc.sendrequestpassword(emailresetpasswordController.text);
    print("success");
    print(successInformation);
    pr.hide();
    if (successInformation['code'] == 1) {
      verfiycode(context,
          setState: setState,
          emailresetpasswordController: emailresetpasswordController,
          codeController: codeController,
          newpassword: newpassword,
          validatephone: validatephone,
          confirmpasswordController: confirmpasswordController);
    } else {
      if (successInformation['msg'] == null) {
        snackbar(S.of(context).wrong);
      } else {
        snackbar(successInformation['msg']);
      }
    }
  }

  setState(() {
    isLoading = false;
  });
}
