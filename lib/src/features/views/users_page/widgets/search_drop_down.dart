import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

const _radius = Radius.circular(12);

class BaseSearchDropDown extends HookWidget {
  final dynamic selectedValue;
  final String? label;
  final String? title;
  final List<dynamic> data;
  final void Function(dynamic)? onChanged;
  final Widget? icon;
  final bool isRequired;
  final bool isMultiSelect;
  final bool ignoring;
  final String? ignoringMessage;
  final Function(dynamic)? itemModelAsName;
  final Function(dynamic)? multiItemsAsName;

  const BaseSearchDropDown(
      {super.key,
      required this.onChanged,
      required this.data,
      required this.label,
      required this.selectedValue,
      this.isRequired = true,
      this.isMultiSelect = false,
      this.ignoring = false,
      this.title,
      this.itemModelAsName,
      this.multiItemsAsName,
      this.ignoringMessage,
      this.icon});

  @override
  Widget build(BuildContext context) {
    final isDropdownOpen = useState(false);

    return Material(
      borderRadius: isDropdownOpen.value
          ? const BorderRadius.only(topLeft: _radius, topRight: _radius)
          : BorderRadius.circular(12),
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            Text(title!,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                )),
            const SizedBox(height: 8),
          ],
          _singleSelect(context, isDropdownOpen: isDropdownOpen),
        ],
      ),
    );
  }

  Widget _singleSelect(BuildContext context,
      {required ValueNotifier<bool> isDropdownOpen}) {
    return DropdownSearch(
      onBeforePopupOpening: (controller) {
        isDropdownOpen.value = true;
        return Future.value(true);
      },
      onBeforeChange: (val, value) {
        isDropdownOpen.value = false;
        return Future.value(true);
      },
      autoValidateMode: AutovalidateMode.onUserInteraction,
      validator: (value) {
        if (value == null && selectedValue == null && isRequired) {
          return 'Please select $label';
        }
        return null;
      },
      selectedItem: selectedValue,
      popupProps: PopupProps.menu(
        onDismissed: () {
          isDropdownOpen.value = false;
        },
        menuProps: MenuProps(
          elevation: 2,
          backgroundColor: Colors.white,
          borderRadius: !isDropdownOpen.value
              ? const BorderRadius.only(
                  bottomLeft: _radius,
                  bottomRight: _radius,
                )
              : BorderRadius.circular(12),
        ),
        itemBuilder: (context, data, isSelected) {
          return Column(
            children: [
              ListTile(
                selected: isSelected,
                title: Text(
                  itemModelAsName != null
                      ? itemModelAsName!(data)
                      : data.toString(),
                ),
              ),
              const Divider(
                thickness: .4,
              ),
            ],
          );
        },
        isFilterOnline: false,
        showSelectedItems: false,
        searchFieldProps: const TextFieldProps(
          decoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(horizontal: 12),
              hintText: "Search"),
        ),
        showSearchBox: true,
      ),
      dropdownBuilder: (context, value) {
        if (value == null && selectedValue == null) {
          return Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Select $label',
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            itemModelAsName != null
                ? itemModelAsName!(value)
                : '${value ?? selectedValue ?? 'Select $label'}',
          ),
        );
      },
      dropdownDecoratorProps: DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          contentPadding: EdgeInsets.symmetric(
            vertical: 12,
            horizontal: icon == null ? 16 : 0,
          ),
          icon: icon,
          border: InputBorder.none,
          labelText: label,
          labelStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          filled: true,
          fillColor: Colors.white,
        ),
      ),
      items: data,
      onChanged: onChanged,
    );
  }
}
