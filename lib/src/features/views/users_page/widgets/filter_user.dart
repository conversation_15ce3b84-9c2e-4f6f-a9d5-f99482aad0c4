import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:admin_dubai/src/features/models/user_list_model.dart';
import 'package:admin_dubai/src/features/models/user_model_type.dart';
import 'package:admin_dubai/src/features/views/users_page/users_page.dart';
import 'package:admin_dubai/src/features/views/users_page/widgets/filter_by_country.dart';
import 'package:admin_dubai/src/features/views/users_page/widgets/filter_by_date.dart';
import 'package:admin_dubai/src/features/views/users_page/widgets/filter_by_type_widget.dart';
import 'package:flutter/material.dart';

import '../../../../../../generated/l10n.dart';

void filterUser(
  BuildContext context, {
  required ValueNotifier<String> valvheck,
  required List<UserModelType> listtype,
  required load,
  required pagenum,
  required userList,
  required List<CountryModel> countries,
  required getUsers,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.50,
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 30, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                        height: 10,
                        color: Colors.transparent,
                      ),
                      Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(S.of(context).Filter,
                                style: const TextStyle(
                                    fontSize: 18, fontWeight: FontWeight.bold)),
                            GestureDetector(
                                onTap: () {
                                  stateSetter(() {
                                    load = true;

                                    userList.clear();
                                    pagenum = 1;
                                    load = false;
                                    valvheck.value = 'All Users';
                                    listtype[0].check = true;
                                    listtype[1].check = false;
                                    listtype[2].check = false;

                                    startDateFilterController.clear();

                                    endDateFilterController.clear();

                                    selectedCountryFilter.value = null;
                                  });
                                  getUsers('');
                                  Navigator.pop(context);
                                  Navigator.pop(context);
                                  Navigator.push(context,
                                      MaterialPageRoute(builder: (context) {
                                    return UserWidgetPage();
                                  }));
                                },
                                child: Text(
                                  S.of(context).Reset,
                                  style:
                                      const TextStyle(color: Color(0xff51565B)),
                                ))
                          ]),

                      const SizedBox(height: 20),

                      //? Filter by type
                      FilterByType(
                          valvheck: valvheck,
                          listtype: listtype,
                          load: load,
                          pagenum: pagenum,
                          userList: userList,
                          stateSetter: stateSetter,
                          getAgetn: getUsers),

                      const SizedBox(
                        height: 25,
                      ),

                      //? Filter by date
                      const FilterByDate(),

                      const SizedBox(
                        height: 25,
                      ),

                      //? Filter by country
                      FilterByCountry(
                        countries: countries,
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      ApplyButton(
                        onTap: () async {
                          stateSetter(() {
                            load = false;
                            pagenum = 1;
                            userList.clear();

                            getUsers(valvheck.value);
                          });
                          Navigator.pop(context);
                        },
                      ),

                      const SizedBox(height: 20),
                    ],
                  )),
                ));
          }));
}

class ApplyButton extends StatelessWidget {
  final Function() onTap;

  const ApplyButton({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 50,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
              color: GlobalColors.primaryColor,
              borderRadius: BorderRadius.circular(5)),
          child: Container(
              padding: const EdgeInsets.all(10),
              child: Center(
                  child: Text(
                S.of(context).ApplyFilter,
                style: const TextStyle(color: Colors.white),
              ))),
        ),
      ),
    );
  }
}
