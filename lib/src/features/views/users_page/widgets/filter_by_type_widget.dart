import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/models/user_model_type.dart';
import 'package:flutter/material.dart';

class FilterByType extends StatelessWidget {
  FilterByType({
    required this.valvheck,
    required this.listtype,
    required this.load,
    required this.pagenum,
    required this.userList,
    required this.getAgetn,
    required this.stateSetter,
  });

  ValueNotifier<String> valvheck;
  List<UserModelType> listtype;
  var load;
  var pagenum;
  var userList;
  var getAgetn;
  var stateSetter;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(10)),
      child: ExpansionTile(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        title: Text(
            '${S.of(context).type}: ${valvheck.value.isEmpty ? S.of(context).AllUsers : valvheck.value}',
            style: const TextStyle(
                fontFamily: 'Roboto-Medium',
                fontSize: 14,
                color: Color(0xFF191C1F))),
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            child: Container(
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(10)),
              child: Container(
                padding: const EdgeInsets.all(15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ListView.builder(
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: () {
                            stateSetter(() {
                              for (int y = 0; y < listtype.length; y++) {
                                listtype[y].check = false;
                              }
                              valvheck.value = listtype[index].val!;
                              listtype[index].check = true;
                            });
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(10),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      listtype[index].name!,
                                      style: TextStyle(
                                          fontFamily:
                                              listtype[index].check == true
                                                  ? 'Roboto-Medium'
                                                  : 'Roboto-Regular',
                                          fontSize: 14,
                                          color: const Color(0xFF191C1F)),
                                    ),
                                    listtype[index].check == true
                                        ? const Icon(
                                            Icons.check,
                                            color: Color(0xFFD8B77F),
                                          )
                                        : const Visibility(
                                            child: Text(''),
                                            visible: false,
                                          )
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: listtype.length,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
