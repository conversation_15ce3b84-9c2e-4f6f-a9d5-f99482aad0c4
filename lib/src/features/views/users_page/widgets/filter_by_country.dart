import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/models/user_list_model.dart';
import 'package:admin_dubai/src/features/views/users_page/widgets/search_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:get/utils.dart';

final ValueNotifier<CountryModel?> selectedCountryFilter = ValueNotifier(null);

class FilterByCountry extends StatelessWidget {
  final List<CountryModel> countries;

  const FilterByCountry({super.key, required this.countries});

  @override
  Widget build(BuildContext context) {
    return BaseSearchDropDown(
      label: S.of(context).Country,
      data: countries.map((e) => e.countryName).toList(),
      selectedValue: selectedCountryFilter.value?.countryName,
      onChanged: (val) {
        selectedCountryFilter.value = countries.firstWhereOrNull(
          (element) => element.countryName == val,
        );
      },
    );
  }
}
