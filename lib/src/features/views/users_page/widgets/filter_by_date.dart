import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/shared_widgets/date_picker.dart';
import 'package:flutter/material.dart';

final TextEditingController startDateFilterController = TextEditingController();
final TextEditingController endDateFilterController = TextEditingController();

class FilterByDate extends StatelessWidget {
  const FilterByDate({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                child: Text(
                  S.of(context).startDate,
                  style: const TextStyle(fontSize: 13),
                ),
              ),
              const SizedBox(height: 10),
              BuildDateFieldWidget(
                startDateFilterController,
              ),
            ],
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                child: Text(
                  S.of(context).endDate,
                  style: const TextStyle(fontSize: 13),
                ),
              ),
              const SizedBox(height: 10),
              BuildDateFieldWidget(
                endDateFilterController,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
