import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:admin_dubai/src/features/models/user_list_model.dart';
import 'package:admin_dubai/src/features/models/user_model_type.dart';
import 'package:admin_dubai/src/features/repository/user_repository.dart';
import 'package:admin_dubai/src/features/response/user_list_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import 'widgets/filter_user.dart';

class UserWidgetPage extends StatefulWidget {
  @override
  _UserWidgetPage createState() => _UserWidgetPage();
}

class _UserWidgetPage extends State<UserWidgetPage> {
  List<UserModelType> listtype = [
    UserModelType('All Users', true, ''),
    UserModelType('Android', false, 'android'),
    UserModelType('iOS', false, 'ios')
  ];
  bool load = false;
  ValueNotifier<String> filterValue = ValueNotifier('All Users');

  List<UserListModel> userList = [];
  List<CountryModel> countries = [];

  int androidUsers = 0;
  int iOSUsers = 0;

  int pagenum = 1;
  int pagesize = 100;

  String search = '';

  getUsers(String val) async {
    setState(() {
      load = false;
      androidUsers = 0;
      iOSUsers = 0;
      //error = res.error.toString();
    });
    pagenum = 1;
    final UserRepository _repository = UserRepository();
    pagenum == 1 ? userList.clear() : print(pagenum);
    UserListResponse res =
        await _repository.getUser(val, search, pagenum, pagesize);

    setState(() {
      userList.addAll(res.userList);

      countries = res.countryList;

      for (var element in userList) {
        if (element.deviceType == 'android') {
          androidUsers += 1;
        }
        if (element.deviceType == 'ios') {
          iOSUsers += 1;
        }
      }

      load = true;
    });
  }

  @override
  void initState() {
    super.initState();
    getUsers('');
  }

  @override
  Widget build(BuildContext context) {
    final title = filterValue.value == 'android'
        ? S.of(context).androidUsers
        : filterValue.value == 'ios'
            ? S.of(context).iosUsers
            : S.of(context).AllUsers;
    // log('asfsaf ${selectedCountryFilter.value?.countryName}');

    final androidUsersWidget = Row(
      children: [
        const Icon(
          Icons.android,
          color: Colors.green,
          size: 17,
        ),
        Text(
          ' ($androidUsers)',
          style: const TextStyle(color: Color(0xff51565B)),
        ),
      ],
    );

    final iOSUserWidget = Row(
      children: [
        Image.asset(
          'assets/apple.png',
          width: 15,
          fit: BoxFit.cover,
        ),
        Text(
          ' ($iOSUsers)',
          style: const TextStyle(color: Color(0xff51565B)),
        ),
      ],
    );

    final Widget svg2 = SizedBox(
        width: 20,
        height: 20.0,
        child: SvgPicture.asset(
          'assets/filter.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).Users),
      ),
      body: Stack(
        children: [
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            const SizedBox(
              height: 20,
            ),

            //? Search Bar
            Container(
              padding: const EdgeInsets.only(left: 20, right: 20),
              child: TextField(
                onChanged: (val) {
                  search = val;
                  getUsers(filterValue.value == 'All Users' ||
                          filterValue.value.isEmpty
                      ? ''
                      : filterValue.value);

                  setState(() {});
                },
                onTapOutside: (_) => FocusScope.of(context).unfocus(),
                decoration: InputDecoration(
                    hintText: S.of(context).Search,
                    hintStyle: const TextStyle(color: Color(0xff51565B)),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: Color(0xff51565B),
                    ),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(
                            color: Color(0xff51565B), width: 1))),
              ),
            ),

            const SizedBox(
              height: 20,
            ),
            Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (filterValue.value == 'All Users' ||
                        filterValue.value.isEmpty) ...[
                      Row(
                        children: [
                          Text(
                            '$title (${userList.length})',
                            style: const TextStyle(color: Color(0xff51565B)),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          androidUsersWidget,
                          const SizedBox(
                            width: 10,
                          ),
                          iOSUserWidget
                        ],
                      ),
                    ] else if (filterValue.value == 'android') ...[
                      androidUsersWidget,
                    ] else if (filterValue.value == 'ios') ...[
                      iOSUserWidget,
                    ],
                    GestureDetector(
                      onTap: () {
                        filterUser(context,
                            valvheck: filterValue,
                            listtype: listtype,
                            load: load,
                            pagenum: pagenum,
                            userList: userList,
                            countries: countries,
                            getUsers: getUsers);
                      },
                      child: svg2,
                    )
                  ],
                )),
            const SizedBox(
              height: 10,
            ),
            load == false
                ? const ADLinearProgressIndicator()
                : userList.isNotEmpty
                    ? Expanded(child: _buildUsersWidget())
                    : Expanded(
                        child: Center(
                          child: Text(
                            S.of(context).ThereAreNoUsers,
                            style: const TextStyle(fontSize: 25),
                          ),
                        ),
                      ),
            const SizedBox(
              height: 20,
            )
          ]),
        ],
      ),
    ));
  }

  Widget _buildUsersWidget() {
    return ListView.builder(
      itemCount: userList.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.fromLTRB(15, 5, 15, 5),
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(width: 1, color: const Color(0xFFF1F1F2)),
                borderRadius: BorderRadius.circular(5)),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
              child: Row(
                children: [
                  Container(
                    decoration: const BoxDecoration(
                        color: Color(0xFFEBEBEB), shape: BoxShape.circle),
                    child: Padding(
                      padding: const EdgeInsets.all(15),
                      child: Text(
                        userList[index].fullname!.isEmpty
                            ? ' ' * 7
                            : userList[index].fullname!.length > 2
                                ? userList[index].fullname!.substring(0, 2)
                                : userList[index].fullname![0],
                        style: const TextStyle(
                            color: Color(0xFFC4C4C4),
                            fontSize: 14,
                            fontFamily: 'Roboto-Medium'),
                      ),
                    ),
                  ),
                  Container(
                    width: MediaQuery.of(context).size.width * 0.7,
                    padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                userList[index].fullname ?? '',
                                style: const TextStyle(
                                    color: Color(0xFF191C1F),
                                    fontSize: 14,
                                    fontFamily: 'Roboto-Medium'),
                              ),
                            ),

                            const SizedBox(
                              width: 5,
                            ),

                            //? Created At
                            Text(
                              userList[index].createdAt?.substring(0, 10) ?? '',
                              style: const TextStyle(
                                  color: Color(0xFF8B959E),
                                  fontSize: 14,
                                  fontFamily: 'Roboto-Regular'),
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(0, 5, 0, 5),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                userList[index].email ?? '',
                                style: const TextStyle(
                                    color: Color(0xFF8B959E),
                                    fontSize: 14,
                                    fontFamily: 'Roboto-Regular'),
                              ),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Align(
                                  alignment: Alignment.centerRight,
                                  child: userList[index].deviceType == 'ios'
                                      ? Image.asset(
                                          'assets/apple.png',
                                          width: 15,
                                          fit: BoxFit.cover,
                                        )
                                      : userList[index].deviceType == 'android'
                                          ? const Icon(
                                              Icons.android,
                                              color: Colors.green,
                                              size: 17,
                                            )
                                          : const SizedBox(),
                                ),
                              )
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(0, 1, 0, 5),
                          child: Row(
                            children: [
                              Text(
                                (userList[index].phoneCode ?? '') +
                                    (userList[index].phone ?? ''),
                                style: const TextStyle(
                                    color: Color(0xFF8B959E),
                                    fontSize: 14,
                                    fontFamily: 'Roboto-Regular'),
                              ),

                              const SizedBox(
                                width: 5,
                              ),

                              //? Country Name
                              Expanded(
                                child: Text(
                                  '(${userList[index].countryName ?? ''})',
                                  style: const TextStyle(
                                      color: Color(0xFF8B959E),
                                      fontSize: 14,
                                      fontFamily: 'Roboto-Regular'),
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // const Spacer(),
                  // Flexible(
                  //   child: Padding(
                  //     padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  //     child: Align(
                  //       alignment: Alignment.centerRight,
                  //       child: userList[index].deviceType == 'ios'
                  //           ? Image.asset(
                  //               'assets/apple.png',
                  //               width: 15,
                  //               fit: BoxFit.cover,
                  //             )
                  //           : userList[index].deviceType == 'android'
                  //               ? const Icon(
                  //                   Icons.android,
                  //                   color: Colors.green,
                  //                   size: 17,
                  //                 )
                  //               : const SizedBox(),
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
        );
      },
      shrinkWrap: true,
    );
  }
}
