import 'package:admin_dubai/src/features/repository/properties_reposotiry.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:queen_validators/queen_validators.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/currency_container.dart';
import '../../../../../core/utils/resources.dart';
import '../properties_page.dart';

void editPropertyPrice(
  id, {
  required BuildContext context,
  required TextEditingController priceController,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.40,
                decoration: BoxDecoration(
                    color: const Color(0xffF5F6F7),
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        height: 5, width: 50, color: const Color(0xffD2D4D6)),
                    const SizedBox(
                      height: 20,
                    ),
                    Center(
                        child: Text(
                      S.of(context).editPrice,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    )),
                    Container(
                      padding: const EdgeInsets.all(15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(
                                height: 20,
                              ),
                              Text(S.of(context).editPrice),
                              const SizedBox(
                                height: 20,
                              ),
                              StatefulBuilder(
                                builder: (BuildContext context, refrechState) {
                                  return Center(
                                      child: Column(children: [
                                    Row(children: [
                                      Expanded(
                                          child: Container(
                                              height: 50,
                                              // width: MediaQuery.of(context).size.width,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                color: Colors.white,
                                                border: Border.all(
                                                    width: 0.5,
                                                    color: Colors.grey),
                                              ),
                                              child: Row(
                                                children: [
                                                  const CurrencyContainer(),
                                                  Container(
                                                    width: 1,
                                                    height: 30,
                                                    color: Colors.grey,
                                                  ),
                                                  Expanded(
                                                    child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                left: 10,
                                                                right: 10),
                                                        height: 50,
                                                        width: 75,
                                                        child: TextFormField(
                                                          controller:
                                                              priceController,
                                                          decoration: InputDecoration(
                                                              border:
                                                                  InputBorder
                                                                      .none,
                                                              hintText: S
                                                                  .of(context)
                                                                  .From,
                                                              hintStyle: const TextStyle(
                                                                  color: Color(
                                                                      0xffB7B7B7),
                                                                  fontSize:
                                                                      14)),
                                                          validator: qValidator(
                                                              [IsRequired()]),
                                                          keyboardType:
                                                              TextInputType
                                                                  .number,
                                                        )),
                                                  )
                                                ],
                                              ))),
                                    ]),
                                  ]));
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30.0),
                      child: GestureDetector(
                          onTap: () async {
                            if (priceController.text.isEmpty) {
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(SnackBar(
                                content: Text(S.of(context).fill),
                              ));
                            }

                            FormData form = FormData.fromMap({
                              'id': id,
                              'price': priceController.text,
                            });

                            var response = await PropertiesRepository()
                                .editStartEndPrice(form);

                            if (response.code == 1) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                      backgroundColor: Colors.green,
                                      content: Text(
                                          S.of(context).editedSuccessfully)));
                              Navigator.pop(context);
                              Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(
                                      builder: (BuildContext context) =>
                                          Properties()));
                            }

                            // Navigator.of(context).pop();
                            // Navigator.of(context).pushNamed(
                            //     Routes.editHolidayHome,
                            //     arguments: id);
                          },
                          child: Container(
                            height: 50,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                color: GlobalColors.primaryColor,
                                borderRadius: BorderRadius.circular(5)),
                            child: Container(
                                padding: const EdgeInsets.all(10),
                                child: Center(
                                    child: Text(
                                  S.of(context).editPrice,
                                  style: const TextStyle(color: Colors.white),
                                ))),
                          )),
                    ),
                    const Spacer(),
                  ],
                )),
          ));
}
