import 'dart:io';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:admin_dubai/src/features/models/category_model.dart';
import 'package:admin_dubai/src/features/response/generalResponse.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/utils.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:queen_validators/queen_validators.dart';

import '../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/currency_container.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/resources.dart';
import '../../../bloc/other_settings_bloc.dart';
import '../../../models/other_settings.dart';
import '../../../repository/properties_reposotiry.dart';
import '../../../response/other_settings_response.dart';
import '../../open_map/open_map.dart';
import '../properties/properties_page.dart';
import 'widgets/add_property_button.dart';

class AddProperty extends StatefulWidget {
  final CategoryModel? property;

  const AddProperty({super.key, this.property});

  @override
  _AddProperty createState() => _AddProperty();
}

class _AddProperty extends State<AddProperty> {
  List<String?> currency = ['AED', 'USD', 'EUR'];
  bool switchOn1 = false;
  bool switchOn2 = false;
  final _formKey = GlobalKey<FormState>();
  String? _arabicPropertyName;
  String? _englishPropertyName;
  String? _arabicDescription;
  String? _englishDescription;
  String? _price;

  // OtherSettingsModel? _features;
  OtherSettingsModel? _location;
  File? _mainVideo;
  File? _mainVideoAr;
  List<File>? _reels;
  List<File>? _images;
  String? _phone;
  String? _website;
  String? _websiteAr;
  String? _instagram;
  String? _whatsapp;
  OtherSettingsModel? _type;
  String? _startSize;
  String? _endSize;
  String? _numberOfRooms;
  Set<Marker>? _marker;
  List<int> f = [];

  List features = [];

  bool _isLoading = false;

  @override
  void initState() {
    othersettingsbloc.getAdminFeatures(AppConstants.propertiesId.toString());
    othersettingsbloc.gettypes(0, 20, '', AppConstants.propertiesId.toString());
    othersettingsbloc.getLocations();

    if (widget.property != null) {
      final property = widget.property!;

      _arabicPropertyName = property.nameAr;
      _englishPropertyName = property.nameEn;
      _arabicDescription = property.descriptionAr;
      _englishDescription = property.descriptionEn;
      _price = property.price?.toString();
      _type = OtherSettingsModel(
          id: property.typeId,
          name: property.type,
          category: property.category);

      _startSize = property.startSize?.toString();
      _endSize = property.endSize?.toString();
      _numberOfRooms = property.roomnumber
          ?.toString()
          .replaceAll('[', '')
          .replaceAll(']', '');

      _marker = {
        Marker(
            markerId: const MarkerId('1'),
            position: LatLng(
              property.lat!,
              property.lng!,
            ),
            infoWindow: InfoWindow(
                title: property.nameEn, snippet: property.descriptionEn))
      };
      _location = OtherSettingsModel(
          id: property.location?.id,
          name: property.location?.name,
          category: property.category);
      _phone = property.phone;

      _website = property.websiteEn;

      _websiteAr = property.websiteAr;

      _instagram = property.instagram;

      _whatsapp = property.whatsapp;

      f = property.features ?? [];

      switchOn1 = property.featuredHome == 1 ? true : false;

      switchOn2 = property.featuredCategory == 1 ? true : false;

      _marker = {
        Marker(
            markerId: const MarkerId('1'),
            position: LatLng(property.lat!, property.lng!))
      };
    }

    super.initState();
  }

  get isEdit => widget.property != null;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).AddnewProperty),
      ),
      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).Basicinformation,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).arapro,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 20, right: 20, top: 15, bottom: 15),
                    child: TextFormField(
                      initialValue: _arabicPropertyName,
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: S.of(context).arapro,
                          hintStyle: const TextStyle(
                              color: Color(0xffB7B7B7), fontSize: 14)),
                      validator: qValidator([IsRequired()]),
                      onSaved: (value) => _arabicPropertyName = value,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).enpro,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _englishPropertyName,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).enpro,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _englishPropertyName = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).desara,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 120,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      child: TextFormField(
                        initialValue: _arabicDescription,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).desara,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _arabicDescription = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).deaen,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 120,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      child: TextFormField(
                        initialValue: _englishDescription,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).deaen,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _englishDescription = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).Price,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                    height: 50,
                    // width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.white,
                      border: Border.all(width: 0.5, color: Colors.grey),
                    ),
                    child: Row(
                      children: [
                        const CurrencyContainer(),
                        Container(
                          width: 1,
                          height: 30,
                          color: Colors.grey,
                        ),
                        Expanded(
                          child: Container(
                              padding:
                                  const EdgeInsets.only(left: 10, right: 10),
                              height: 50,
                              width: 75,
                              child: TextFormField(
                                initialValue: _price,
                                decoration: const InputDecoration(
                                    border: InputBorder.none,
                                    hintStyle: TextStyle(
                                        color: Color(0xffB7B7B7),
                                        fontSize: 14)),
                                validator: qValidator([IsRequired()]),
                                onSaved: (value) => _price = value,
                                keyboardType: TextInputType.number,
                              )),
                        )
                      ],
                    )),
                const SizedBox(height: 20),
                Text(
                  S.of(context).Features,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                  stream: othersettingsbloc.adminFeatures.stream,
                  builder: (BuildContext context,
                      AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                    if (snapshot.hasData &&
                        snapshot.connectionState != ConnectionState.waiting) {
                      if (snapshot.data!.code != 1) {
                        snackbar(snapshot.data!.msg!);
                        return const SizedBox();
                      }
                      if (snapshot.data!.results.isEmpty) {
                        return Text(S.of(context).Therearenoitems);
                      }
                      return StatefulBuilder(
                        builder: (BuildContext context, setStateF) {
                          features = snapshot.data!.results;
                          return SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                for (var item in snapshot.data!.results) ...[
                                  InkWell(
                                    onTap: () {
                                      if (f.contains(item.id)) {
                                        f.remove(item.id);
                                      } else {
                                        f.add(item.id!);
                                      }
                                      setStateF(() {});
                                    },
                                    child: Card(
                                      color: f.contains(item.id)
                                          ? Colors.blue
                                          : Colors.white,
                                      child: Container(
                                        height: 30,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10),
                                        child: Center(
                                            child: Text(
                                          item.name!,
                                          style: TextStyle(
                                              color: f.contains(item.id)
                                                  ? Colors.white
                                                  : Colors.black),
                                        )),
                                      ),
                                    ),
                                  ),
                                ]
                              ],
                            ),
                          );
                        },
                      );
                    }
                    return const Center(child: CircularProgressIndicator());
                  },
                ),

                const SizedBox(height: 20),
                Text(
                  S.of(context).Type,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                    stream: othersettingsbloc.types.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.results.isEmpty) {
                          return Text(S.of(context).Therearenoitems);
                        }

                        if (snapshot.data!.code != 1) {
                          snackbar(snapshot.data!.msg ?? '');
                          return const SizedBox();
                        }

                        return Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(width: 0.5, color: Colors.grey),
                                color: Colors.white),
                            height: 50,
                            child: DropdownButtonFormField<OtherSettingsModel>(
                              isExpanded: true,
                              hint: Text(
                                S.of(context).Type,
                                style:
                                    const TextStyle(color: Color(0xffB7B7B7)),
                              ),
                              onSaved: (value) => _type = value,
                              value: _type ?? snapshot.data?.results.first,
                              iconEnabledColor: Colors.black,
                              items: snapshot.data!.results
                                  .map((OtherSettingsModel value) {
                                return DropdownMenuItem<OtherSettingsModel>(
                                  value: value,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.only(
                                        start: 10.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.only(
                                          start: 10.0),
                                      child: Text(
                                        '${value.name}  ',
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (_) {},
                            ));
                      }
                      return const Center(child: CircularProgressIndicator());
                    }),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).numroom,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 20, right: 20, top: 15, bottom: 15),
                    child: TextFormField(
                      initialValue: _numberOfRooms,
                      decoration: const InputDecoration(
                          border: InputBorder.none,
                          hintText: 'eg. 2,4',
                          hintStyle: TextStyle(
                              color: Color(0xffB7B7B7), fontSize: 14)),
                      keyboardType: TextInputType.text,
                      validator: qValidator([IsRequired()]),
                      onSaved: (value) => _numberOfRooms = value,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).startsize,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 20, right: 20, top: 15, bottom: 15),
                    child: TextFormField(
                      initialValue: _startSize,
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: S.of(context).startsize,
                          hintStyle: const TextStyle(
                              color: Color(0xffB7B7B7), fontSize: 14)),
                      keyboardType: TextInputType.number,
                      validator: qValidator([IsRequired()]),
                      onSaved: (value) => _startSize = value,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).endsize,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 20, right: 20, top: 15, bottom: 15),
                    child: TextFormField(
                      initialValue: _endSize,
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: S.of(context).endsize,
                          hintStyle: const TextStyle(
                              color: Color(0xffB7B7B7), fontSize: 14)),
                      keyboardType: TextInputType.number,
                      validator: qValidator([IsRequired()]),
                      onSaved: (value) => _endSize = value,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).Locationation,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                    stream: othersettingsbloc.locations.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.code != 1) {
                          snackbar(snapshot.data!.msg ?? '');
                          return const SizedBox();
                        }
                        if (snapshot.data!.results.isEmpty) {
                          return Text(S.of(context).Therearenoitems);
                        }
                        return Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(width: 0.5, color: Colors.grey),
                                color: Colors.white),
                            height: 50,
                            child: DropdownButtonFormField<OtherSettingsModel>(
                              isExpanded: true,
                              hint: Text(
                                S.of(context).Location,
                                style:
                                    const TextStyle(color: Color(0xffB7B7B7)),
                              ),
                              onSaved: (value) => _location = value,
                              value: snapshot.data?.results.firstWhereOrNull(
                                      (element) =>
                                          element.id == _location?.id) ??
                                  snapshot.data?.results.first,
                              iconEnabledColor: Colors.black,
                              items: snapshot.data!.results
                                  .map((OtherSettingsModel value) {
                                return DropdownMenuItem<OtherSettingsModel>(
                                  value: value,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.only(
                                        start: 10.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.only(
                                          start: 10.0),
                                      child: Text(
                                        '${value.name}  ',
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (_) {},
                            ));
                      }
                      return const Center(child: CircularProgressIndicator());
                    }),
                const SizedBox(
                  height: 20,
                ),
                Center(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (BuildContext context) => OpenMap(
                            onSave: (markers) {
                              setState(() {
                                _marker = markers;
                              });
                            },
                          ),
                        ),
                      );
                    },
                    child: Text(S.of(context).SetLocationonmap),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),

                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).MediaUpload,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).UploadMainVideo,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  title: S.of(context).Tabheretouploadmainvideo,
                  onSingleFileSelected: (video) => _mainVideo = video,
                  isMultiple: false,
                  type: FileType.video,
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).UploadMainVideoAr,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  title: S.of(context).TabheretouploadmainvideoAr,
                  onSingleFileSelected: (video) => _mainVideoAr = video,
                  isMultiple: false,
                  type: FileType.video,
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 15, bottom: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context).Assignasfeaturedvideoinhomepage,
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 13)),
                          Switch(
                            inactiveTrackColor: Colors.grey[200],
                            activeColor: Colors.grey[200],
                            activeTrackColor: const Color(0xff556477),
                            onChanged: (value) {
                              setState(() {
                                switchOn1 = !switchOn1;
                              });
                            },
                            value: switchOn1,
                          ),
                        ],
                      )),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 15, bottom: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context).Assignasfeaturedvideoincatpage,
                              style:
                                  const TextStyle(color: Colors.grey, fontSize: 13)),
                          Container(
                              child: Switch(
                            inactiveTrackColor: Colors.grey[200],
                            activeColor: Colors.grey[200],
                            activeTrackColor: const Color(0xff556477),
                            onChanged: (value) {
                              setState(() {
                                switchOn2 = !switchOn2;
                              });
                            },
                            value: switchOn2,
                          )),
                        ],
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).UploadReel,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  onFilesSelected: (videos) => _reels = videos,
                  title: S.of(context).Tabheretouploadreel,
                  type: FileType.video,
                ),
                const SizedBox(
                  height: 20,
                ),
                const Text(
                  "Upload Cover",
                  style: TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  onSingleFileSelected: (images) => _images = [images],
                  title: S.of(context).Tabheretouploadimage,
                  type: FileType.media,
                  isMultiple: false,
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).ExtraInformation,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                // SizedBox(
                //   height: 20,
                // ),
                // Text(
                //   S.of(context).GoogleReviewLink,
                //   style: TextStyle(fontSize: 13),
                // ),
                // SizedBox(
                //   height: 10,
                // ),
                // Container(
                //   height: 50,
                //   width: MediaQuery.of(context).size.width,
                //   decoration: BoxDecoration(
                //       borderRadius: BorderRadius.circular(10),
                //       border: Border.all(width: 0.5, color: Colors.grey),
                //       color: Colors.white),
                //   child: Container(
                //       padding: EdgeInsets.only(
                //           left: 20, right: 20, top: 15, bottom: 15),
                //       child: TextFormField(
                //         decoration: InputDecoration(
                //             border: InputBorder.none,
                //             hintText: S.of(context).GoogleReviewLink,
                //             hintStyle: TextStyle(
                //                 color: Color(0xffB7B7B7), fontSize: 14)),
                //       )),
                // ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).PhoneNumber,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _phone,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).PhoneNumber,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        keyboardType: TextInputType.number,
                        onSaved: (value) => _phone = value,
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).website,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _website,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).website,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _website = value,
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).websiteAr,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _websiteAr,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).website,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _websiteAr = value,
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).insta,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _instagram,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).insta,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _instagram = value,
                      )),
                ),
                const SizedBox(height: 20),

                Text(
                  S.of(context).whatsapp,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _whatsapp,
                        decoration: const InputDecoration(
                            border: InputBorder.none,
                            hintText: '+97123456789',
                            hintStyle: TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _whatsapp = value,
                      )),
                ),
                const SizedBox(height: 20),
                _isLoading
                    ? const ADLinearProgressIndicator()
                    : AddPropertyButton(
                        isEdit: isEdit,
                        onTap: () async {
                          if (_formKey.currentState!.validate()) {
                            if (_images == null && !isEdit) {
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(SnackBar(
                                content: Text(S.of(context).selectimg),
                              ));
                            } else if (_mainVideo == null && !isEdit) {
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(SnackBar(
                                content: Text(S.of(context).selectvid),
                              ));
                            } else {
                              _formKey.currentState!.save();
                              if (_marker == null ||
                                  _location == null ||
                                  (f.isEmpty && features.isNotEmpty)) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text(S.of(context).fill),
                                ));
                              } else {
                                FormData form = FormData.fromMap({
                                  if (isEdit) "id": widget.property?.id,
                                  'name[en]': _englishPropertyName,
                                  "name[ar]": _arabicPropertyName,
                                  "description[en]": _englishDescription,
                                  "description[ar]": _arabicDescription,
                                  "price": _price,
                                  "location_id": _location!.id,
                                  "features[]": f,
                                  "currency": 'AED',
                                  "featuredCategory": switchOn2 ? 1 : 0,
                                  "featuredHome": switchOn1 ? 1 : 0,
                                  "latitude": _marker!.first.position.latitude,
                                  "longitude":
                                      _marker!.first.position.longitude,
                                  "phone": _phone,
                                  "website": _website,
                                  "website_ar": _websiteAr,
                                  "instagram": _instagram,
                                  "type_id": _type!.id,
                                  "start_size": _startSize,
                                  "end_size": _endSize,
                                  "rooms": _numberOfRooms,
                                  "category_id": AppConstants.propertiesId,
                                  "whatsapp": _whatsapp,
                                  // !.split(','),
                                });
                                if (_mainVideo != null) {
                                  form.files.add(MapEntry(
                                    "video",
                                    await MultipartFile.fromFile(
                                        _mainVideo!.path),
                                  ));
                                }
                                if (_mainVideoAr != null) {
                                  form.files.add(MapEntry(
                                    "video_ar",
                                    await MultipartFile.fromFile(
                                        _mainVideoAr!.path),
                                  ));
                                }
                                if (_images != null) {
                                  for (int i = 0; i < _images!.length; i++) {
                                    form.files.add(MapEntry(
                                      "image[]",
                                      await MultipartFile.fromFile(
                                          _images![i].path),
                                    ));
                                  }
                                }
                                if (_reels != null) {
                                  for (int i = 0; i < _reels!.length; i++) {
                                    form.files.add(MapEntry(
                                      "reels[]",
                                      await MultipartFile.fromFile(
                                          _reels![i].path),
                                    ));
                                  }
                                }
                                setState(() {
                                  _isLoading = true;
                                });
                                GeneralResponse? response;

                                if (isEdit) {
                                  response = await PropertiesRepository()
                                      .editProperty(form);
                                } else {
                                  response = await PropertiesRepository()
                                      .addProperty(form);
                                }

                                print(
                                    "=================== $response ==================");
                                if (response.code == -1) {
                                  snackbar(response.msg ?? '');
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                          backgroundColor: Colors.green,
                                          content: Text(S.of(context).added)));
                                  Navigator.pop(context);
                                  Navigator.pushReplacement(
                                      context,
                                      MaterialPageRoute(
                                          builder: (BuildContext context) =>
                                              Properties()));
                                }
                                setState(() {
                                  _isLoading = false;
                                });
                              }
                            }
                          } else {
                            snackbar(S.of(context).fill);
                          }
                        },
                      )
              ],
            ),
          ),
        ),
      ),
    ));
  }
}
