import 'dart:async';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../account/account.dart';
import '../login/login.dart';

class SplashPage extends StatefulWidget {
  static String routeName = 'SplashPage';

  const SplashPage({super.key});

  @override
  _SplashPage createState() => _SplashPage();
}

class _SplashPage extends State<SplashPage>
    with SingleTickerProviderStateMixin {
  bool islogin = false;

  final Duration _animationDuration =
      const Duration(seconds: 1, milliseconds: 1);
  late Animation<double> _animation;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: _animationDuration,
      lowerBound: 0.0,
      upperBound: 1.0,
    );

    _animation = CurvedAnimation(
        parent: _animationController, curve: Curves.fastOutSlowIn);

    _animation.addStatusListener((AnimationStatus state) {
      if (state == AnimationStatus.completed) {
        setState(() {});
      }
    });

    _animation.addListener(() {
      setState(() {});
    });

    Future.delayed(
        const Duration(
          milliseconds: 500,
        ), () {
      _animationController.forward();
    });

    isloggedin();

    Future.delayed(
        const Duration(
          seconds: 4,
        ), () {
      islogin
          ? Navigator.of(context).pushReplacement(MaterialPageRoute(
              builder: (BuildContext context) => const Account()))
          : Navigator.of(context).pushReplacement(MaterialPageRoute(
              builder: (BuildContext context) => const Login()));
    });
  }

  isloggedin() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();

    var islogged = _prefs.getBool('is_logged');

    if (islogged == true) {
      setState(() {
        islogin = true;
      });
    } else {
      setState(() {
        islogin = false;
      });
    }
    print(islogin);
  }

  @override
  void dispose() {
    super.dispose();
    _animationController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.sizeOf(context).width;

    final isIpad = width > 600;

    final imageSize = isIpad ? 450 : 300;

    final imageAnimatedSize = isIpad ? 450 : 300;

    final Widget image = Align(
      alignment: Alignment.center,
      child: SizedBox(
        width: imageSize * _animation.value,
        height: imageSize * _animation.value,
        child: Image.asset(
          'assets/splash.png',
          fit: BoxFit.contain,
        ),
      ),
    );

    return SafeArea(
        child: Scaffold(
            body: Stack(
      children: [
        Container(
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
          child: Stack(
            children: <Widget>[
              Positioned.fill(
                child: Align(
                  alignment: Alignment.center,
                  child: SizedBox(
                    width: imageAnimatedSize * _animation.value,
                    height: imageAnimatedSize * _animation.value,
                    child: image,
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    )));
  }
}

// import 'dart:async';
//
// import 'package:admin_dubai/src/core/utils/resources.dart';
// import 'package:flutter/material.dart';
// import 'package:shared_preferences/shared_preferences.dart';
//
// import '../account/account.dart';
// import '../login/login.dart';
//
// class FirstSplash extends StatefulWidget {
//   const FirstSplash({super.key});
//
//   @override
//   _FirstSplash createState() => _FirstSplash();
// }
//
// class _FirstSplash extends State<FirstSplash>
//     with SingleTickerProviderStateMixin {
//   bool islogin = false;
//   final Duration _animationDuration =
//       const Duration(seconds: 1, milliseconds: 1);
//
//   late Animation<double> _animation;
//   late AnimationController _animationController;
//
//   @override
//   void initState() {
//     _animationController = AnimationController(
//       vsync: this,
//       duration: _animationDuration,
//       lowerBound: 0.0,
//       upperBound: 1.0,
//     );
//
//     _animation = CurvedAnimation(
//       parent: _animationController,
//       curve: Curves.fastOutSlowIn,
//     );
//
//     _animation.addStatusListener((AnimationStatus state) {
//       if (state == AnimationStatus.completed) {
//         setState(() {});
//       }
//     });
//
//     _animation.addListener(() {
//       setState(() {});
//     });
//
//     Future.delayed(
//         const Duration(
//           milliseconds: 1000,
//         ), () {
//       _animationController.forward();
//     });
//     isloggedin();
//
//     Future.delayed(
//         const Duration(
//           seconds: 4,
//         ), () {
//       //! here we should navigate
//       islogin
//           ? Navigator.of(context).pushReplacement(MaterialPageRoute(
//               builder: (BuildContext context) => const Account()))
//           : Navigator.of(context).pushReplacement(MaterialPageRoute(
//               builder: (BuildContext context) => const Login()));
//     });
//
//     super.initState();
//   }
//
//   isloggedin() async {
//     SharedPreferences _prefs = await SharedPreferences.getInstance();
//
//     var islogged = _prefs.getBool('is_logged');
//
//     if (islogged == true) {
//       setState(() {
//         islogin = true;
//       });
//     } else {
//       setState(() {
//         islogin = false;
//       });
//     }
//     print(islogin);
//   }
//
//   Widget build(BuildContext context) {
//     final Widget svg2 = Align(
//       alignment: Alignment.center,
//       child: SizedBox(
//         width: 200 * _animation.value,
//         height: 200 * _animation.value,
//         child: Image.asset(
//           'assets/splash.png',
//           fit: BoxFit.contain,
//         ),
//       ),
//     );
//     // = SizedBox(
//     //   height: 100,
//     //   width: 100,
//     //   child: Image.asset(
//     //     'assets/splash.png',
//     //     fit: BoxFit.fill,
//     //     height: 230,
//     //     width: 230,
//     //   ),
//     // );
//
//     return SafeArea(
//         child: Scaffold(
//             // backgroundColor: Color(0xff4D63A0),
//             body: Stack(
//       children: [
//         Container(
//           decoration: BoxDecoration(
//             gradient: LinearGradient(
//               begin: const FractionalOffset(0, 0),
//               end: const FractionalOffset(0, 1),
//               colors: <Color>[
//                 GlobalColors.primaryColor,
//               ],
//             ),
//           ),
//           child: Stack(
//             children: <Widget>[
//               Positioned.fill(
//                 child: Align(
//                   alignment: Alignment.center,
//                   child: SizedBox(
//                     width: 400 * _animation.value,
//                     height: 400 * _animation.value,
//                     child: svg2,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         )
//       ],
//     )));
//   }
// }
