import 'dart:developer';
import 'dart:io';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:admin_dubai/src/features/models/category_model.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/utils.dart';
import 'package:queen_validators/queen_validators.dart';

import '../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/currency_container.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/resources.dart';
import '../../../bloc/other_settings_bloc.dart';
import '../../../models/agent_list_model.dart';
import '../../../models/other_settings.dart';
import '../../../models/year.dart';
import '../../../repository/car_rentals_reposotiry.dart';
import '../../../response/agent_list_response.dart';
import '../../../response/other_settings_response.dart';
import '../../../response/year_list_response.dart';
import '../car_rental_page/car_rentals.dart';

class AddCarRental extends StatefulWidget {
  final CategoryModel? carRent;

  const AddCarRental({super.key, this.carRent});

  @override
  _AddCarRental createState() => _AddCarRental();
}

class _AddCarRental extends State<AddCarRental> {
  bool switchOn1 = false;
  bool switchOn2 = false;
  final _formKey = GlobalKey<FormState>();
  String? _arabicCarRentalName;
  String? _englishCarRentalName;
  String? _arabicDescription;
  String? _englishDescription;
  String? _price;
  String? _privateDrivePrice;

  // OtherSettingsModel? _features;
  List<int> f = [];

  File? _mainVideo;
  List<File>? _reels;
  List<File>? _images;
  String? _phone;
  String? _website;
  String? _instagram;
  AgentListModel? _agent;
  OtherSettingsModel? _type;
  OtherSettingsModel? _brand;
  Year? _year;
  bool _isLoading = false;

  @override
  void initState() {
    othersettingsbloc.getAgents(AppConstants.carRentalsId.toString(), 0, 200);

    othersettingsbloc.getAdminFeatures(AppConstants.carRentalsId.toString());
    othersettingsbloc.gettypes(0, 20, '', AppConstants.carRentalsId.toString());
    othersettingsbloc.getCarBrands();
    othersettingsbloc.getYears();

    if (widget.carRent != null) {
      _arabicCarRentalName = widget.carRent!.nameAr;
      _englishCarRentalName = widget.carRent!.nameEn;
      _arabicDescription = widget.carRent!.descriptionAr;
      _englishDescription = widget.carRent!.descriptionEn;
      _price = widget.carRent!.price.toString();
      _privateDrivePrice = widget.carRent!.privatePrice.toString();
      _agent = widget.carRent!.agent;

      _phone = widget.carRent!.phone;
      _website = widget.carRent!.website;
      _instagram = widget.carRent!.instagram;

      f = widget.carRent!.features ?? [];

      _type = OtherSettingsModel(
          id: widget.carRent!.typeId,
          name: widget.carRent!.type,
          category: widget.carRent!.category);

      _brand = OtherSettingsModel(
          id: widget.carRent!.brandId,
          name: widget.carRent!.brand,
          category: widget.carRent!.category);
      _year = Year(year: widget.carRent!.year);

      switchOn1 = widget.carRent?.featuredHome == 1 ? true : false;

      switchOn2 = widget.carRent?.featuredCategory == 1 ? true : false;
    }

    super.initState();
  }

  get isEdit => widget.carRent != null;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(isEdit ? S.of(context).Edit : S.of(context).addcar),
      ),
      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).Basicinformation,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).arabicnum,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 20, right: 20, top: 15, bottom: 15),
                    child: TextFormField(
                      initialValue: _arabicCarRentalName,
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: S.of(context).arabicnum,
                          hintStyle: const TextStyle(
                              color: Color(0xffB7B7B7), fontSize: 14)),
                      validator: qValidator([IsRequired()]),
                      onSaved: (value) => _arabicCarRentalName = value,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).engnum,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _englishCarRentalName,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).engnum,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _englishCarRentalName = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).Agents,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<AgentListResponse?>(
                  stream: othersettingsbloc.agents.stream,
                  builder: (BuildContext context,
                      AsyncSnapshot<AgentListResponse?> snapshot) {
                    if (snapshot.hasData &&
                        snapshot.connectionState != ConnectionState.waiting) {
                      if (snapshot.data!.code != 1) {
                        snackbar(snapshot.data!.msg!);
                        return const SizedBox();
                      }
                      if (snapshot.data!.agentList.isEmpty) {
                        return const SizedBox();
                      }
                      return Container(
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(width: 0.5, color: Colors.grey),
                            color: Colors.white),
                        height: 50,
                        child: DropdownButtonFormField<AgentListModel>(
                          isExpanded: true,
                          hint: Container(
                            padding: const EdgeInsets.only(left: 5, right: 5),
                            child: Text(
                              S.of(context).Agents,
                              style: const TextStyle(color: Color(0xffB7B7B7)),
                            ),
                          ),
                          onSaved: (value) => _agent = value,
                          value: snapshot.data!.agentList.first,
                          iconEnabledColor: Colors.black,
                          items: snapshot.data!.agentList
                              .map((AgentListModel value) {
                            return DropdownMenuItem<AgentListModel>(
                              value: value,
                              child: Padding(
                                padding: const EdgeInsetsDirectional.only(
                                    start: 10.0),
                                child: Text(
                                  value.fullname!,
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: (_) {},
                        ),
                      );
                    }
                    return const Center(child: CircularProgressIndicator());
                  },
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).desara,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 120,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      child: TextFormField(
                        initialValue: _arabicDescription,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).desara,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _arabicDescription = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).deaen,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 120,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      child: TextFormField(
                        initialValue: _englishDescription,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).deaen,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _englishDescription = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).Price,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                    height: 50,
                    // width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.white,
                      border: Border.all(width: 0.5, color: Colors.grey),
                    ),
                    child: Row(
                      children: [
                        CurrencyContainer(),
                        Container(
                          width: 1,
                          height: 30,
                          color: Colors.grey,
                        ),
                        Expanded(
                          child: Container(
                              padding:
                                  const EdgeInsets.only(left: 10, right: 10),
                              height: 50,
                              width: 75,
                              child: TextFormField(
                                initialValue: _price,
                                decoration: const InputDecoration(
                                    border: InputBorder.none,
                                    hintStyle: TextStyle(
                                        color: Color(0xffB7B7B7),
                                        fontSize: 14)),
                                validator: qValidator([IsRequired()]),
                                onSaved: (value) => _price = value,
                                keyboardType: TextInputType.number,
                              )),
                        )
                      ],
                    )),
                const SizedBox(height: 20),
                Text(
                  S.of(context).priprice,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                    height: 50,
                    // width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.white,
                      border: Border.all(width: 0.5, color: Colors.grey),
                    ),
                    child: Row(
                      children: [
                        CurrencyContainer(),
                        Container(
                          width: 1,
                          height: 30,
                          color: Colors.grey,
                        ),
                        Expanded(
                          child: Container(
                              padding:
                                  const EdgeInsets.only(left: 10, right: 10),
                              height: 50,
                              width: 75,
                              child: TextFormField(
                                initialValue: _privateDrivePrice,
                                decoration: const InputDecoration(
                                    border: InputBorder.none,
                                    hintStyle: TextStyle(
                                        color: Color(0xffB7B7B7),
                                        fontSize: 14)),
                                validator: qValidator([IsRequired()]),
                                onSaved: (value) => _privateDrivePrice = value,
                                keyboardType: TextInputType.number,
                              )),
                        )
                      ],
                    )),
                const SizedBox(height: 20),
                Text(
                  S.of(context).Features,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                  stream: othersettingsbloc.adminFeatures.stream,
                  builder: (BuildContext context,
                      AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                    if (snapshot.hasData &&
                        snapshot.connectionState != ConnectionState.waiting) {
                      if (snapshot.data!.code != 1) {
                        snackbar(snapshot.data!.msg!);
                        return const SizedBox();
                      }
                      if (snapshot.data!.results.isEmpty) {
                        return Text(S.of(context).Therearenoitems);
                      }
                      return StatefulBuilder(
                        builder: (BuildContext context, setStateF) {
                          return SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                for (var item in snapshot.data!.results) ...[
                                  InkWell(
                                    onTap: () {
                                      if (f.contains(item.id)) {
                                        f.remove(item.id);
                                      } else {
                                        f.add(item.id!);
                                      }
                                      setStateF(() {});
                                    },
                                    child: Card(
                                      color: f.contains(item.id)
                                          ? Colors.blue
                                          : Colors.white,
                                      child: Container(
                                        height: 30,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10),
                                        child: Center(
                                            child: Text(
                                          item.name!,
                                          style: TextStyle(
                                              color: f.contains(item.id)
                                                  ? Colors.white
                                                  : Colors.black),
                                        )),
                                      ),
                                    ),
                                  ),
                                ]
                              ],
                            ),
                          );
                        },
                      );
                    }
                    return const Center(child: CircularProgressIndicator());
                  },
                ),

                // StreamBuilder<OtherSettingsResponse?>(
                //   stream: othersettingsbloc.adminFeatures.stream,
                //   builder: (BuildContext context,
                //       AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                //     if (snapshot.hasData &&
                //         snapshot.connectionState != ConnectionState.waiting) {
                //       if (snapshot.data!.results.isEmpty) {
                //         return Text(S.of(context).Therearenoitems);
                //       }
                //       return Container(
                //         width: MediaQuery.of(context).size.width,
                //         decoration: BoxDecoration(
                //             borderRadius: BorderRadius.circular(10),
                //             border: Border.all(
                //                 width: 0.5, color: Colors.grey),
                //             color: Colors.white),
                //         height: 50,
                //         child: DropdownButtonFormField<OtherSettingsModel>(
                //           isExpanded: true,
                //           hint: Container(
                //             padding: const EdgeInsets.only(left: 5, right: 5),
                //             child: Text(
                //               S.of(context).Features,
                //               style: const TextStyle(color: Color(0xffB7B7B7)),
                //             ),
                //           ),
                //           onSaved: (value) => _features = value,
                //           value: snapshot.data!.results.first,
                //           iconEnabledColor: Colors.black,
                //           items: snapshot.data!.results
                //               .map((OtherSettingsModel value) {
                //             return DropdownMenuItem<OtherSettingsModel>(
                //               value: value,
                //               child: Padding(
                //                 padding: const EdgeInsetsDirectional.only(
                //                     start: 10.0),
                //                 child: Text(
                //                   value.name!,
                //                   style: const TextStyle(fontSize: 16),
                //                 ),
                //               ),
                //             );
                //           }).toList(),
                //           onChanged: (_) {},
                //         ),
                //       );
                //     }
                //     return const Center(child: CircularProgressIndicator());
                //   },
                // ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).Type,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                    stream: othersettingsbloc.types.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.results.isEmpty) {
                          return Text(S.of(context).Therearenoitems);
                        }
                        return Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(width: 0.5, color: Colors.grey),
                                color: Colors.white),
                            height: 50,
                            child: DropdownButtonFormField<OtherSettingsModel>(
                              isExpanded: true,
                              hint: Text(
                                S.of(context).Type,
                                style:
                                    const TextStyle(color: Color(0xffB7B7B7)),
                              ),
                              onSaved: (value) => _type = value,
                              value: snapshot.data!.results.first,
                              iconEnabledColor: Colors.black,
                              items: snapshot.data!.results
                                  .map((OtherSettingsModel value) {
                                return DropdownMenuItem<OtherSettingsModel>(
                                  value: value,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.only(
                                        start: 10.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.only(
                                          start: 10.0),
                                      child: Text(
                                        '${value.name}  ',
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (_) {},
                            ));
                      }
                      return const Center(child: CircularProgressIndicator());
                    }),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).Brand,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                    stream: othersettingsbloc.carBrands.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.results.isEmpty) {
                          return const SizedBox();
                        }
                        return Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(width: 0.5, color: Colors.grey),
                                color: Colors.white),
                            height: 50,
                            child: DropdownButtonFormField<OtherSettingsModel>(
                              isExpanded: true,
                              hint: Text(
                                S.of(context).Brand,
                                style:
                                    const TextStyle(color: Color(0xffB7B7B7)),
                              ),
                              onSaved: (value) => _brand = value,
                              value: snapshot.data!.results.first,
                              iconEnabledColor: Colors.black,
                              items: snapshot.data!.results
                                  .map((OtherSettingsModel value) {
                                return DropdownMenuItem<OtherSettingsModel>(
                                  value: value,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.only(
                                        start: 10.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.only(
                                          start: 10.0),
                                      child: Text(
                                        '${value.name}  ',
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (_) {},
                            ));
                      }
                      return const Center(child: CircularProgressIndicator());
                    }),
                const SizedBox(
                  height: 20,
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).CarProductionYear,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<YearListResponse?>(
                    stream: othersettingsbloc.years.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<YearListResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.years!.isEmpty) {
                          return const SizedBox();
                        }
                        var _val;
                        if (widget.carRent != null) {
                          _val ??= snapshot.data!.years!.firstWhereOrNull(
                              (e) => e.year == widget.carRent?.year);
                        } else {
                          _val ??= snapshot.data!.years!.first;
                        }
                        return Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(width: 0.5, color: Colors.grey),
                                color: Colors.white),
                            height: 50,
                            child: DropdownButtonFormField<Year>(
                              isExpanded: true,
                              hint: Text(
                                S.of(context).CarProductionYear,
                                style:
                                    const TextStyle(color: Color(0xffB7B7B7)),
                              ),
                              onSaved: (value) => _year = value,
                              value: _val,
                              iconEnabledColor: Colors.black,
                              items: snapshot.data!.years!.map((Year value) {
                                return DropdownMenuItem<Year>(
                                  value: value,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.only(
                                        start: 10.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.only(
                                          start: 10.0),
                                      child: Text(
                                        '${value.year}  ',
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (_) {},
                            ));
                      }
                      return const Center(child: CircularProgressIndicator());
                    }),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).MediaUpload,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).UploadMainVideo,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  title: S.of(context).Tabheretouploadmainvideo,
                  onSingleFileSelected: (video) => _mainVideo = video,
                  isMultiple: false,
                  type: FileType.video,
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 15, bottom: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context).Assignasfeaturedvideoinhomepage,
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 13)),
                          Container(
                              child: Switch(
                            inactiveTrackColor: Colors.grey[200],
                            activeColor: Colors.grey[200],
                            activeTrackColor: const Color(0xff556477),
                            onChanged: (value) {
                              setState(() {
                                switchOn1 = !switchOn1;
                              });
                            },
                            value: switchOn1,
                          )),
                        ],
                      )),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 15, bottom: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context).Assignasfeaturedvideoincatpage,
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 13)),
                          Container(
                              child: Switch(
                            inactiveTrackColor: Colors.grey[200],
                            activeColor: Colors.grey[200],
                            activeTrackColor: const Color(0xff556477),
                            onChanged: (value) {
                              setState(() {
                                switchOn2 = !switchOn2;
                              });
                            },
                            value: switchOn2,
                          )),
                        ],
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).UploadReel,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  onFilesSelected: (videos) => _reels = videos,
                  title: S.of(context).Tabheretouploadreel,
                  type: FileType.video,
                ),
                const SizedBox(
                  height: 20,
                ),
                const Text(
                  "Upload Cover",
                  style: TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  onSingleFileSelected: (images) => _images = [images],
                  title: S.of(context).Tabheretouploadimage,
                  type: FileType.media,
                  isMultiple: false,
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).ExtraInformation,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),

                // Text(
                //   S.of(context).GoogleReviewLink,
                //   style: TextStyle(fontSize: 13),
                // ),
                // SizedBox(
                //   height: 10,
                // ),
                // Container(
                //   height: 50,
                //   width: MediaQuery.of(context).size.width,
                //   decoration: BoxDecoration(
                //       borderRadius: BorderRadius.circular(10),
                //       border: Border.all(width: 0.5, color: Colors.grey),
                //       color: Colors.white),
                //   child: Container(
                //       padding: EdgeInsets.only(
                //           left: 20, right: 20, top: 15, bottom: 15),
                //       child: TextFormField(
                //         decoration: InputDecoration(
                //             border: InputBorder.none,
                //             hintText: S.of(context).GoogleReviewLink,
                //             hintStyle: TextStyle(
                //                 color: Color(0xffB7B7B7), fontSize: 14)),
                //       )),
                // ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).PhoneNumber,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _phone,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).PhoneNumber,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        keyboardType: TextInputType.number,
                        onSaved: (value) => _phone = value,
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).website,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _website,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).website,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _website = value,
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).insta,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: _instagram,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).insta,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _instagram = value,
                      )),
                ),
                const SizedBox(height: 20),
                _isLoading
                    ? const ADLinearProgressIndicator()
                    : Center(
                        child: Container(
                            // padding: EdgeInsets.only(right: 20, left: 20),
                            child: GestureDetector(
                                onTap: () async {
                                  if (_formKey.currentState!.validate()) {
                                    if (_images == null && !isEdit) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(SnackBar(
                                        content: Text(S.of(context).selectimg),
                                      ));
                                    }
                                    if (_mainVideo == null && !isEdit) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(SnackBar(
                                        content: Text(S.of(context).selectvid),
                                      ));
                                    } else {
                                      _formKey.currentState!.save();
                                      if (_brand == null ||
                                          _year == null ||
                                          f.isEmpty ||
                                          _agent == null) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(SnackBar(
                                          content: Text(S.of(context).fill),
                                        ));
                                      } else {
                                        log('asfassafsafjffiet ${f}');
                                        FormData form = FormData.fromMap({
                                          if (isEdit) "id": widget.carRent!.id,
                                          'name[en]': _englishCarRentalName,
                                          "name[ar]": _arabicCarRentalName,
                                          "description[en]":
                                              _englishDescription,
                                          "description[ar]": _arabicDescription,
                                          "price": _price,
                                          "features[]": f,
                                          "currency": 'AED',
                                          "featuredHome": switchOn1 ? 1 : 0,
                                          "featuredCategory": switchOn2 ? 1 : 0,
                                          "phone": _phone,
                                          "website": _website,
                                          "instagram": _instagram,
                                          "agent_id": _agent!.id,
                                          "type_id": _type!.id,
                                          "private_driver_price":
                                              _privateDrivePrice,
                                          "brand_car": _brand!.id,
                                          "year_car": _year!.id,
                                          "category_id":
                                              AppConstants.carRentalsId,
                                          // "location_id": 1
                                        });
                                        if (_mainVideo != null) {
                                          form.files.add(MapEntry(
                                            "video",
                                            await MultipartFile.fromFile(
                                                _mainVideo!.path),
                                          ));
                                        }
                                        if (_images != null) {
                                          for (int i = 0;
                                              i < _images!.length;
                                              i++) {
                                            form.files.add(MapEntry(
                                              "image[]",
                                              await MultipartFile.fromFile(
                                                  _images![i].path),
                                            ));
                                          }
                                        }
                                        if (_reels != null) {
                                          for (int i = 0;
                                              i < _reels!.length;
                                              i++) {
                                            form.files.add(MapEntry(
                                              "reels[]",
                                              await MultipartFile.fromFile(
                                                  _reels![i].path),
                                            ));
                                          }
                                        }
                                        setState(() {
                                          _isLoading = true;
                                        });
                                        var response;

                                        if (isEdit) {
                                          response =
                                              await CarRentalsRepository()
                                                  .editCarRental(form);
                                        } else {
                                          response =
                                              await CarRentalsRepository()
                                                  .addCarRental(form);
                                        }

                                        if (response.code == -1) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(SnackBar(
                                                  backgroundColor: Colors.red,
                                                  content: Text(
                                                    response.msg ?? '',
                                                    style: const TextStyle(
                                                        color: Colors.white),
                                                  )));
                                        } else {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(SnackBar(
                                                  backgroundColor: Colors.green,
                                                  content: Text(
                                                      S.of(context).added)));
                                          Navigator.pop(context);
                                          Navigator.pushReplacement(
                                              context,
                                              MaterialPageRoute(
                                                  builder:
                                                      (BuildContext context) =>
                                                          CarRentals()));
                                        }
                                        setState(() {
                                          _isLoading = false;
                                        });
                                      }
                                    }
                                  } else {
                                    ScaffoldMessenger.of(context)
                                        .showSnackBar(SnackBar(
                                            backgroundColor: Colors.red,
                                            content: Text(
                                              S.of(context).fill,
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            )));
                                  }
                                },
                                child: Container(
                                  height: 50,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                      color: GlobalColors.primaryColor,
                                      borderRadius: BorderRadius.circular(5)),
                                  child: Container(
                                      padding: const EdgeInsets.all(10),
                                      child: Center(
                                          child: Text(
                                        isEdit
                                            ? S.of(context).Edit
                                            : S.of(context).addcar,
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ))),
                                ))))
              ],
            ),
          ),
        ),
      ),
    ));
  }
}
