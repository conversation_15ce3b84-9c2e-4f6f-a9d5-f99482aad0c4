import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:admin_dubai/src/features/bloc/category_bloc.dart';
import 'package:admin_dubai/src/features/response/category_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/ad_utils.dart';
import '../../../../core/utils/resources.dart';
import '../../video_details/video_details.dart';
import '../add_car_rental/add_car_rental.dart';
import 'widgets/delete_car_rental.dart';

class CarRentals extends StatefulWidget {
  @override
  _CarRentals createState() => _CarRentals();
}

class _CarRentals extends State<CarRentals> {
  TextEditingController searchController = TextEditingController();
  String? currentvalue2;
  String? currentvalue3;
  String? currentvalue4;
  List<String?> filteredAs = ['Filtered As'];
  List<String?> category = ['Car Rentals', 'Shopd'];
  bool isLoading = false;

  @override
  void initState() {
    // carRentalBloc.getCarRentals(0, 200);
    categoryBloc.getCategories(AppConstants.carRentalsId.toString(), 0, 200);

    super.initState();
  }

  final TextEditingController priceController = TextEditingController();
  final TextEditingController privatePriceController = TextEditingController();

  Widget _buildCategoryWidget() {
    return StreamBuilder<CategoryResponse?>(
        stream: categoryBloc.subject.stream,
        builder: (context, snapshot) {
          if (snapshot.hasData &&
              snapshot.connectionState != ConnectionState.waiting) {
            if (snapshot.data!.code != 1) {
              snackbar(snapshot.data!.msg!);
              return const SizedBox();
            }
            if (snapshot.data!.category.isEmpty) {
              return Container(
                height: MediaQuery.of(context).size.height * 0.7,
                child: Center(
                  child: Text(
                    S.of(context).Therearenoitems,
                    style: const TextStyle(fontSize: 25),
                  ),
                ),
              );
            }
            return MasonryGridView.count(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(
                    horizontal: 10.0, vertical: 10.0),
                crossAxisCount: 2,
                mainAxisSpacing: 8,
                itemCount: snapshot.data!.category.length,
                primary: false,
                itemBuilder: (context, index) {
                  return Column(children: [
                    const SizedBox(
                      width: 5,
                    ),
                    Stack(children: [
                      GestureDetector(
                          onTap: () {
                            Navigator.of(context).push(MaterialPageRoute(
                                builder: (BuildContext context) => ChewieDemo(
                                    video: snapshot.data!.category[index].video,
                                    id: snapshot.data!.category[index].id,
                                    image:
                                        snapshot.data!.category[index].image!,
                                    categoryName: "CarRents",
                                    withDetails: true)));
                          },
                          child: Container(
                              child: Container(
                                  child: ClipRRect(
                            borderRadius: BorderRadius.circular(5),
                            child: Image.network(
                              snapshot.data!.category[index].image!,
                              height: 288,
                              width: MediaQuery.of(context).size.width * 0.45,
                              fit: BoxFit.fill,
                            ),
                          )))),
                      GestureDetector(
                          onTap: () {
                            Navigator.of(context).push(MaterialPageRoute(
                                builder: (BuildContext context) => ChewieDemo(
                                    id: snapshot.data!.category[index].id,
                                    image: snapshot.data!.category[index].image,
                                    video: snapshot.data!.category[index].video,
                                    categoryName: "CarRents",
                                    withDetails: true)));
                          },
                          child: Container(
                            height: 288,
                            width: MediaQuery.of(context).size.width * 0.45,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5),
                                gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.white.withOpacity(0.5),
                                      Colors.black.withOpacity(0.5),
                                    ])),
                          )),
                      Positioned(
                          top: 10,
                          left: 10,
                          child: GestureDetector(
                            onTap: () {
                              // priceController.text = snapshot
                              //     .data!.category[index].price
                              //     .toString();
                              //
                              // privatePriceController.text = snapshot
                              //     .data!.category[index].privatePrice
                              //     .toString();
                              //
                              // editCarRentalPrices(
                              //   snapshot.data!.category[index].id,
                              //   context: context,
                              //   priceController: priceController,
                              //   privatePriceController: privatePriceController,
                              // );

                              Navigator.push(context,
                                  MaterialPageRoute(builder: (context) {
                                return AddCarRental(
                                  carRent: snapshot.data!.category[index],
                                );
                              }));
                            },
                            child: Container(
                              height: 25,
                              width: 25,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(25),
                                  color:
                                      const Color(0xff233549).withOpacity(0.2)),
                              child: Center(
                                  child: Icon(Icons.edit,
                                      color: Colors.white.withOpacity(0.5),
                                      size: 16)),
                            ),
                          )),
                      Positioned(
                          top: 10,
                          right: 10,
                          child: GestureDetector(
                            onTap: () {
                              deleteCarRentals(
                                  snapshot.data!.category[index].id, index,
                                  context: context, isLoading: isLoading);
                            },
                            child: Container(
                              height: 25,
                              width: 25,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(25),
                                  color:
                                      const Color(0xff233549).withOpacity(0.2)),
                              child: Center(
                                  child: Icon(Icons.delete_outline,
                                      color: Colors.white.withOpacity(0.5),
                                      size: 16)),
                            ),
                          )),
                      Positioned(
                          bottom: 12,
                          left: 10,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                ADUtils.trimText(
                                    snapshot.data!.category[index].name!, 20),
                                style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 15,
                                    fontWeight: FontWeight.w500),
                              ),
                              const SizedBox(height: 5),
                              Text(
                                snapshot.data!.category[index].price.toString(),
                                style: const TextStyle(
                                    color: Colors.white, fontSize: 14),
                              )
                            ],
                          ))
                    ])
                  ]);
                });
          }
          return const ADCircularProgressIndicator();
        });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: GlobalColors.primaryColor,
              centerTitle: true,
              title: Text(S.of(context).carr),
            ),
            body: SingleChildScrollView(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 20,
                    ),
                    Container(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              S.of(context).allcar,
                              style: const TextStyle(color: Color(0xff51565B)),
                            ),
                          ],
                        )),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        child: Container(
                            height: 40,
                            decoration: BoxDecoration(
                                color: const Color(0xffF1F1F1),
                                borderRadius: BorderRadius.circular(3)),
                            child: Container(
                                decoration: const BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(5)),
                                ),
                                child: TextFormField(
                                  textInputAction: TextInputAction.search,
                                  onFieldSubmitted: (value) {
                                    categoryBloc.getCategories(
                                        AppConstants.carRentalsId.toString(),
                                        0,
                                        200,
                                        value);
                                  },
                                  controller: searchController,
                                  decoration: InputDecoration(
                                      prefixIcon: const Icon(
                                        Icons.search,
                                        color: Color(0xff8B959E),
                                      ),
                                      contentPadding: const EdgeInsets.only(
                                          left: 20, right: 20, top: 5),
                                      hintText: S
                                          .of(context)
                                          .Searchplacesandlocations,
                                      hintStyle: const TextStyle(
                                          color: Color(0xff8B959E),
                                          fontSize: 13),
                                      border: InputBorder.none),
                                )))),
                    _buildCategoryWidget(),
                    const SizedBox(
                      height: 20,
                    )
                  ]),
            ),
            bottomNavigationBar: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                // padding: EdgeInsets.only(right: 20, left: 20),
                child: GestureDetector(
                    onTap: () async {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return const AddCarRental();
                      }));
                    },
                    child: Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          color: GlobalColors.primaryColor,
                          borderRadius: BorderRadius.circular(5)),
                      child: Container(
                          padding: const EdgeInsets.all(10),
                          child: Center(
                              child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.add, color: Colors.white),
                              Text(
                                S.of(context).addcar,
                                style: const TextStyle(color: Colors.white),
                              )
                            ],
                          ))),
                    )))));
  }
}
