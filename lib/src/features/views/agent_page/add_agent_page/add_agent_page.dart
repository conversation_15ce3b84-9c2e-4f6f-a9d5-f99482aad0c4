import 'dart:io';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../repository/agent_repository.dart';
import '../../../response/generalResponse.dart';

class AddAgetnPage extends StatefulWidget {
  @override
  _AddAgetnPage createState() => _AddAgetnPage();
}

class _AddAgetnPage extends State<AddAgetnPage> {
  bool click = true;
  String cat = 'property';
  String code = '+971';
  bool load = false;

  final TextEditingController _controller = TextEditingController();
  final TextEditingController _controllername = TextEditingController();
  final TextEditingController _controllernameAr = TextEditingController();
  final TextEditingController _controllerem = TextEditingController();
  final TextEditingController _controllerph = TextEditingController();
  final TextEditingController _controllerpaww = TextEditingController();

  String datev = '';
  File? _image;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).AddNewagent),
      ),
      body: Container(
        color: Colors.white,
        child: SingleChildScrollView(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            const SizedBox(
              height: 10,
            ),
            ADFilePicker(
              onSingleFileSelected: (images) =>
                  _image = [images].isNotEmpty ? [images][0] : null,
              title: S.of(context).Tabheretouploadimage,
              type: FileType.media,
              isMultiple: false,
            ),
            // const SizedBox(
            //   height: 20,
            // ),
            // Padding(
            //   padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            //   child: Container(
            //     width: MediaQuery.of(context).size.width,
            //     padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            //     decoration: BoxDecoration(
            //         color: const Color(0xFFF9F9F9),
            //         borderRadius: BorderRadius.circular(5)),
            //     child: Padding(
            //       padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
            //       child: Text(
            //         S.of(context).MainAgentInformation,
            //         style: const TextStyle(
            //             fontFamily: 'Roboto-Medium',
            //             fontSize: 16,
            //             fontWeight: FontWeight.bold,
            //             color: Color(0xFF1C2127)),
            //       ),
            //     ),
            //   ),
            // ),
            // const SizedBox(
            //   height: 30,
            // ),
            // Padding(
            //   padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
            //   child: Text(
            //     S.of(context).Agentcompanyname,
            //     style: const TextStyle(
            //         fontFamily: 'Roboto-Medium',
            //         fontSize: 13,
            //         color: Color(0xFF1C2127)),
            //   ),
            // ),
            // const SizedBox(
            //   height: 10,
            // ),
            // Padding(
            //   padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            //   child: Container(
            //     width: MediaQuery.of(context).size.width,
            //     padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            //     decoration: BoxDecoration(
            //         border:
            //             Border.all(width: 1, color: const Color(0xFFEFEFEF)),
            //         borderRadius: BorderRadius.circular(5)),
            //     child: Padding(
            //       padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
            //       child: TextField(
            //         controller: _controller,
            //         style: const TextStyle(
            //           color: Colors.black,
            //         ),
            //         decoration: InputDecoration(
            //             enabledBorder: const UnderlineInputBorder(
            //               borderSide: BorderSide(color: Colors.transparent),
            //             ),
            //             focusedBorder: const UnderlineInputBorder(
            //               borderSide: BorderSide(color: Colors.transparent),
            //             ),
            //             hintText: S.of(context).Agentcompanyname,
            //             hintStyle:
            //                 const TextStyle(color: Colors.grey, fontSize: 12)),
            //       ),
            //     ),
            //   ),
            // ),
            // const SizedBox(
            //   height: 30,
            // ),
            // Padding(
            //   padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
            //   child: Text(
            //     S.of(context).Category,
            //     style: const TextStyle(
            //         fontFamily: 'Roboto-Medium',
            //         fontSize: 13,
            //         color: Color(0xFF1C2127)),
            //   ),
            // ),
            // const SizedBox(
            //   height: 10,
            // ),
            // Padding(
            //   padding: const EdgeInsets.fromLTRB(20, 0, 10, 0),
            //   child: Row(
            //     crossAxisAlignment: CrossAxisAlignment.center,
            //     children: [
            //       InkWell(
            //         onTap: () {
            //           setState(() {
            //             click = true;
            //             cat = 'property';
            //           });
            //         },
            //         child: Container(
            //           width: MediaQuery.of(context).size.width / 2.3,
            //           padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            //           decoration: BoxDecoration(
            //               color: click == true
            //                   ? GlobalColors.primaryColor
            //                   : const Color(0xFFF9F9F9),
            //               borderRadius: BorderRadius.circular(5)),
            //           child: Padding(
            //             padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
            //             child: Text(
            //               S.of(context).Property,
            //               textAlign: TextAlign.center,
            //               style: TextStyle(
            //                   fontFamily: 'Roboto-Medium',
            //                   fontSize: 14,
            //                   fontWeight: FontWeight.bold,
            //                   color: click == true
            //                       ? Colors.white
            //                       : const Color(0xFF233549)),
            //             ),
            //           ),
            //         ),
            //       ),
            //       // InkWell(
            //       //   onTap: () {
            //       //     setState(() {
            //       //       click = true;
            //       //       cat = 'carRent';
            //       //     });
            //       //   },
            //       //   child: Padding(
            //       //     padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            //       //     child: Container(
            //       //       width: MediaQuery.of(context).size.width / 2.3,
            //       //       padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            //       //       decoration: BoxDecoration(
            //       //           color: click == true
            //       //               ? GlobalColors.primaryColor
            //       //               : const Color(0xFFF9F9F9),
            //       //           borderRadius: BorderRadius.circular(5)),
            //       //       child: Padding(
            //       //         padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
            //       //         child: Text(
            //       //           S.of(context).CarRental,
            //       //           textAlign: TextAlign.center,
            //       //           style: TextStyle(
            //       //               fontFamily: 'Roboto-Medium',
            //       //               fontSize: 14,
            //       //               fontWeight: FontWeight.bold,
            //       //               color: click == true
            //       //                   ? Colors.white
            //       //                   : const Color(0xFF233549)),
            //       //         ),
            //       //       ),
            //       //     ),
            //       //   ),
            //       // )
            //     ],
            //   ),
            // ),
            // const SizedBox(
            //   height: 30,
            // ),
            // Padding(
            //   padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
            //   child: Text(
            //     S.of(context).ContractFinishDate,
            //     style: const TextStyle(
            //         fontFamily: 'Roboto-Medium',
            //         fontSize: 13,
            //         color: Color(0xFF1C2127)),
            //   ),
            // ),
            // const SizedBox(
            //   height: 10,
            // ),
            // Padding(
            //   padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            //   child: InkWell(
            //     onTap: () {
            //       FocusScope.of(context).requestFocus(FocusNode());
            //
            //       showDatePicker(
            //         context: context,
            //         initialDate: DateTime.now(),
            //         firstDate: DateTime(1950),
            //         lastDate: DateTime(2100),
            //       ).then((value) {
            //         setState(() {
            //           datev = value.toString().substring(0, 10);
            //         });
            //       });
            //     },
            //     child: Container(
            //       width: MediaQuery.of(context).size.width,
            //       padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            //       decoration: BoxDecoration(
            //           border:
            //               Border.all(width: 1, color: const Color(0xFFEFEFEF)),
            //           borderRadius: BorderRadius.circular(5)),
            //       child: Padding(
            //         padding: const EdgeInsets.fromLTRB(10, 15, 0, 15),
            //         child: Row(
            //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //           children: [
            //             Text(
            //               datev == '' ? 'DD/MM/YY' : datev,
            //               style: const TextStyle(
            //                   fontFamily: 'Roboto-Regular',
            //                   fontSize: 14,
            //                   color: Color(0xFFB7B7B7)),
            //             ),
            //             Row(
            //               children: [
            //                 SvgPicture.asset('assets/cal.svg'),
            //                 const Padding(
            //                   padding: EdgeInsets.only(left: 6),
            //                   child: Icon(
            //                     Icons.keyboard_arrow_down_rounded,
            //                   ),
            //                 )
            //               ],
            //             )
            //           ],
            //         ),
            //       ),
            //     ),
            //   ),
            // ),
            const SizedBox(
              height: 30,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                decoration: BoxDecoration(
                    color: const Color(0xFFF9F9F9),
                    borderRadius: BorderRadius.circular(5)),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
                  child: Text(
                    S.of(context).UserInformation,
                    style: const TextStyle(
                        fontFamily: 'Roboto-Medium',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1C2127)),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
              child: Text(
                S.of(context).Fullname,
                style: const TextStyle(
                    fontFamily: 'Roboto-Medium',
                    fontSize: 13,
                    color: Color(0xFF1C2127)),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                decoration: BoxDecoration(
                    border:
                        Border.all(width: 1, color: const Color(0xFFEFEFEF)),
                    borderRadius: BorderRadius.circular(5)),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                  child: TextField(
                    controller: _controllername,
                    style: const TextStyle(
                      color: Colors.black,
                    ),
                    decoration: InputDecoration(
                        enabledBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent),
                        ),
                        hintText: S.of(context).Fullname,
                        hintStyle:
                            const TextStyle(color: Colors.grey, fontSize: 12)),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
              child: Text(
                S.of(context).FullnameAr,
                style: const TextStyle(
                    fontFamily: 'Roboto-Medium',
                    fontSize: 13,
                    color: Color(0xFF1C2127)),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                decoration: BoxDecoration(
                    border:
                        Border.all(width: 1, color: const Color(0xFFEFEFEF)),
                    borderRadius: BorderRadius.circular(5)),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                  child: TextField(
                    controller: _controllernameAr,
                    style: const TextStyle(
                      color: Colors.black,
                    ),
                    decoration: InputDecoration(
                        enabledBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent),
                        ),
                        hintText: S.of(context).Fullname,
                        hintStyle:
                            const TextStyle(color: Colors.grey, fontSize: 12)),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
              child: Text(
                S.of(context).EmailAddress,
                style: const TextStyle(
                    fontFamily: 'Roboto-Medium',
                    fontSize: 13,
                    color: Color(0xFF1C2127)),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                decoration: BoxDecoration(
                    border:
                        Border.all(width: 1, color: const Color(0xFFEFEFEF)),
                    borderRadius: BorderRadius.circular(5)),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                  child: TextField(
                    controller: _controllerem,
                    style: const TextStyle(
                      color: Colors.black,
                    ),
                    decoration: InputDecoration(
                        enabledBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent),
                        ),
                        hintText: S.of(context).EmailAddress,
                        hintStyle:
                            const TextStyle(color: Colors.grey, fontSize: 12)),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
              child: Text(
                S.of(context).Password,
                style: const TextStyle(
                    fontFamily: 'Roboto-Medium',
                    fontSize: 13,
                    color: Color(0xFF1C2127)),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                decoration: BoxDecoration(
                    border:
                        Border.all(width: 1, color: const Color(0xFFEFEFEF)),
                    borderRadius: BorderRadius.circular(5)),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                  child: TextField(
                    controller: _controllerpaww,
                    obscureText: true,
                    style: const TextStyle(
                      color: Colors.black,
                    ),
                    decoration: InputDecoration(
                        enabledBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.transparent),
                        ),
                        hintText: S.of(context).Password,
                        hintStyle:
                            const TextStyle(color: Colors.grey, fontSize: 12)),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
              child: Text(
                S.of(context).PhoneNumber,
                style: const TextStyle(
                    fontFamily: 'Roboto-Medium',
                    fontSize: 13,
                    color: Color(0xFF1C2127)),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                decoration: BoxDecoration(
                    border:
                        Border.all(width: 1, color: const Color(0xFFEFEFEF)),
                    borderRadius: BorderRadius.circular(5)),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                  child: Row(
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.4,
                        child: CountryCodePicker(
                          onChanged: (value) {
                            setState(() {
                              code = value.dialCode ?? '';
                            });
                            print(value.dialCode);
                          },
                          // Initial selection and favorite can be one of code ('IT') OR dial_code('+39')
                          initialSelection: '+971',
                          favorite: const [
                            '+971',
                          ],
                          // optional. Shows only country name and flag
                          showCountryOnly: false,
                          // optional. Shows only country name and flag when popup is closed.
                          showOnlyCountryWhenClosed: false,
                          // optional. aligns the flag and the Text left
                          alignLeft: true,
                        ),
                      ),
                      Container(
                        height: 40,
                        color: Colors.grey,
                        width: 1,
                      ),
                      Container(
                          width: MediaQuery.of(context).size.width * 0.4,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(3)),
                          child: TextFormField(
                            keyboardType: TextInputType.number,
                            controller: _controllerph,
                            decoration: const InputDecoration(
                                contentPadding: EdgeInsets.only(
                                    left: 20, right: 20, top: 10),
                                hintText: '',
                                hintStyle:
                                    TextStyle(color: Colors.grey, fontSize: 16),
                                border: InputBorder.none),
                          ))
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 30,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                decoration: BoxDecoration(
                    color: const Color(0xFFFAF5EC),
                    borderRadius: BorderRadius.circular(5)),
                child: const Padding(
                  padding: EdgeInsets.fromLTRB(10, 15, 10, 15),
                  child: Text(
                    'If you’re not a UAE citizen please provide us with a phone number that has a WhatsApp account.',
                    style: TextStyle(
                        fontFamily: 'Roboto-Regular',
                        fontSize: 12,
                        color: Color(0xFF1C2127)),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 30,
            ),
            Container(
                padding: const EdgeInsets.only(
                    right: 10, left: 10, bottom: 15, top: 10),
                child: load == true
                    ? const ADLinearProgressIndicator()
                    : GestureDetector(
                        onTap: () async {
                          if (
                              // _controller.text == '' ||
                              // datev == '' ||
                              _controllername.text == '' ||
                                  _controllerem.text == '' ||
                                  _controllerpaww.text == '' ||
                                  _controllerph.text == '') {
                            snackbar(S.of(context).fill);
                          } else {
                            _controller.text = _controllername.text;

                            addAgent();
                          }
                        },
                        child: Container(
                          height: 50,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                              color: GlobalColors.primaryColor,
                              borderRadius: BorderRadius.circular(5)),
                          child: Container(
                              padding: const EdgeInsets.all(10),
                              child: Center(
                                  child: Text(
                                S.of(context).AddAgent,
                                style: const TextStyle(color: Colors.white),
                              ))),
                        ))),
            const SizedBox(
              height: 10,
            ),
          ]),
        ),
      ),
    ));
  }

  addAgent() async {
    setState(() {
      load = true;
    });

    final AgentRepository _repository = AgentRepository();

    GeneralResponse res = await _repository.addAgent(
      _controller.text,
      cat,
      datev,
      _controllername.text,
      _controllernameAr.text,
      _controllerem.text,
      _controllerpaww.text,
      _controllerph.text,
      code,
      _image,
    );

    print(res);

    if (res.code == 1) {
      setState(() {
        load = false;
      });
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      _prefs.setString('pass', _controllerpaww.text);
      Navigator.pop(context, true);
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text("agent has beed added successfully"),
        backgroundColor: Colors.green,
      ));
    } else {
      setState(() {
        load = false;
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(res.msg ?? ''),
          backgroundColor: Colors.red,
        ));
        //error = res.error.toString();
      });
    }
  }
}
