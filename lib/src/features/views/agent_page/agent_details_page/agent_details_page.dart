import 'dart:developer';
import 'dart:io';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../models/agnet_deails_model.dart';
import '../../../repository/agent_repository.dart';
import '../../../response/agent_details_response.dart';
import '../../../response/generalResponse.dart';
import 'widgets/delete_agent.dart';

class AgentDetailsPage extends StatefulWidget {
  final String id;

  const AgentDetailsPage(this.id, {super.key});

  @override
  _AgentDetailsPage createState() => _AgentDetailsPage();
}

class _AgentDetailsPage extends State<AgentDetailsPage> {
  String code = '';
  bool load = false;
  bool edit = false;
  bool save = false;

  AgentDetailsModel? item;
  final TextEditingController _controller = TextEditingController();
  final TextEditingController _controllername = TextEditingController();
  final TextEditingController _controllernameAr = TextEditingController();
  final TextEditingController _controllerem = TextEditingController();
  final TextEditingController _controllerph = TextEditingController();
  final TextEditingController _controllerpaww = TextEditingController();

  String datev = '';
  String pass = '';

  @override
  void initState() {
    super.initState();
    getAgentDetails();
  }

  getAgentDetails() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    setState(() {
      pass = _prefs.getString('pass') ?? '';
    });
    final AgentRepository _repository = AgentRepository();

    AgentDetailsResponse res = await _repository.getAgentDetails(widget.id);
    print(res);

    if (res.code == 1) {
      print(res.agenItem);
      setState(() {
        try {
          item = res.agenItem[0];

          _controller.text = item?.company_name ?? '';

          _controllername.text = item?.fullname ?? '';
          _controllernameAr.text = item?.fullnameAr ?? '';
          _controllerem.text = item?.email ?? '';
          _controllerph.text = item?.phone ?? '';
        } catch (e) {}
        load = true;
      });
    } else {
      setState(() {
        load = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        actions: [
          InkWell(
            onTap: () {
              editOrDeleteDialog(context,
                  id: widget.id,
                  save: save,
                  load: load,
                  edit: edit,
                  setState: setState);
            },
            child: const Padding(
              padding: EdgeInsets.only(left: 20.0, right: 10),
              child: Icon(Icons.more_horiz_rounded),
            ),
          )
        ],
        title: Text(S.of(context).AgentDetails),
      ),
      body: Container(
        color: Colors.white,
        child: load == false
            ? const ADLinearProgressIndicator()
            : SingleChildScrollView(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      ADFilePicker(
                        onSingleFileSelected: (images) {
                          setState(() {
                            _image = [images].isNotEmpty ? [images][0] : null;
                          });
                        },
                        title: S.of(context).Tabheretouploadimage,
                        type: FileType.media,
                        isMultiple: false,
                      ),
                      const SizedBox(
                        height: 20,
                      ),

                      // view  agent network image if not selected file
                      if (_image != null || item?.image != '') ...[
                        Center(
                          child: _image != null
                              ? CircleAvatar(
                                  radius: 50,
                                  backgroundImage: FileImage(_image!))
                              : CircleAvatar(
                                  radius: 50,
                                  backgroundImage:
                                      NetworkImage(item?.image ?? ''),
                                ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                      ],
                      // Padding(
                      //   padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      //   child: Container(
                      //     width: MediaQuery.of(context).size.width,
                      //     padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      //     decoration: BoxDecoration(
                      //         color: const Color(0xFFF9F9F9),
                      //         borderRadius: BorderRadius.circular(5)),
                      //     child: Padding(
                      //       padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
                      //       child: Text(
                      //         S.of(context).MainAgentInformation,
                      //         style: const TextStyle(
                      //             fontFamily: 'Roboto-Medium',
                      //             fontSize: 16,
                      //             fontWeight: FontWeight.bold,
                      //             color: Color(0xFF1C2127)),
                      //       ),
                      //     ),
                      //   ),
                      // ),
                      // const SizedBox(
                      //   height: 30,
                      // ),
                      // Padding(
                      //   padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                      //   child: Text(
                      //     S.of(context).Agentcompanyname,
                      //     style: const TextStyle(
                      //         fontFamily: 'Roboto-Medium',
                      //         fontSize: 13,
                      //         color: Color(0xFF1C2127)),
                      //   ),
                      // ),
                      // const SizedBox(
                      //   height: 10,
                      // ),
                      // Padding(
                      //   padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      //   child: Container(
                      //     width: MediaQuery.of(context).size.width,
                      //     padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      //     decoration: BoxDecoration(
                      //         border: Border.all(
                      //             width: 1, color: const Color(0xFFEFEFEF)),
                      //         borderRadius: BorderRadius.circular(5)),
                      //     child: Padding(
                      //       padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                      //       child: TextField(
                      //         controller: _controller,
                      //         readOnly: edit == true ? false : true,
                      //         style: const TextStyle(
                      //           color: Colors.black,
                      //         ),
                      //         decoration: const InputDecoration(
                      //             enabledBorder: UnderlineInputBorder(
                      //               borderSide:
                      //                   BorderSide(color: Colors.transparent),
                      //             ),
                      //             focusedBorder: UnderlineInputBorder(
                      //               borderSide:
                      //                   BorderSide(color: Colors.transparent),
                      //             ),
                      //             hintStyle: TextStyle(
                      //                 color: Colors.grey, fontSize: 12)),
                      //       ),
                      //     ),
                      //   ),
                      // ),
                      // const SizedBox(
                      //   height: 30,
                      // ),
                      // Padding(
                      //   padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                      //   child: Text(
                      //     S.of(context).Category,
                      //     style: const TextStyle(
                      //         fontFamily: 'Roboto-Medium',
                      //         fontSize: 13,
                      //         color: Color(0xFF1C2127)),
                      //   ),
                      // ),
                      // const SizedBox(
                      //   height: 10,
                      // ),
                      // Padding(
                      //   padding: const EdgeInsets.fromLTRB(20, 0, 10, 0),
                      //   child: Row(
                      //     crossAxisAlignment: CrossAxisAlignment.center,
                      //     children: [
                      //       InkWell(
                      //         child: Container(
                      //           width: MediaQuery.of(context).size.width / 2.3,
                      //           padding:
                      //               const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      //           decoration: BoxDecoration(
                      //               color: GlobalColors.primaryColor,
                      //               borderRadius: BorderRadius.circular(5)),
                      //           child: Padding(
                      //             padding:
                      //                 const EdgeInsets.fromLTRB(10, 15, 10, 15),
                      //             child: Text(
                      //               S.of(context).Property,
                      //               textAlign: TextAlign.center,
                      //               style: const TextStyle(
                      //                   fontFamily: 'Roboto-Medium',
                      //                   fontSize: 14,
                      //                   fontWeight: FontWeight.bold,
                      //                   color: Colors.white),
                      //             ),
                      //           ),
                      //         ),
                      //       ),
                      //       // InkWell(
                      //       //   onTap: () {
                      //       //     setState(() {
                      //       //       click = true;
                      //       //       cat = 'carRent';
                      //       //     });
                      //       //   },
                      //       //   child: Padding(
                      //       //     padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      //       //     child: Container(
                      //       //       width: MediaQuery.of(context).size.width / 2.3,
                      //       //       padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      //       //       decoration: BoxDecoration(
                      //       //           color: click == true
                      //       //               ? GlobalColors.primaryColor
                      //       //               : const Color(0xFFF9F9F9),
                      //       //           borderRadius: BorderRadius.circular(5)),
                      //       //       child: Padding(
                      //       //         padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
                      //       //         child: Text(
                      //       //           S.of(context).CarRental,
                      //       //           textAlign: TextAlign.center,
                      //       //           style: TextStyle(
                      //       //               fontFamily: 'Roboto-Medium',
                      //       //               fontSize: 14,
                      //       //               fontWeight: FontWeight.bold,
                      //       //               color: click == true
                      //       //                   ? Colors.white
                      //       //                   : const Color(0xFF233549)),
                      //       //         ),
                      //       //       ),
                      //       //     ),
                      //       //   ),
                      //       // )
                      //     ],
                      //   ),
                      // ),
                      // const SizedBox(
                      //   height: 30,
                      // ),
                      // Padding(
                      //   padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                      //   child: Text(
                      //     S.of(context).ContractFinishDate,
                      //     style: const TextStyle(
                      //         fontFamily: 'Roboto-Medium',
                      //         fontSize: 13,
                      //         color: Color(0xFF1C2127)),
                      //   ),
                      // ),
                      // const SizedBox(
                      //   height: 10,
                      // ),
                      // Padding(
                      //   padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      //   child: InkWell(
                      //     onTap: () {
                      //       if (edit == false) {
                      //       } else {
                      //         FocusScope.of(context).requestFocus(FocusNode());
                      //
                      //         showDatePicker(
                      //                 context: context,
                      //                 initialDate: DateTime.now(),
                      //                 firstDate: DateTime(1950),
                      //                 lastDate: DateTime(2100))
                      //             .then((value) {
                      //           setState(() {
                      //             datev = value.toString().substring(0, 10);
                      //             item!.avaliableuntil =
                      //                 value.toString().substring(0, 10);
                      //           });
                      //         });
                      //       }
                      //     },
                      //     child: Container(
                      //       width: MediaQuery.of(context).size.width,
                      //       padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                      //       decoration: BoxDecoration(
                      //           border: Border.all(
                      //               width: 1, color: const Color(0xFFEFEFEF)),
                      //           borderRadius: BorderRadius.circular(5)),
                      //       child: Padding(
                      //         padding: const EdgeInsets.fromLTRB(10, 15, 0, 15),
                      //         child: Row(
                      //           mainAxisAlignment:
                      //               MainAxisAlignment.spaceBetween,
                      //           children: [
                      //             Text(
                      //               item!.avaliableuntil ?? '',
                      //               style: const TextStyle(
                      //                   fontFamily: 'Roboto-Regular',
                      //                   fontSize: 14,
                      //                   color: Color(0xFFB7B7B7)),
                      //             ),
                      //             Row(
                      //               children: [
                      //                 SvgPicture.asset('assets/cal.svg'),
                      //                 const Padding(
                      //                   padding: EdgeInsets.only(left: 6),
                      //                   child: Icon(
                      //                     Icons.keyboard_arrow_down_rounded,
                      //                   ),
                      //                 )
                      //               ],
                      //             )
                      //           ],
                      //         ),
                      //       ),
                      //     ),
                      //   ),
                      // ),
                      // const SizedBox(
                      //   height: 30,
                      // ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          decoration: BoxDecoration(
                              color: const Color(0xFFF9F9F9),
                              borderRadius: BorderRadius.circular(5)),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
                            child: Text(
                              S.of(context).UserInformation,
                              style: const TextStyle(
                                  fontFamily: ' Roboto-Medium',
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF1C2127)),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                        child: Text(
                          S.of(context).Fullname,
                          style: const TextStyle(
                              fontFamily: 'Roboto-Medium',
                              fontSize: 13,
                              color: Color(0xFF1C2127)),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 1, color: const Color(0xFFEFEFEF)),
                              borderRadius: BorderRadius.circular(5)),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                            child: TextField(
                              controller: _controllername,
                              readOnly: edit == true ? false : true,
                              style: const TextStyle(
                                color: Colors.black,
                              ),
                              decoration: const InputDecoration(
                                  enabledBorder: UnderlineInputBorder(
                                    borderSide:
                                        BorderSide(color: Colors.transparent),
                                  ),
                                  focusedBorder: UnderlineInputBorder(
                                    borderSide:
                                        BorderSide(color: Colors.transparent),
                                  ),
                                  hintStyle: TextStyle(
                                      color: Colors.grey, fontSize: 12)),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                        child: Text(
                          S.of(context).FullnameAr,
                          style: const TextStyle(
                              fontFamily: 'Roboto-Medium',
                              fontSize: 13,
                              color: Color(0xFF1C2127)),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 1, color: const Color(0xFFEFEFEF)),
                              borderRadius: BorderRadius.circular(5)),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                            child: TextField(
                              controller: _controllernameAr,
                              readOnly: edit == true ? false : true,
                              style: const TextStyle(
                                color: Colors.black,
                              ),
                              decoration: const InputDecoration(
                                  enabledBorder: UnderlineInputBorder(
                                    borderSide:
                                        BorderSide(color: Colors.transparent),
                                  ),
                                  focusedBorder: UnderlineInputBorder(
                                    borderSide:
                                        BorderSide(color: Colors.transparent),
                                  ),
                                  hintStyle: TextStyle(
                                      color: Colors.grey, fontSize: 12)),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                        child: Text(
                          S.of(context).EmailAddress,
                          style: const TextStyle(
                              fontFamily: 'Roboto-Medium',
                              fontSize: 13,
                              color: Color(0xFF1C2127)),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 1, color: const Color(0xFFEFEFEF)),
                              borderRadius: BorderRadius.circular(5)),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                            child: TextField(
                              controller: _controllerem,
                              readOnly: edit == true ? false : true,
                              style: const TextStyle(
                                color: Colors.black,
                              ),
                              decoration: const InputDecoration(
                                  enabledBorder: UnderlineInputBorder(
                                    borderSide:
                                        BorderSide(color: Colors.transparent),
                                  ),
                                  focusedBorder: UnderlineInputBorder(
                                    borderSide:
                                        BorderSide(color: Colors.transparent),
                                  ),
                                  hintStyle: TextStyle(
                                      color: Colors.grey, fontSize: 12)),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                        child: Text(
                          S.of(context).Password,
                          style: const TextStyle(
                              fontFamily: 'Roboto-Medium',
                              fontSize: 13,
                              color: Color(0xFF1C2127)),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 1, color: const Color(0xFFEFEFEF)),
                              borderRadius: BorderRadius.circular(5)),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                            child: TextField(
                              controller: _controllerpaww,
                              readOnly: edit == true ? false : true,
                              obscureText: true,
                              style: const TextStyle(
                                color: Colors.black,
                              ),
                              decoration: InputDecoration(
                                  enabledBorder: const UnderlineInputBorder(
                                    borderSide:
                                        BorderSide(color: Colors.transparent),
                                  ),
                                  focusedBorder: const UnderlineInputBorder(
                                    borderSide:
                                        BorderSide(color: Colors.transparent),
                                  ),
                                  hintText: S.of(context).Password,
                                  hintStyle: const TextStyle(
                                      color: Colors.grey, fontSize: 12)),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                        child: Text(
                          S.of(context).PhoneNumber,
                          style: const TextStyle(
                              fontFamily: 'Roboto-Medium',
                              fontSize: 13,
                              color: Color(0xFF1C2127)),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 1, color: const Color(0xFFEFEFEF)),
                              borderRadius: BorderRadius.circular(5)),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                            child: Row(
                              children: [
                                SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width * 0.4,
                                  child: CountryCodePicker(
                                    onChanged: (value) {
                                      if (edit == false) {
                                      } else {
                                        setState(() {
                                          code = value.dialCode ?? '';
                                        });
                                        print(value.dialCode);
                                      }
                                    },
                                    // Initial selection and favorite can be one of code ('IT') OR dial_code('+39')
                                    initialSelection: item?.phone_code,
                                    favorite: [
                                      item?.phone_code ?? '',
                                    ],
                                    // optional. Shows only country name and flag
                                    showCountryOnly: false,
                                    // optional. Shows only country name and flag when popup is closed.
                                    showOnlyCountryWhenClosed: false,
                                    enabled: edit == true ? true : false,
                                    // optional. aligns the flag and the Text left
                                    alignLeft: true,
                                  ),
                                ),
                                Container(
                                  height: 40,
                                  color: Colors.grey,
                                  width: 1,
                                ),
                                Container(
                                    width:
                                        MediaQuery.of(context).size.width * 0.4,
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(3)),
                                    child: Container(
                                        child: TextFormField(
                                      keyboardType: TextInputType.number,
                                      controller: _controllerph,
                                      readOnly: edit == true ? false : true,
                                      decoration: const InputDecoration(
                                          contentPadding: EdgeInsets.only(
                                              left: 20, right: 20, top: 10),
                                          hintStyle: TextStyle(
                                              color: Colors.grey, fontSize: 16),
                                          border: InputBorder.none),
                                    )))
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      save == false
                          ? Container(
                              padding: const EdgeInsets.only(
                                  right: 10, left: 10, bottom: 15, top: 10),
                              child: edit == true
                                  ? GestureDetector(
                                      onTap: () async {
                                        setState(() {
                                          save = true;
                                        });
                                        editAgent();
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: GlobalColors.primaryColor,
                                            borderRadius:
                                                BorderRadius.circular(5)),
                                        child: Container(
                                            padding: const EdgeInsets.all(10),
                                            child: Center(
                                                child: Text(
                                              S.of(context).SaveChanges,
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ))),
                                      ))
                                  : GestureDetector(
                                      onTap: () async {
                                        setState(() {
                                          edit = true;
                                        });
                                        //    editAgent();
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: GlobalColors.primaryColor,
                                            borderRadius:
                                                BorderRadius.circular(5)),
                                        child: Container(
                                            padding: const EdgeInsets.all(10),
                                            child: Center(
                                                child: Text(
                                              S.of(context).Edit,
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ))),
                                      )))
                          : const ADLinearProgressIndicator(),
                      const SizedBox(
                        height: 10,
                      ),
                    ]),
              ),
      ),
    ));
  }

  File? _image;

  editAgent() async {
    if (_controller.text == '') {
      _controller.text = item?.company_name ?? item?.fullname ?? '';
    }
    if (_controllername.text == '') {
      _controllername.text = item!.fullname ?? '';
    }
    if (_controllernameAr.text == '') {
      _controllernameAr.text = item!.fullnameAr ?? '';
    }
    if (_controllerem.text == '') {
      _controllerem.text = item!.email ?? '';
    }

    if (_controllerpaww.text == '') {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      String passw = _prefs.getString('pass') ?? '';
      _controllerpaww.text = passw;
    }

    if (code == '') {
      code = item!.phone_code ?? '';
    }
    if (_controllerph.text == '') {
      _controllerph.text = item!.phone ?? '';
    }
    final AgentRepository _repository = AgentRepository();
    try {
      log('asfsafasfsafas2111');

      GeneralResponse res = await _repository.updateAgent(
          _controller.text,
          item?.category ?? '',
          item!.avaliableuntil,
          _controllername.text,
          _controllernameAr.text,
          _controllerem.text,
          _controllerpaww.text,
          _controllerph.text,
          code,
          widget.id,
          _image);
      print(res);

      if (res.code.toString() == '1') {
        setState(() {
          save = false;
          edit = false;
        });

        Navigator.pop(context, true);
        getAgentDetails();
      } else {
        setState(() {
          save = false;

          //error = res.error.toString();
        });
      }
    } catch (e, s) {
      log('asfasfsafsafas ${e} ss $s');
      setState(() {
        save = false;

        //error = res.error.toString();
      });
      snackbar(S.of(context).wrong);
    }
  }
}
