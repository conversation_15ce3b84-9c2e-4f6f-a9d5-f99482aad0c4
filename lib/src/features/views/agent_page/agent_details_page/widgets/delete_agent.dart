import 'dart:developer';

import 'package:flutter/material.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../repository/agent_repository.dart';
import '../../../../response/generalResponse.dart';
import '../../agent_page_widget/agent_page.dart';

Future<void> deleteAgent(
  id,
  setState,
  save,
  load,
  edit,
  context,
) async {
  setState(() {
    save = true;
    load = false;
    edit = false;
  });
  final AgentRepository repository = AgentRepository();
  try {
    GeneralResponse res = await repository.deleteAgent(id);
    log('DeleeteAgent: $res');

    if (res.code.toString() == '1') {
      Navigator.pop(context, true);
    } else {
      setState(() {
        save = false;
        load = true;

        //error = res.error.toString();
      });
    }
  } catch (e) {
    setState(() {
      save = false;
      load = true;

      //error = res.error.toString();
    });
    snackbar(S.of(context).wrong);
  }
}

void editOrDeleteDialog(
  BuildContext context, {
  required String id,
  required bool save,
  required bool load,
  required bool edit,
  required Function setState,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: const EdgeInsets.fromLTRB(20, 0, 20, 40),
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                  ),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 20,
                      ),
                      InkWell(
                          onTap: () {
                            Navigator.pop(context);
                            showDelete(context,
                                id: id,
                                setState: setState,
                                save: save,
                                load: load,
                                edit: edit);
                          },
                          child: Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                color: const Color(0xFFF9F9F9),
                                borderRadius: BorderRadius.circular(14)),
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(0, 15, 0, 15),
                              child: Text(
                                S.of(context).DeleteAgent,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                    color: Color(0xFFE04E4D),
                                    fontSize: 17,
                                    fontFamily: 'Roboto-Regular'),
                              ),
                            ),
                          )),
                      const SizedBox(
                        height: 20,
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(14)),
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(0, 15, 0, 15),
                            child: Text(
                              S.of(context).Cancel,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                  color: Color(0xFF007AFF),
                                  fontSize: 17,
                                  fontFamily: 'Roboto-Regular'),
                            ),
                          ),
                        ),
                      )
                    ],
                  )),
                ));
          }));
}

void showDelete(
  BuildContext context, {
  required id,
  required setState,
  required save,
  required load,
  required edit,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                child: Container(
                  decoration: const BoxDecoration(
                    color: Color(0xFFF5F6F7),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                  ),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                          height: 5, width: 30, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Text(
                        S.of(context).DeleteAgent,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                            fontFamily: 'Roboto-Medium',
                            fontSize: 16,
                            color: Color(0xFF191C1F)),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(25, 0, 25, 20),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Padding(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              children: [
                                Text(S.of(context).deleteag,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                        fontFamily: 'Roboto-Medium',
                                        fontSize: 15,
                                        color: Color(0xFF191C1F))),
                                Padding(
                                  padding:
                                      const EdgeInsets.fromLTRB(0, 25, 0, 25),
                                  child: InkWell(
                                    onTap: () async {
                                      await deleteAgent(id, setState, save,
                                          load, edit, context);

                                      Navigator.pop(context);

                                      Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  AgentWidgetPage()));
                                    },
                                    child: Container(
                                      width: MediaQuery.of(context).size.width,
                                      decoration: BoxDecoration(
                                          color: const Color(0xFFE04E4D),
                                          borderRadius:
                                              BorderRadius.circular(14)),
                                      child: Padding(
                                        padding: const EdgeInsets.fromLTRB(
                                            0, 15, 0, 15),
                                        child: Text(
                                          S.of(context).Yes,
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 16,
                                              fontFamily: 'Roboto-Medium'),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      )
                    ],
                  )),
                ));
          }));
}
