import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:admin_dubai/src/features/models/agent_model_type.dart';
import 'package:flutter/material.dart';

import '../../../../../../generated/l10n.dart';

void filterAgent(
  BuildContext context, {
  required valvheck,
  required List<AgentModelType> listtype,
  required load,
  required pagenum,
  required agentList,
  required getAgetn,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.50,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 30, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  height: 10,
                                  color: Colors.transparent,
                                ),

                                Text(S.of(context).Filter,
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold)),

                                // Spacer(),
                                GestureDetector(
                                    onTap: () {
                                      stateSetter(() {
                                        agentList.clear();
                                        pagenum = 1;
                                        load = false;
                                      });
                                      getAgetn('');
                                      Navigator.pop(context);
                                    },
                                    child: Text(
                                      S.of(context).Reset,
                                      style: const TextStyle(
                                          color: Color(0xff51565B)),
                                    ))
                              ])),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ListView.builder(
                                  itemBuilder: (context, index) {
                                    return InkWell(
                                      onTap: () {
                                        stateSetter(() {
                                          for (int y = 0;
                                              y < listtype.length;
                                              y++) {
                                            listtype[y].check = false;
                                          }
                                          valvheck = listtype[index].val!;
                                          listtype[index].check = true;
                                        });
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.all(10),
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  listtype[index].name!,
                                                  style: TextStyle(
                                                      fontFamily: listtype[
                                                                      index]
                                                                  .check ==
                                                              true
                                                          ? 'Roboto-Medium'
                                                          : 'Roboto-Regular',
                                                      fontSize: 14,
                                                      color: const Color(
                                                          0xFF191C1F)),
                                                ),
                                                listtype[index].check == true
                                                    ? const Icon(
                                                        Icons.check,
                                                        color:
                                                            Color(0xFFD8B77F),
                                                      )
                                                    : const Visibility(
                                                        child: Text(''),
                                                        visible: false,
                                                      )
                                              ],
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.fromLTRB(
                                                      0, 10, 0, 10),
                                              child: Container(
                                                width: MediaQuery.of(context)
                                                    .size
                                                    .width,
                                                height: 1,
                                                color: const Color(0xFFF1F1F1),
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: listtype.length,
                                ),
                                Center(
                                  child: Container(
                                    // padding: EdgeInsets.only(right: 20, left: 20),
                                    child: GestureDetector(
                                      onTap: () async {
                                        stateSetter(() {
                                          load = false;
                                          pagenum = 1;
                                          agentList.clear();
                                          getAgetn(valvheck);
                                        });
                                        Navigator.pop(context);

                                        // resetsuccess(context);
                                        // _submit(rate.toString(), _comment.text);
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: GlobalColors.primaryColor,
                                            borderRadius:
                                                BorderRadius.circular(5)),
                                        child: Container(
                                            padding: const EdgeInsets.all(10),
                                            child: Center(
                                                child: Text(
                                              S.of(context).ApplyFilter,
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ))),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}
