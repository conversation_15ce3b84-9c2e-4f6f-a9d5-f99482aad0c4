import 'dart:io';

import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:admin_dubai/src/features/models/category_model.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:queen_validators/queen_validators.dart';

import '../../../../../generated/l10n.dart';
import '../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/currency_container.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/resources.dart';
import '../../../bloc/other_settings_bloc.dart';
import '../../../models/other_settings.dart';
import '../../../repository/chalets_reposotiry.dart';
import '../../../response/other_settings_response.dart';
import '../../open_map/open_map.dart';
import '../chalets_page/chalets.dart';
import 'widgets/add_chalet_button.dart';

class AddChalet extends StatefulWidget {
  final CategoryModel? chalet;

  const AddChalet({super.key, this.chalet});

  @override
  _AddChalet createState() => _AddChalet();
}

class _AddChalet extends State<AddChalet> {
  bool switchOn1 = false;
  bool switchOn2 = false;
  final _formKey = GlobalKey<FormState>();
  String? _arabicChaletName;
  String? _englishChaletName;
  String? _arabicDescription;
  String? _englishDescription;
  String? _startPrice;

  // String? _endPrice;
  // OtherSettingsModel? _features;
  OtherSettingsModel? _location;
  File? _mainVideo;
  List<File>? _reels;
  List<File>? _images;
  String? _phone;
  String? _website;
  String? _instagram;
  Set<Marker>? _marker;
  bool _isLoading = false;
  String? greviewlink;
  String? greviewName;

  @override
  void initState() {
    // othersettingsbloc.getAdminFeatures('chalets');
    othersettingsbloc.getLocations();
    othersettingsbloc.gettypes(0, 1000, "", AppConstants.chaletsId.toString());

    if (widget.chalet != null) {
      final chalet = widget.chalet!;

      _arabicChaletName = chalet.nameAr;
      _englishChaletName = chalet.nameEn;
      _arabicDescription = chalet.descriptionAr;
      _englishDescription = chalet.descriptionEn;
      _startPrice = chalet.startPrice.toString();
      _phone = chalet.phone;
      _website = chalet.website;
      _instagram = chalet.instagram;
      _marker = {
        Marker(
            markerId: const MarkerId('1'),
            position: LatLng(chalet.lat!, chalet.lng!))
      };
      greviewlink = chalet.googleReviewLink;
      greviewName = chalet.googleReviewName;

      switchOn1 = chalet.featuredHome == 1 ? true : false;

      switchOn2 = chalet.featuredCategory == 1 ? true : false;

      _location = OtherSettingsModel(
          id: chalet.location?.id,
          name: chalet.location?.name,
          category: chalet.category);

      _type = OtherSettingsModel(
          id: chalet.typeId, name: chalet.type, category: chalet.category);
    }

    super.initState();
  }

  OtherSettingsModel? _type;

  get isEdit => widget.chalet != null;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(
          !isEdit ? S.of(context).AddNewChalet : S.of(context).Edit,
        ),
      ),
      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).Basicinformation,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).chaletara,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 20, right: 20, top: 15, bottom: 15),
                    child: TextFormField(
                      initialValue: widget.chalet?.nameAr,
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: S.of(context).chaletara,
                          hintStyle: const TextStyle(
                              color: Color(0xffB7B7B7), fontSize: 14)),
                      validator: qValidator([IsRequired()]),
                      onSaved: (value) => _arabicChaletName = value,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).chaleteng,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: widget.chalet?.nameEn,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).chaleteng,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _englishChaletName = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).desara,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 120,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      child: TextFormField(
                        initialValue: widget.chalet?.descriptionAr,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).desara,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _arabicDescription = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).deaen,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 120,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      child: TextFormField(
                        initialValue: widget.chalet?.descriptionEn,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).deaen,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        validator: qValidator([IsRequired()]),
                        onSaved: (value) => _englishDescription = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).starting,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Row(children: [
                  Expanded(
                      child: Container(
                          height: 50,
                          // width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: Colors.white,
                            border: Border.all(width: 0.5, color: Colors.grey),
                          ),
                          child: Row(
                            children: [
                              const CurrencyContainer(),
                              Container(
                                width: 1,
                                height: 30,
                                color: Colors.grey,
                              ),
                              Expanded(
                                child: Container(
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 10),
                                    height: 50,
                                    width: 75,
                                    child: TextFormField(
                                      initialValue:
                                          widget.chalet?.startPrice.toString(),
                                      decoration: InputDecoration(
                                          border: InputBorder.none,
                                          hintText: S.of(context).Price,
                                          hintStyle: const TextStyle(
                                              color: Color(0xffB7B7B7),
                                              fontSize: 14)),
                                      validator: qValidator([IsRequired()]),
                                      onSaved: (value) => _startPrice = value,
                                      keyboardType: TextInputType.number,
                                    )),
                              )
                            ],
                          ))),

                  // const SizedBox(
                  //   width: 10,
                  // ),
                  // Expanded(
                  //     child: Container(
                  //         height: 50,
                  //         // width: MediaQuery.of(context).size.width,
                  //         decoration: BoxDecoration(
                  //           borderRadius: BorderRadius.circular(10),
                  //           color: Colors.white,
                  //           border: Border.all(
                  //               width: 0.5, color: Colors.grey),
                  //         ),
                  //         child: Row(
                  //           children: [
                  //             const CurrencyContainer(),
                  //             Container(
                  //               width: 1,
                  //               height: 30,
                  //               color: Colors.grey,
                  //             ),
                  //             Expanded(
                  //               child: Container(
                  //                   padding: const EdgeInsets.only(
                  //                       left: 10, right: 10),
                  //                   height: 50,
                  //                   width: 75,
                  //                   child: TextFormField(
                  //                     decoration: InputDecoration(
                  //                         border: InputBorder.none,
                  //                         hintText: S.of(context).To,
                  //                         hintStyle: const TextStyle(
                  //                             color: Color(0xffB7B7B7),
                  //                             fontSize: 14)),
                  //                     validator: qValidator([IsRequired()]),
                  //                     onSaved: (value) => _endPrice = value,
                  //                     keyboardType: TextInputType.number,
                  //                   )),
                  //             )
                  //           ],
                  //         ))),
                ]),
                const SizedBox(height: 20),

                StreamBuilder<OtherSettingsResponse?>(
                    stream: othersettingsbloc.types.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.results.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              S.of(context).Types,
                              style: const TextStyle(fontSize: 13),
                            ),
                            const SizedBox(height: 10),
                            Container(
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(
                                        width: 0.5, color: Colors.grey),
                                    color: Colors.white),
                                height: 50,
                                child:
                                    DropdownButtonFormField<OtherSettingsModel>(
                                  isExpanded: true,
                                  hint: Text(
                                    S.of(context).Type,
                                    style: const TextStyle(
                                        color: Color(0xffB7B7B7)),
                                  ),
                                  onSaved: (value) => _type = value,
                                  value: snapshot.data!.results.firstWhere(
                                      (element) => element.id == _type?.id,
                                      orElse: () => snapshot.data!.results[0]),
                                  // snapshot.data!.results.first,
                                  iconEnabledColor: Colors.black,
                                  items: snapshot.data!.results
                                      .map((OtherSettingsModel value) {
                                    return DropdownMenuItem<OtherSettingsModel>(
                                      value: value,
                                      child: Padding(
                                        padding:
                                            const EdgeInsetsDirectional.only(
                                                start: 10.0),
                                        child: Padding(
                                          padding:
                                              const EdgeInsetsDirectional.only(
                                                  start: 10.0),
                                          child: Text(
                                            '${value.name}  ',
                                            style:
                                                const TextStyle(fontSize: 16),
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (_) {},
                                )),
                          ],
                        );
                      }
                      return const Center(child: CircularProgressIndicator());
                    }),
                // const SizedBox(height: 20),
                // Text(
                //   S.of(context).Features,
                //   style: const TextStyle(fontSize: 13),
                // ),
                // const SizedBox(height: 10),
                // StreamBuilder<OtherSettingsResponse?>(
                //   stream: othersettingsbloc.adminFeatures.stream,
                //   builder: (BuildContext context,
                //       AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                //     if (snapshot.hasData &&
                //         snapshot.connectionState != ConnectionState.waiting) {
                //       if (snapshot.data!.code != 1) {
                //         snackbar(snapshot.data!.msg!);
                //         return const SizedBox();
                //       }
                //       if (snapshot.data!.results.isEmpty) {
                //         return Text(S.of(context).Therearenoitems);
                //       }
                //       return StatefulBuilder(
                //         builder: (BuildContext context, setStateF) {
                //           return SingleChildScrollView(
                //             scrollDirection: Axis.horizontal,
                //             child: Row(
                //               children: [
                //                 for (var item in snapshot.data!.results) ...[
                //                   InkWell(
                //                     onTap: () {
                //                       if (f.contains(item.id)) {
                //                         f.remove(item.id);
                //                       } else {
                //                         f.add(item.id!);
                //                       }
                //                       setStateF(() {});
                //                     },
                //                     child: Card(
                //                       color: f.contains(item.id)
                //                           ? Colors.blue
                //                           : Colors.white,
                //                       child: Container(
                //                         height: 30,
                //                         padding: const EdgeInsets.symmetric(
                //                             horizontal: 10),
                //                         child: Center(
                //                             child: Text(
                //                           item.name!,
                //                           style: TextStyle(
                //                               color: f.contains(item.id)
                //                                   ? Colors.white
                //                                   : Colors.black),
                //                         )),
                //                       ),
                //                     ),
                //                   ),
                //                 ]
                //               ],
                //             ),
                //           );
                //         },
                //       );
                //     }
                //     return const Center(child: CircularProgressIndicator());
                //   },
                // ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).Locationation,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 10),
                StreamBuilder<OtherSettingsResponse?>(
                    stream: othersettingsbloc.locations.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<OtherSettingsResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.code != 1) {
                          snackbar(snapshot.data!.msg!);
                          return const SizedBox();
                        }
                        if (snapshot.data!.results.isEmpty) {
                          return Text(S.of(context).Therearenoitems);
                        }
                        return Container(
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border:
                                    Border.all(width: 0.5, color: Colors.grey),
                                color: Colors.white),
                            height: 50,
                            child: DropdownButtonFormField<OtherSettingsModel>(
                              isExpanded: true,
                              hint: Text(
                                S.of(context).Location,
                                style:
                                    const TextStyle(color: Color(0xffB7B7B7)),
                              ),
                              onSaved: (value) => _location = value,
                              value: snapshot.data!.results.firstWhere(
                                  (element) => element.id == _location?.id,
                                  orElse: () => snapshot.data!.results[0]),
                              iconEnabledColor: Colors.black,
                              items: snapshot.data!.results
                                  .map((OtherSettingsModel value) {
                                _location ??= value;
                                return DropdownMenuItem<OtherSettingsModel>(
                                  value: value,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.only(
                                        start: 10.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.only(
                                          start: 10.0),
                                      child: Text(
                                        '${value.name}  ',
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (_) {},
                            ));
                      }
                      return const Center(child: CircularProgressIndicator());
                    }),
                const SizedBox(
                  height: 20,
                ),
                Center(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (BuildContext context) => OpenMap(
                            onSave: (markers) {
                              setState(() {
                                _marker = markers;
                              });
                            },
                          ),
                        ),
                      );
                    },
                    child: Text(S.of(context).SetLocationonmap),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).MediaUpload,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).UploadMainVideo,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  title: S.of(context).Tabheretouploadmainvideo,
                  onSingleFileSelected: (video) => _mainVideo = video,
                  isMultiple: false,
                  type: FileType.video,
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 15, bottom: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context).Assignasfeaturedvideoinhomepage,
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 13)),
                          Switch(
                            inactiveTrackColor: Colors.grey[200],
                            activeColor: Colors.grey[200],
                            activeTrackColor: const Color(0xff556477),
                            onChanged: (value) {
                              setState(() {
                                switchOn1 = !switchOn1;
                              });
                            },
                            value: switchOn1,
                          ),
                        ],
                      )),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.only(
                          left: 10, right: 10, top: 15, bottom: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(S.of(context).Assignasfeaturedvideoincatpage,
                              style: const TextStyle(
                                  color: Colors.grey, fontSize: 13)),
                          Switch(
                            inactiveTrackColor: Colors.grey[200],
                            activeColor: Colors.grey[200],
                            activeTrackColor: const Color(0xff556477),
                            onChanged: (value) {
                              setState(() {
                                switchOn2 = !switchOn2;
                              });
                            },
                            value: switchOn2,
                          ),
                        ],
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).UploadReel,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  onFilesSelected: (videos) => _reels = videos,
                  title: S.of(context).Tabheretouploadreel,
                  type: FileType.video,
                ),
                const SizedBox(
                  height: 20,
                ),
                const Text(
                  "Upload Cover",
                  style: TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                ADFilePicker(
                  onSingleFileSelected: (images) => _images = [images],
                  title: S.of(context).Tabheretouploadimage,
                  type: FileType.media,
                  isMultiple: false,
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.grey),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Text(
                        S.of(context).ExtraInformation,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).GoogleReviewName,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: widget.chalet?.googleReviewName,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).GoogleReviewName,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => greviewName = value,
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  S.of(context).GoogleReviewLink,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: widget.chalet?.googleReviewLink,
                        onSaved: (value) => greviewlink = value,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).GoogleReviewLink,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).PhoneNumber,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: widget.chalet?.phone,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).PhoneNumber,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        keyboardType: TextInputType.number,
                        onSaved: (value) => _phone = value,
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).website,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: widget.chalet?.website,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).website,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _website = value,
                      )),
                ),
                const SizedBox(height: 20),
                Text(
                  S.of(context).insta,
                  style: const TextStyle(fontSize: 13),
                ),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(width: 0.5, color: Colors.grey),
                      color: Colors.white),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        initialValue: widget.chalet?.instagram,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: S.of(context).insta,
                            hintStyle: const TextStyle(
                                color: Color(0xffB7B7B7), fontSize: 14)),
                        onSaved: (value) => _instagram = value,
                      )),
                ),
                const SizedBox(height: 20),
                _isLoading
                    ? const ADLinearProgressIndicator()
                    : AddChaletButton(
                        isEdit: isEdit,
                        onTap: () async {
                          if (_formKey.currentState!.validate()) {
                            if (_images == null && !isEdit) {
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(SnackBar(
                                content: Text(S.of(context).selectimg),
                              ));
                            }
                            if (_mainVideo == null && !isEdit) {
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(SnackBar(
                                content: Text(S.of(context).selectvid),
                              ));
                            } else {
                              _formKey.currentState!.save();

                              if (_marker == null || _location == null) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text(S.of(context).fill),
                                ));
                              } else {
                                FormData form = FormData.fromMap({
                                  if (isEdit) "id": widget.chalet!.id,
                                  'name[en]': _englishChaletName,
                                  "name[ar]": _arabicChaletName,
                                  "description[en]": _englishDescription,
                                  "description[ar]": _arabicDescription,
                                  "start_price": _startPrice,
                                  "end_price": 0,
                                  "location_id": _location!.id,
                                  "features[]": [],
                                  "currency": 'AED',
                                  "endpricecurrency": 'AED',
                                  "featuredCategory": switchOn2 ? 1 : 0,
                                  "featuredHome": switchOn1 ? 1 : 0,
                                  "latitude": _marker != null
                                      ? _marker!.first.position.latitude
                                      : 33.5252252,
                                  "longitude": _marker != null
                                      ? _marker!.first.position.longitude
                                      : 36.34333434,
                                  "phone": _phone,
                                  "website": _website,
                                  "instagram": _instagram,
                                  "type_id": _type?.id,
                                  "category_id": AppConstants.chaletsId,
                                  "review_name": greviewName,
                                  "review_link": greviewlink,
                                });
                                if (_mainVideo != null) {
                                  form.files.add(MapEntry(
                                    "video",
                                    await MultipartFile.fromFile(
                                        _mainVideo!.path),
                                  ));
                                }
                                if (_images != null) {
                                  for (int i = 0; i < _images!.length; i++) {
                                    form.files.add(MapEntry(
                                      "image[]",
                                      await MultipartFile.fromFile(
                                          _images![i].path),
                                    ));
                                  }
                                }
                                if (_reels != null) {
                                  for (int i = 0; i < _reels!.length; i++) {
                                    form.files.add(MapEntry(
                                      "reels[]",
                                      await MultipartFile.fromFile(
                                          _reels![i].path),
                                    ));
                                  }
                                }
                                setState(() {
                                  _isLoading = true;
                                });
                                var response;

                                if (isEdit) {
                                  response = await await ChaletsRepository()
                                      .editChalet(form);
                                } else {
                                  response =
                                      await ChaletsRepository().addChalet(form);
                                }

                                if (response.code == -1) {
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(SnackBar(
                                          backgroundColor: Colors.red,
                                          content: Text(
                                            response.msg ?? '',
                                            style: const TextStyle(
                                                color: Colors.white),
                                          )));
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                          backgroundColor: Colors.green,
                                          content: Text(S.of(context).added)));
                                  Navigator.pop(context);
                                  Navigator.pushReplacement(
                                      context,
                                      MaterialPageRoute(
                                          builder: (BuildContext context) =>
                                              const Chalets()));
                                }
                                setState(() {
                                  _isLoading = false;
                                });
                              }
                            }
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                backgroundColor: Colors.red,
                                content: Text(
                                  S.of(context).fill,
                                  style: const TextStyle(color: Colors.white),
                                )));
                          }
                        },
                      )
              ],
            ),
          ),
        ),
      ),
    ));
  }
}
