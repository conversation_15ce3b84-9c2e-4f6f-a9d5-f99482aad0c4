import 'package:admin_dubai/src/features/views/chalets/chalets_page/chalets.dart';
import 'package:flutter/material.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../repository/hotels_reposotiry.dart';

void deleteChalets(
  id,
  index, {
  required BuildContext context,
  required bool isLoading,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.40,
                decoration: BoxDecoration(
                    color: const Color(0xffF5F6F7),
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: Column(
                  children: [
                    const SizedBox(height: 10),
                    Container(
                        height: 5, width: 50, color: const Color(0xffD2D4D6)),
                    const SizedBox(height: 20),
                    Center(
                        child: Text(
                      S.of(context).DeleteChalet,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    )),
                    Container(
                      padding: const EdgeInsets.all(15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 20),
                              Text(S
                                  .of(context)
                                  .Areyousureyouwanttodeletethischaletifyesyouwontbeabletoseeagain),
                              const SizedBox(height: 20),
                              StatefulBuilder(
                                builder: (BuildContext context, refrechState) {
                                  return Center(
                                    child: GestureDetector(
                                      onTap: () async {
                                        refrechState(() {
                                          isLoading = true;
                                        });

                                        var response = await HotelsRepository()
                                            .removeHotel(id);
                                        print(response.code);
                                        if (response.code == 1) {
                                          Navigator.pop(context);

                                          Navigator.pushReplacement(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (_) =>
                                                      const Chalets()));

                                          snackbar(response.msg ?? '');
                                          // CategoryResponse? categoryResponse =
                                          //     categoryBloc.subject.value;
                                          // categoryResponse!.category
                                          //     .removeAt(index);
                                          // categoryBloc.subject.sink
                                          //     .add(categoryResponse);
                                          // Navigator.pop(context);
                                        } else {
                                          Navigator.pop(context);
                                          snackbar(response.msg ?? '');
                                        }
                                        refrechState(() {
                                          isLoading = false;
                                        });
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: const Color(0xffE04E4D),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                          padding: const EdgeInsets.all(10),
                                          child: Center(
                                            child: isLoading
                                                ? const ADLinearProgressIndicator()
                                                : Text(
                                                    S
                                                        .of(context)
                                                        .yesDeletechalet,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                )),
          ));
}
