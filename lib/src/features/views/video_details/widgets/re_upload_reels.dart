import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

import '../../../../core/utils/resources.dart';

void reuploadreels(BuildContext context) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.40,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                const Center(
                    child: Text(
                  'Re-Upload Reel Video',
                  style: TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  width: MediaQuery.of(context).size.width,
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 10,
                          ),
                          const Text(
                            'Re-Upload Reel Video',
                            style: TextStyle(fontSize: 13),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          DottedBorder(
                            color: const Color(0xffEFEFEF),
                            strokeWidth: 1,
                            child: Container(
                              height: 90,
                              decoration: const BoxDecoration(),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Center(
                                      child: Container(
                                    height: 30,
                                    width: 30,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(20),
                                        color: const Color(0xffB7B7B7)
                                            .withOpacity(0.2)),
                                    child: const Center(
                                      child: Icon(
                                        Icons.file_upload,
                                        color: Colors.black12,
                                      ),
                                    ),
                                  )),
                                  const Text(
                                    'Tab here to reel video',
                                    style: TextStyle(
                                        color: Color(0xffB7B7B7), fontSize: 14),
                                  )
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                            height: 50,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                color: GlobalColors.primaryColor,
                                borderRadius: BorderRadius.circular(10)),
                            child: Container(
                                padding: const EdgeInsets.all(10),
                                child: const Center(
                                    child: Text(
                                  'Re-Upload Reel Video',
                                  style: TextStyle(color: Colors.white),
                                ))),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
          )));
}
