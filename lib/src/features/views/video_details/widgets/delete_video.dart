import 'package:admin_dubai/src/features/repository/hotels_reposotiry.dart';
import 'package:admin_dubai/src/features/views/account/account.dart';
import 'package:flutter/material.dart';

import '../../../../../generated/l10n.dart';

void deletevideo(
  BuildContext context, {
  required int? id,
}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.3,
                decoration: BoxDecoration(
                    color: const Color(0xffF5F6F7),
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        height: 5, width: 50, color: const Color(0xffD2D4D6)),
                    const SizedBox(
                      height: 20,
                    ),
                    Center(
                        child: Text(
                      S.of(context).DeleteReelVideo,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    )),
                    Container(
                      padding: const EdgeInsets.all(15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(
                                height: 20,
                              ),
                              Text(S
                                  .of(context)
                                  .Areyousureyouwanttodeletethisreelvideo),
                              const SizedBox(
                                height: 20,
                              ),
                              GestureDetector(
                                onTap: () async {
                                  var response =
                                      await HotelsRepository().removeHotel(id);
                                  print(response.code);

                                  Navigator.pop(context);

                                  Navigator.pushReplacement(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              const Account()));
                                },
                                child: Center(
                                    child: Container(
                                  height: 50,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                      color: const Color(0xffE04E4D),
                                      borderRadius: BorderRadius.circular(10)),
                                  child: Container(
                                      padding: const EdgeInsets.all(10),
                                      child: Center(
                                          child: Text(
                                        S.of(context).yesde,
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ))),
                                )),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                )),
          ));
}
