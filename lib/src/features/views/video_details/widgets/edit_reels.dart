import 'dart:io';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../../core/utils/resources.dart';
import '../../../bloc/category_bloc.dart';
import '../../../repository/hotels_reposotiry.dart';
import '../../../response/category_reels_response.dart';
import '../video_details.dart';

class EditReels extends StatefulWidget {
  final int id;
  final categoryName;
  final String? image;

  const EditReels({Key? key, required this.id, this.categoryName, this.image})
      : super(key: key);

  @override
  _EditReels createState() => _EditReels();
}

class _EditReels extends State<EditReels> {
  TextEditingController searchController = TextEditingController();

  String? currentvalue2;
  String? currentvalue3;
  String? currentvalue4;
  String? currentvalue5;
  String? currentvalue6;
  String? currentvalue7;
  bool isLoading = false;
  List<File> _videos = [];
  TextEditingController startdateController = TextEditingController();
  TextEditingController enddateController = TextEditingController();
  // List<String> locations = ['Dubai', 'Abu Dhabi'];
  // List<String> numberofroom = ['1', '2', '3'];
  // List<String> propreytype = [
  //   'Property Type1',
  //   'Property Type2',
  //   'Property Type3'
  // ];
  // List<String> feature = ['feature', 'Property Type2', 'Property Type3'];
  // List<String> size = ['large', 'small'];
  // List<String> agentname = ['Ahmad', 'Mohmmad'];

  void uploadreels(
    BuildContext context,
  ) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
              height: MediaQuery.of(context).size.height * 0.40,
              decoration: BoxDecoration(
                  color: const Color(0xffF5F6F7),
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25.0),
                      topRight: Radius.circular(25.0)),
                  border: Border.all(color: Colors.black, width: 1.0)),
              child: SingleChildScrollView(
                  child: Column(
                children: [
                  const SizedBox(
                    height: 10,
                  ),
                  Container(
                      height: 5, width: 50, color: const Color(0xffD2D4D6)),
                  const SizedBox(
                    height: 20,
                  ),
                  Center(
                      child: Text(
                    S.of(context).Uploadreelvideos,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  )),
                  Container(
                    width: MediaQuery.of(context).size.width,
                    padding: const EdgeInsets.all(15),
                    child: Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10)),
                      child: Container(
                        padding: const EdgeInsets.all(15),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(
                              height: 10,
                            ),
                            Text(
                              S.of(context).Uploadreelvideos,
                              style: const TextStyle(fontSize: 13),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            ADFilePicker(
                              title: S.of(context).Tabheretouploadmainvideo,
                              onFilesSelected: (videos) {
                                _videos = videos;
                              },
                              isMultiple: true,
                              type: FileType.video,
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            StatefulBuilder(
                              builder: (BuildContext context, refrechState) {
                                return Center(
                                  child: Container(
                                    child: GestureDetector(
                                      onTap: () async {
                                        print(_videos[0].path);
                                        refrechState(() {
                                          isLoading = true;
                                        });
                                        FormData form;
                                        print(
                                            "================ ${widget.categoryName} =============");

                                        form = FormData.fromMap({
                                          "video_id": widget.id.toString(),
                                        });
                                        print("length");
                                        print(_videos.length);
                                        if (_videos != null) {
                                          for (int i = 0;
                                              i < _videos.length;
                                              i++) {
                                            form.files.add(
                                              MapEntry(
                                                "reels[]",
                                                await MultipartFile.fromFile(
                                                    _videos[0].path),
                                              ),
                                            );
                                          }
                                        }
                                        var response = await HotelsRepository()
                                            .uploadCategoryVideo(
                                          form,
                                        );
                                        if (response.code == 1) {
                                          categoryBloc.getMainCategoryReels(
                                            widget.id,
                                          );
                                          Navigator.pop(context);
                                        } else {
                                          Navigator.pop(context);
                                          snackbar(response.msg ?? '');
                                        }
                                        refrechState(() {
                                          isLoading = false;
                                        });
                                        _videos.clear();
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: GlobalColors.primaryColor,
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                          padding: const EdgeInsets.all(10),
                                          child: Center(
                                            child: isLoading
                                                ? const ADLinearProgressIndicator()
                                                : Text(
                                                    S
                                                        .of(context)
                                                        .Uploadreelvideos,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              )),
            )));
  }

  @override
  void initState() {
    categoryBloc.subject4.sink.add(null);
    categoryBloc.getMainCategoryReels(widget.id);
    super.initState();
  }

  Widget _buildCategoryWidget() {
    return StreamBuilder<CategoryReelsResponse?>(
        stream: categoryBloc.subject4.stream,
        builder: (context, snapshot) {
          if (snapshot.hasData &&
              snapshot.connectionState != ConnectionState.waiting) {
            if (snapshot.data!.code != 1) {
              snackbar(snapshot.data!.msg ?? '');
              return const SizedBox();
            }
            if (snapshot.data!.reels!.isEmpty) {
              return const Center(
                child: Text("There are no reels yet"),
              );
            }
            return MasonryGridView.count(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(
                    horizontal: 10.0, vertical: 10.0),
                crossAxisCount: 2,
                //  physics: NeverScrollableScrollPhysics(),
                // crossAxisSpacing: 5,
                mainAxisSpacing: 8,
                itemCount: snapshot.data!.reels!.length,
                primary: false,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (BuildContext context) => ChewieDemo(
                                  id: widget.id,
                                  name: snapshot.data!.reels![index].title,
                                  video: snapshot.data!.reels![index].video,
                                ))),
                    child: Column(children: [
                      const SizedBox(
                        width: 5,
                      ),
                      Stack(children: [
                        Container(
                            child: Container(
                                child: ClipRRect(
                          borderRadius: BorderRadius.circular(5),
                          child: Image.network(
                            '${widget.image}',
                            height: 288,
                            width: MediaQuery.of(context).size.width * 0.45,
                            fit: BoxFit.fill,
                          ),
                        ))),
                        Container(
                          height: 288,
                          width: MediaQuery.of(context).size.width * 0.45,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.white.withOpacity(0.5),
                                    Colors.black.withOpacity(0.5),
                                  ])),
                        ),
                        Positioned(
                          top: 10,
                          right: 10,
                          child: InkWell(
                            child: Container(
                              height: 25,
                              width: 25,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(25),
                                  color:
                                      const Color(0xff233549).withOpacity(0.2)),
                              child: Center(
                                child: Icon(
                                  Icons.delete_outline,
                                  color: Colors.white.withOpacity(0.5),
                                  size: 16,
                                ),
                              ),
                            ),
                            onTap: () {
                              deleteVideo(
                                  snapshot.data!.reels![index].id, index);
                            },
                          ),
                        ),
                        Positioned(
                            bottom: 12,
                            left: 10,
                            child: Column(
                              children: [
                                Text(
                                  snapshot.data!.reels![index].createat != null
                                      ? S.of(context).addedon +
                                          ' ${snapshot.data!.reels![index].createat}'
                                      : "",
                                  style: const TextStyle(
                                      color: Colors.white, fontSize: 9),
                                )
                              ],
                            ))
                      ])
                    ]),
                  );
                });
          }
          return const ADCircularProgressIndicator();
        });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).EditReelvideos),
      ),
      body: SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const SizedBox(
            height: 20,
          ),
          _buildCategoryWidget(),
          const SizedBox(
            height: 20,
          )
        ]),
      ),
      bottomNavigationBar: Container(
          padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
          // padding: EdgeInsets.only(right: 20, left: 20),
          child: GestureDetector(
              onTap: () async {
                uploadreels(context);
              },
              child: Container(
                height: 50,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                    color: GlobalColors.primaryColor,
                    borderRadius: BorderRadius.circular(5)),
                child: Container(
                    padding: const EdgeInsets.all(10),
                    child: Center(
                        child: Text(
                      S.of(context).Uploadreelvideos,
                      style: const TextStyle(color: Colors.white),
                    ))),
              ))),
    ));
  }

  void deleteVideo(id, index) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.4,
          decoration: BoxDecoration(
              color: const Color(0xffF5F6F7),
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25.0),
                  topRight: Radius.circular(25.0)),
              border: Border.all(color: Colors.black, width: 1.0)),
          child: Column(
            children: [
              const SizedBox(
                height: 10,
              ),
              Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
              const SizedBox(
                height: 20,
              ),
              Center(
                  child: Text(
                S.of(context).deletev,
                style: const TextStyle(fontWeight: FontWeight.bold),
              )),
              Container(
                padding: const EdgeInsets.all(15),
                child: Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10)),
                  child: Container(
                    padding: const EdgeInsets.all(15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(
                          height: 20,
                        ),
                        Text(S.of(context).delemsgv),
                        const SizedBox(
                          height: 20,
                        ),
                        StatefulBuilder(
                          builder: (BuildContext context, refrechState) {
                            return Center(
                              child: Container(
                                child: GestureDetector(
                                  onTap: () async {
                                    refrechState(() {
                                      isLoading = true;
                                    });

                                    var response = await HotelsRepository()
                                        .deleteVideo(id, widget.categoryName);
                                    print(response.code);
                                    if (response.code == 1) {
                                      CategoryReelsResponse
                                          categoryReelsResponse =
                                          categoryBloc.subject4.value!;
                                      categoryReelsResponse.reels!
                                          .removeAt(index);
                                      categoryBloc.subject4.sink
                                          .add(categoryReelsResponse);
                                      Navigator.pop(context);
                                    } else {
                                      Navigator.pop(context);
                                      snackbar(response.msg ?? '');
                                    }
                                    refrechState(() {
                                      isLoading = false;
                                    });
                                  },
                                  child: Container(
                                    height: 50,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                        color: const Color(0xffE04E4D),
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    child: Container(
                                      padding: const EdgeInsets.all(10),
                                      child: Center(
                                        child: isLoading
                                            ? const ADLinearProgressIndicator()
                                            : Text(
                                                S.of(context).DeleteReelVideo,
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                ),
                                              ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
