import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:video_player/video_player.dart';

import '../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../core/theme/ad_colors.dart';
import '../../bloc/discussions_bloc.dart';
import '../../models/category_model.dart';
import '../albums_screen/albums_screen.dart';
import 'widgets/delete_video.dart';
import 'widgets/discussion.dart';
import 'widgets/edit_reels.dart';

TextEditingController commentController = TextEditingController();
List<TextEditingController> replyControllers = [];

// ignore: must_be_immutable
class ChewieDemo extends StatefulWidget {
  final int? id;
  final String? categoryName;
  final bool? withDetails;
  final String? video;
  final String? name;
  final String? image;

  const ChewieDemo(
      {super.key,
      required this.id,
      this.withDetails = false,
      this.categoryName,
      this.video,
      this.name,
      this.image});
  @override
  State<StatefulWidget> createState() {
    return _ChewieDemoState();
  }
}

class _ChewieDemoState extends State<ChewieDemo> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  bool isLoading = false;

  late VideoPlayerController _controller;

  CategoryDetails? _categoryDetails;
  @override
  void initState() {
    discussionsBloc.discussionStreamController.sink.add(null);

    print(widget.categoryName);
    // if (widget.categoryName != "Areas") {
    //   categoryBloc
    //       .getCategoryDetails(widget.id!, widget.categoryName)
    //       .then((value) {
    //     _categoryDetails = value;
    //     print(_categoryDetails?.description ?? "");
    //     print(_categoryDetails!.video);
    //     _controller =
    //         VideoPlayerController.network('${_categoryDetails!.video}')
    //           ..initialize().then((_) {
    //             _controller.play();
    //             // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
    //             setState(() {});
    //           });
    //   });
    // } else {
    _categoryDetails = CategoryDetails.fromJson({});
    _controller =
        VideoPlayerController.network(Uri.parse(widget.video ?? '').toString())
          ..initialize().then((_) {
            _controller.play();
            // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
            setState(() {});
          });
    // }

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  int editCommentId = -1;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      key: _scaffoldKey,
      body: _categoryDetails != null
          ? Stack(
              alignment: Alignment.bottomCenter,
              children: <Widget>[
                Container(
                    child: Container(
                        height: MediaQuery.of(context).size.height,
                        width: MediaQuery.of(context).size.width,
                        child: _controller.value.isInitialized
                            ? FittedBox(
                                fit: BoxFit.cover,
                                child: SizedBox(
                                    height: _controller.value.size.height,
                                    width: _controller.value.size.width,
                                    child: VideoPlayer(_controller)))
                            : Container())),
                Positioned(
                    left: 10,
                    right: 10,
                    top: 20,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          icon:
                              const Icon(Icons.arrow_back, color: Colors.white),
                          onPressed: () => Navigator.pop(context),
                        ),
                        IconButton(
                            onPressed: () {
                              _controller.value.volume > 0
                                  ? _controller.setVolume(0)
                                  : _controller.setVolume(0.5);
                              setState(() {});
                            },
                            icon: Icon(
                              _controller.value.volume > 0
                                  ? Icons.volume_up
                                  : Icons.volume_off,
                              color: Colors.white,
                            ))
                      ],
                    )),
                PositionedDirectional(
                    bottom: 100,
                    start: 16,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.name ?? (_categoryDetails!.name ?? ""),
                          style: const TextStyle(color: Colors.white),
                        ),
                        const SizedBox(height: 5),
                        if (widget.withDetails!)
                          Text(_categoryDetails!.description ?? '',
                              style: const TextStyle(color: Colors.white),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis),
                      ],
                    )),
                if (widget.withDetails!)
                  PositionedDirectional(
                      bottom: 130,
                      end: 12,
                      child: Column(
                        children: [
                          IconButton(
                              onPressed: () => _controller
                                  .seekTo(const Duration(seconds: 0)),
                              icon: SvgPicture.asset(
                                'assets/Group 6258.svg',
                                height: 17,
                                width: 10,
                              )),
                          IconButton(
                              onPressed: () {
                                showCupertinoModalPopup<void>(
                                  context: context,
                                  builder: (BuildContext context) =>
                                      CupertinoActionSheet(
                                    actions: <CupertinoActionSheetAction>[
                                      CupertinoActionSheetAction(
                                        child: Text(S.of(context).Editimages),
                                        onPressed: () {
                                          _controller.pause();
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (BuildContext context) =>
                                                  AlbumsScreen(
                                                      id: widget.id!,
                                                      categoryName:
                                                          widget.categoryName),
                                            ),
                                          );
                                        },
                                      ),
                                      CupertinoActionSheetAction(
                                        child:
                                            Text(S.of(context).EditReelvideos),
                                        onPressed: () {
                                          print(widget.image);
                                          _controller.pause();
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (BuildContext context) =>
                                                  EditReels(
                                                id: widget.id!,
                                                image: widget.image,
                                                categoryName:
                                                    widget.categoryName,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                      CupertinoActionSheetAction(
                                        child:
                                            Text(S.of(context).ViewDiscussion),
                                        onPressed: () {
                                          _controller.pause();
                                          discussion(context,
                                              id: widget.id,
                                              categoryName: widget.categoryName,
                                              isLoading: isLoading,
                                              editCommentId: editCommentId);
                                        },
                                      ),
                                      CupertinoActionSheetAction(
                                        child: Text(
                                          S.of(context).DeleteReelVideo,
                                          style: const TextStyle(
                                              color: CupertinoColors
                                                  .destructiveRed),
                                        ),
                                        onPressed: () {
                                          _controller.pause();
                                          deletevideo(
                                            context,
                                            id: widget.id,
                                          );
                                        },
                                      )
                                    ],
                                    cancelButton: CupertinoActionSheetAction(
                                      child: Text(S.of(context).Cancel),
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                    ),
                                  ),
                                );
                              },
                              icon: SvgPicture.asset(
                                'assets/Group 6507.svg',
                                height: 17,
                                width: 10,
                              )),
                        ],
                      )),
                Padding(
                  padding: const EdgeInsetsDirectional.only(
                      bottom: 44, top: 19, end: 16),
                  child: Row(
                    children: [
                      Expanded(
                        child: IconButton(
                            onPressed: () {
                              _controller.value.isPlaying
                                  ? _controller.pause()
                                  : _controller.play();
                              setState(() {});
                            },
                            icon: Icon(
                              _controller.value.isPlaying
                                  ? Icons.pause
                                  : Icons.play_arrow,
                              color: Colors.white,
                            )),
                      ),
                      Expanded(
                        flex: 8,
                        child: VideoProgressIndicator(
                          _controller,
                          allowScrubbing: true,
                          colors: VideoProgressColors(
                              playedColor: _controller.value.isInitialized
                                  ? Colors.white
                                  : ADColors.primary,
                              backgroundColor: Colors.white.withOpacity(0.44)),
                        ),
                      ),
                    ],
                  ),
                ),
                //FURTHER IMPLEMENTATION
              ],
            )
          : const ADCircularProgressIndicator(),
    ));
  }
}
