import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class WelcomeTextWithLogo extends StatelessWidget {
  const WelcomeTextWithLogo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Widget svg1 = SizedBox(
        width: 12,
        height: 14,
        child: SvgPicture.asset(
          'assets/Path 7024.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return Container(
        padding: const EdgeInsets.only(bottom: 40),
        child: Container(
            width: MediaQuery.of(context).size.width,
            height: 200,
            color: GlobalColors.primaryColor.withOpacity(.5),
            child: Container(
                padding: const EdgeInsets.only(left: 10, right: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // SizedBox(
                    //   height: 10,
                    // ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                S.of(context).WelcometoDashboard,
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width * 0.75,
                                  child: Text(
                                      S
                                          .of(context)
                                          .Manageallfeaturesfromoneplace,
                                      softWrap: true,
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontSize: 14,
                                      )))
                            ],
                          ),
                          const Spacer(),
                          svg1
                        ],
                      ),
                    ),
                  ],
                ))));
  }
}
