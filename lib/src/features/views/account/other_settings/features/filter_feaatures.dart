import 'dart:developer';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/bloc/category_bloc.dart';
import 'package:admin_dubai/src/features/models/main_category_model.dart';
import 'package:flutter/material.dart';

import '../../../../../core/utils/resources.dart';

int? _categoryIdSelected;

void filterFeatures(BuildContext context,
    {required categories, required categorySelected, getfeatures}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          StatefulBuilder(builder: (context, StateSetter stateSetter) {
            return StreamBuilder<List<MainCategoryModel>?>(
                stream: categoryBloc.mainCategoriesSubject.stream,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  if (snapshot.hasData && categories.isEmpty) {
                    categories
                        .add({'name': 'All Categories', 'isselected': true});
                    for (var item in snapshot.data!) {
                      categories.add({
                        'name': item.name,
                        'isselected': false,
                        'id': item.id
                      });
                    }
                  }
                  return Padding(
                      padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom),
                      child: Container(
                        height: MediaQuery.of(context).size.height * 0.60,
                        decoration: BoxDecoration(
                            color: const Color(0xffF5F6F7),
                            borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(25.0),
                                topRight: Radius.circular(25.0)),
                            border:
                                Border.all(color: Colors.black, width: 1.0)),
                        child: SingleChildScrollView(
                            child: Column(
                          children: [
                            const SizedBox(
                              height: 10,
                            ),
                            Container(
                                height: 5,
                                width: 50,
                                color: const Color(0xffD2D4D6)),
                            const SizedBox(
                              height: 20,
                            ),
                            Container(
                                padding:
                                    const EdgeInsets.only(left: 20, right: 20),
                                child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Container(
                                        height: 10,
                                        color: Colors.transparent,
                                      ),
                                      Text(
                                        S.of(context).Filter,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold),
                                      ),
                                      InkWell(
                                          onTap: () {
                                            stateSetter(() {
                                              // setState(() {
                                              for (var i = 0;
                                                  i < categories.length;
                                                  i++) {
                                                categories[i]['isselected'] =
                                                    false;
                                              }
                                              categorySelected =
                                                  "All Categories";
                                              _categoryIdSelected = null;
                                              // });
                                              Navigator.pop(context);
                                            });
                                          },
                                          child: Text(
                                            S.of(context).Reset,
                                            style: const TextStyle(
                                                color: Color(0xff51565B)),
                                          ))
                                    ])),
                            Container(
                              padding: const EdgeInsets.all(15),
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(10)),
                                child: Container(
                                  padding: const EdgeInsets.all(15),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      for (var i = 0;
                                          i < categories.length;
                                          i++)
                                        InkWell(
                                            onTap: () {
                                              stateSetter(() {
                                                for (var i = 0;
                                                    i < categories.length;
                                                    i++) {
                                                  categories[i]['isselected'] =
                                                      false;
                                                }
                                                categories[i]['isselected'] =
                                                    true;
                                                categorySelected =
                                                    categories[i]["name"];
                                                _categoryIdSelected =
                                                    categories[i]["id"];
                                              });
                                            },
                                            child: Column(
                                              children: [
                                                Container(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 20, right: 20),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Text(
                                                        categories[i]['name'],
                                                        style: TextStyle(
                                                            fontWeight: categories[
                                                                            i][
                                                                        'isselected'] ==
                                                                    true
                                                                ? FontWeight
                                                                    .bold
                                                                : FontWeight
                                                                    .normal),
                                                      ),
                                                      categories[i][
                                                                  'isselected'] ==
                                                              true
                                                          ? const Icon(
                                                              Icons.check,
                                                              color: Color(
                                                                  0xffD8B77F),
                                                            )
                                                          : Container()
                                                    ],
                                                  ),
                                                ),
                                                const SizedBox(
                                                  height: 5,
                                                ),
                                                const Divider(
                                                  color: Colors.black,
                                                )
                                              ],
                                            )),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      Center(
                                          child: InkWell(
                                              onTap: () async {
                                                // setState(() {});
                                                log('asdkasdn ${categorySelected}');

                                                Navigator.pop(context);

                                                await getfeatures(
                                                    '',
                                                    _categoryIdSelected
                                                        .toString());
                                              },
                                              child: Container(
                                                height: 50,
                                                width: MediaQuery.of(context)
                                                    .size
                                                    .width,
                                                decoration: BoxDecoration(
                                                    color: GlobalColors
                                                        .primaryColor,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            5)),
                                                child: Container(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            10),
                                                    child: Center(
                                                        child: Text(
                                                      S.of(context).ApplyFilter,
                                                      style: const TextStyle(
                                                          color: Colors.white),
                                                    ))),
                                              ))),
                                      const SizedBox(height: 20),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )),
                      ));
                });
          }));
}
