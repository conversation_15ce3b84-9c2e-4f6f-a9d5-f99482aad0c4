import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/bloc/category_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../models/other_settings.dart';
import '../../../../repository/other_settings_repoistory.dart';
import '../../../../response/other_settings_response.dart';
import 'add_feature.dart';
import 'edit_feature.dart';

class Features extends StatefulWidget {
  const Features({super.key});

  @override
  _Features createState() => _Features();
}

class _Features extends State<Features> {
  int pagenumber = 1;

  bool load = false;
  String valvheck = '';
  List<OtherSettingsModel> featureList = [];
  String categorySelected = "All Categories";
  @override
  void initState() {
    super.initState();
    categoryBloc.getMainCategories();
    getfeatures('');
  }

  getfeatures(String val, [String? cat]) async {
    setState(() {
      load = false;
    });
    final OtherSettingsRepository _repository = OtherSettingsRepository();
    featureList.clear();
    pagenumber = 1;
    OtherSettingsResponse res =
        await _repository.getfeatures(pagenumber, 1000, val, cat);
    print(res);

    if (res.code.toString() == '1') {
      setState(() {
        featureList.addAll(res.results);
        load = true;
      });
    } else {
      setState(() {
        load = true;

        //error = res.error.toString();
      });
    }
    for (var item in featureList) {
      print(item.category);
    }
  }

  List<Map> categories = [];

  TextEditingController searchController = TextEditingController();

  Widget listfeatures(List<OtherSettingsModel> data) {
    List<OtherSettingsModel> filtredList = [];
    filtredList.addAll(data);

    if (categorySelected != "All Categories") {
      filtredList = filtredList
          .where((element) => element.category?.name == categorySelected)
          .toList();
    }

    return filtredList.isNotEmpty
        ? Expanded(
            child: ListView.builder(
            shrinkWrap: true,
            itemBuilder: (BuildContext ctxt, int index) {
              print(filtredList[index].category);
              return GestureDetector(
                  onTap: () {},
                  child: Container(
                      padding: const EdgeInsets.only(
                          top: 10, bottom: 5, right: 20, left: 20),
                      child: GestureDetector(
                        onTap: () {
                          print(filtredList[index].category);
                          Navigator.of(context)
                              .push(
                            MaterialPageRoute(
                                builder: (_) => EditFeature(
                                    filtredList[index].name,
                                    filtredList[index].category,
                                    filtredList[index].id)),
                          )
                              .then((val) {
                            setState(() {
                              load = false;
                              filtredList.clear();
                              getfeatures('');
                            });
                          });

                          /*  Navigator.push(context,
                    MaterialPageRoute(builder: (context) {
                  return EditFeature(
                      featureList[index].name, featureList[index].category,featureList[index].id);
                }));*/
                        },
                        child: Container(
                            padding: const EdgeInsets.only(
                                top: 10, bottom: 10, right: 20, left: 20),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(color: Colors.grey[200]!)),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    filtredList[index].name != null
                                        ? Text(
                                            filtredList[index].name!,
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold),
                                          )
                                        : Container(),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    filtredList[index].category != null
                                        ? Text(
                                            filtredList[index].category!.name ??
                                                '',
                                            style: const TextStyle(
                                                color: Colors.grey),
                                          )
                                        : Container(),
                                  ],
                                ),
                                const Icon(Icons.keyboard_arrow_right)
                              ],
                            )
                            // padding: EdgeInsets.all(20),

                            ),
                      )));
            },
            itemCount: filtredList.length,
          ))
        : nodatafound('No Features to show');
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 20,
        height: 20.0,
        child: SvgPicture.asset(
          'assets/filter.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).Features),
        ),
        body: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const SizedBox(
            height: 20,
          ),
          Container(
              padding: const EdgeInsets.only(left: 20, right: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    S.of(context).AllFeatures,
                    style: const TextStyle(color: Color(0xff51565B)),
                  ),
                  // GestureDetector(
                  //   onTap: () {
                  //     filterFeatures(context,
                  //         categories: categories,
                  //         categorySelected: categorySelected,
                  //         getfeatures: getfeatures);
                  //   },
                  //   child: svg2,
                  // )
                ],
              )),
          const SizedBox(
            height: 10,
          ),
          Container(
              padding: const EdgeInsets.only(left: 20, right: 20),
              child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                      color: const Color(0xffF1F1F1),
                      borderRadius: BorderRadius.circular(3)),
                  child: Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(5)),
                      ),
                      child: TextFormField(
                        controller: searchController,
                        textInputAction: TextInputAction.search,
                        onFieldSubmitted: (value) {
                          setState(() {
                            load == false;
                            getfeatures(value);
                          });
                        },
                        decoration: InputDecoration(
                            prefixIcon: const Icon(
                              Icons.search,
                              color: Color(0xff8B959E),
                            ),
                            contentPadding: const EdgeInsets.only(
                                left: 20, right: 20, top: 5),
                            hintText: S.of(context).Search,
                            hintStyle: const TextStyle(
                                color: Color(0xff8B959E), fontSize: 13),
                            border: InputBorder.none),
                      )))),
          load == false
              ? const ADCircularProgressIndicator()
              : listfeatures(featureList)
        ]),
        bottomNavigationBar: Container(
          padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
          // padding: EdgeInsets.only(right: 20, left: 20),
          child: GestureDetector(
            onTap: () async {
              Navigator.of(context)
                  .push(
                MaterialPageRoute(builder: (_) => AddFeature()),
              )
                  .then((val) {
                setState(() {
                  load = false;

                  featureList.clear();
                  getfeatures("");
                });
              });
            },
            child: Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: GlobalColors.primaryColor,
                  borderRadius: BorderRadius.circular(10)),
              child: Container(
                padding: const EdgeInsets.all(10),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.add, color: Colors.white),
                      Text(
                        S.of(context).AddNewFeature,
                        style: const TextStyle(color: Colors.white),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
