import 'package:admin_dubai/generated/l10n.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../api_provider/profile_api_provider.dart';
import '../../../../bloc/profile_bloc.dart';
import 'widgets/change_password.dart';

// ignore: must_be_immutable
class Accountinformation extends StatefulWidget {
  Map<String, dynamic>? data;
  Accountinformation({this.data});
  @override
  _Accountinformation createState() => _Accountinformation();
}

bool isLoading = false;

class _Accountinformation extends State<Accountinformation> {
  @override
  void initState() {
    super.initState();
    // bloc2.getProfile();
    getProfile();
  }

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _validatephone = false;
  bool isvisible = false;
  TextEditingController passwordController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  ProfileApiProvider profile = ProfileApiProvider();
  Widget editprofile() {
    return Container(
        child: Form(
            key: _formKey,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(
                S.of(context).Fullname,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Container(
                  height: 60,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(3)),
                  child: Container(
                      decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(5)),
                          border:
                              Border.all(color: Colors.black12, width: 1.0)),
                      child: TextFormField(
                        controller: nameController,
                        decoration: InputDecoration(
                            contentPadding: const EdgeInsets.only(
                                left: 20, right: 20, top: 10),
                            errorText: _validatephone
                                ? S.of(context).PhoneNumber
                                : null,
                            hintText: S.of(context).Fullname,
                            hintStyle: const TextStyle(
                                color: Colors.grey, fontSize: 16),
                            border: InputBorder.none),
                      ))),
              const SizedBox(
                height: 20,
              ),
              Text(
                S.of(context).EmailAddress,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Container(
                  height: 60,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(3)),
                  child: Container(
                      decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(5)),
                          border:
                              Border.all(color: Colors.black12, width: 1.0)),
                      child: TextFormField(
                        controller: emailController,
                        decoration: InputDecoration(
                            contentPadding: const EdgeInsets.only(
                                left: 20, right: 20, top: 10),
                            errorText: _validatephone
                                ? S.of(context).PhoneNumber
                                : null,
                            hintText: S.of(context).EmailAddress,
                            hintStyle: const TextStyle(
                                color: Colors.grey, fontSize: 16),
                            border: InputBorder.none),
                      ))),
              const SizedBox(
                height: 20,
              ),
              Text(S.of(context).PhoneNumber),
              const SizedBox(
                height: 10,
              ),
              Container(
                  width: MediaQuery.of(context).size.width,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(5),
                    border: Border.all(color: Colors.black12, width: 1.0),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: MediaQuery.of(context).size.width * 0.4,
                        child: CountryCodePicker(
                          onChanged: (value) {
                            // setState(() {
                            //   code = value;
                            // });
                            print(value);
                          },
                          // Initial selection and favorite can be one of code ('IT') OR dial_code('+39')
                          initialSelection: '+971',
                          favorite: [
                            '+971',
                          ],
                          // optional. Shows only country name and flag
                          showCountryOnly: false,
                          // optional. Shows only country name and flag when popup is closed.
                          showOnlyCountryWhenClosed: false,
                          // optional. aligns the flag and the Text left
                          alignLeft: true,
                        ),
                      ),
                      Container(
                        height: 40,
                        color: Colors.grey,
                        width: 1,
                      ),
                      Container(
                          width: MediaQuery.of(context).size.width * 0.4,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(3)),
                          child: Container(
                              child: TextFormField(
                            keyboardType: TextInputType.number,
                            controller: phoneController,
                            decoration: InputDecoration(
                                contentPadding: const EdgeInsets.only(
                                    left: 20, right: 20, top: 10),
                                hintText: S.of(context).PhoneNumber,
                                hintStyle: const TextStyle(
                                    color: Colors.grey, fontSize: 16),
                                border: InputBorder.none),
                          )))
                    ],
                  )),
              // Text(S.of(context).PhoneNumber),
              // SizedBox(
              //   height: 10,
              // ),
              // Container(
              //     width: MediaQuery.of(context).size.width,
              //     height: 50,
              //     decoration: BoxDecoration(
              //       color: Colors.white,
              //       borderRadius: BorderRadius.circular(5),
              //       border: Border.all(color: Colors.black12, width: 1.0),
              //     ),
              //     child: Row(
              //       children: [
              //         Container(
              //           width: MediaQuery.of(context).size.width * 0.4,
              //           child: CountryCodePicker(
              //             onChanged: (value) {
              //               // setState(() {
              //               //   code = value;
              //               // });
              //               print(value);
              //             },
              //             // Initial selection and favorite can be one of code ('IT') OR dial_code('+39')
              //             initialSelection: '+971',
              //             favorite: [
              //               '+971',
              //             ],
              //             // optional. Shows only country name and flag
              //             showCountryOnly: false,
              //             // optional. Shows only country name and flag when popup is closed.
              //             showOnlyCountryWhenClosed: false,
              //             // optional. aligns the flag and the Text left
              //             alignLeft: true,
              //           ),
              //         ),
              //         Container(
              //           height: 40,
              //           color: Colors.grey,
              //           width: 1,
              //         ),
              //         Container(
              //             width: MediaQuery.of(context).size.width * 0.4,
              //             decoration: BoxDecoration(
              //                 color: Colors.white,
              //                 borderRadius: BorderRadius.circular(3)),
              //             child: Container(
              //                 child: TextFormField(
              //               initialValue: '123456789',
              //               readOnly: true,

              //               keyboardType: TextInputType.number,
              //               // controller: phoneController,
              //               decoration: InputDecoration(
              //                   contentPadding: EdgeInsets.only(
              //                       left: 20, right: 20, top: 10),
              //                   errorText: _validatephone
              //                       ? 'Please insert phone number'
              //                       : null,
              //                   hintText: 'Phone',
              //                   hintStyle:
              //                       TextStyle(color: Colors.grey, fontSize: 16),
              //                   border: InputBorder.none),
              //             )))
              //       ],
              //     )),
              const SizedBox(
                height: 20,
              ),
              GestureDetector(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (BuildContext context) => ChangePassword()));
                  },
                  child: Container(
                    padding: const EdgeInsets.all(15),
                    decoration: const BoxDecoration(
                      color: Color(0xffFFFFFF),
                      borderRadius: BorderRadius.all(Radius.circular(5)),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              S.of(context).ChangePassword,
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 14),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            const Spacer(),
                            const Icon(
                              Icons.keyboard_arrow_right,
                            )
                          ],
                        ),
                      ],
                    ),
                  )),
              const SizedBox(
                height: 20,
              ),
            ])));
  }

  bool isprofile = false;
  void getProfile() async {
    await profile.getProfile().then(
      (value) {
        value != null
            ? setState(
                () {
                  nameController.text = value.results['fullname'];
                  emailController.text = value.results['email'];
                  phoneController.text = value.results['phone'];
                  isprofile = true;
                },
              )
            : null;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).AdminAccountInformation),
        ),
        body: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: SingleChildScrollView(
            child: StatefulBuilder(
              builder: (BuildContext context, setState1) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),
                    isprofile
                        ? Container(
                            child: editprofile(),
                          )
                        : Container(
                            height: MediaQuery.of(context).size.height,
                            child: ADCircularProgressIndicator()),
                    !isLoading
                        ? Container(
                            height: 50,
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(3)),
                            child: GestureDetector(
                                onTap: () async {
                                  setState1(() {
                                    isLoading = true;
                                  });
                                  dynamic sucessinformation =
                                      await bloc2.editProfile(
                                          nameController.text,
                                          emailController.text,
                                          phoneController.text);
                                  print(sucessinformation);
                                  setState1(() {
                                    isLoading = false;
                                  });
                                  if (sucessinformation['code'] == 1) {
                                    Navigator.pop(context);
                                    ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(
                                            backgroundColor: Colors.green,
                                            content:
                                                Text(S.of(context).updatepro)));
                                  } else {
                                    snackbar(S.of(context).wrong);
                                  }
                                },
                                child: Container(
                                    padding: const EdgeInsets.only(
                                        left: 20, right: 20),
                                    decoration: BoxDecoration(
                                        color: GlobalColors.primaryColor,
                                        borderRadius: BorderRadius.circular(5),
                                        border: Border.all(
                                            color: Colors.black12, width: 1.0)),
                                    child: Center(
                                      child: Text(
                                        S.of(context).Save,
                                        style: const TextStyle(
                                            color: Colors.white, fontSize: 16),
                                      ),
                                    ))))
                        : const ADLinearProgressIndicator(),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
