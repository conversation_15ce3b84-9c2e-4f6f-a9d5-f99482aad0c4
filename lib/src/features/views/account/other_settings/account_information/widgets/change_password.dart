import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/material.dart';

import '../../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../../core/utils/resources.dart';
import '../../../../../bloc/profile_bloc.dart';
import '../../../../../response/generalResponse.dart';

class ChangePassword extends StatefulWidget {
  const ChangePassword({super.key});

  @override
  _ChangePassword createState() => _ChangePassword();
}

class _ChangePassword extends State<ChangePassword> {
  @override
  void initState() {
    super.initState();
  }

  bool isLoading = false;
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _validatephone = false;
  bool isvisible = false;
  TextEditingController passwordController = TextEditingController();
  TextEditingController password1Controller = TextEditingController();
  TextEditingController password2Controller = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).ChangePassword),
        ),
        body: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 20,
                ),
                Container(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          S.of(context).passcur,
                          style: const TextStyle(
                            fontSize: 13,
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                            height: 60,
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(3)),
                            child: Container(
                                decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(5)),
                                    border: Border.all(
                                        color: Colors.black12, width: 1.0)),
                                child: TextFormField(
                                  controller: passwordController,
                                  decoration: InputDecoration(
                                      contentPadding: const EdgeInsets.only(
                                          left: 20, right: 20, top: 10),
                                      errorText: _validatephone ? '' : null,
                                      hintText: S.of(context).passcur,
                                      hintStyle: const TextStyle(
                                          color: Colors.grey, fontSize: 16),
                                      border: InputBorder.none),
                                ))),
                        const SizedBox(
                          height: 20,
                        ),
                        Text(
                          S.of(context).EnterNewPassword,
                          style: const TextStyle(
                            fontSize: 13,
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                            height: 60,
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(3)),
                            child: Container(
                                decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(5)),
                                    border: Border.all(
                                        color: Colors.black12, width: 1.0)),
                                child: TextFormField(
                                  controller: password1Controller,
                                  decoration: InputDecoration(
                                      contentPadding: const EdgeInsets.only(
                                          left: 20, right: 20, top: 10),
                                      errorText: _validatephone ? '' : null,
                                      hintText: S.of(context).EnterNewPassword,
                                      hintStyle: const TextStyle(
                                          color: Colors.grey, fontSize: 16),
                                      border: InputBorder.none),
                                ))),
                        const SizedBox(
                          height: 20,
                        ),
                        Text(
                          S.of(context).ConfirmNewPassword,
                          style: const TextStyle(
                            fontSize: 13,
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Container(
                            height: 60,
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(3)),
                            child: Container(
                                decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(5)),
                                    border: Border.all(
                                        color: Colors.black12, width: 1.0)),
                                child: TextFormField(
                                  controller: password2Controller,
                                  decoration: InputDecoration(
                                      contentPadding: const EdgeInsets.only(
                                          left: 20, right: 20, top: 10),
                                      errorText: _validatephone ? '' : null,
                                      hintText:
                                          S.of(context).ConfirmNewPassword,
                                      hintStyle: const TextStyle(
                                          color: Colors.grey, fontSize: 16),
                                      border: InputBorder.none),
                                ))),
                        const SizedBox(
                          height: 20,
                        ),
                        StatefulBuilder(
                          builder: (BuildContext context, setStateSave) {
                            return Container(
                              height: 50,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(3)),
                              child: GestureDetector(
                                onTap: () async {
                                  if (password1Controller.text ==
                                      password2Controller.text) {
                                    setStateSave(() {
                                      isLoading = true;
                                    });
                                    GeneralResponse response =
                                        await bloc2.changePassword(
                                            passwordController.text,
                                            password1Controller.text);
                                    if (response.msg ==
                                        'Password updated successfully') {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(const SnackBar(
                                              backgroundColor: Colors.green,
                                              content: Text(
                                                  "Password Changed Successfully")));
                                    } else {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(const SnackBar(
                                              backgroundColor: Colors.red,
                                              content: Text(
                                                  "Password Changed fail")));
                                    }
                                    setStateSave(() {
                                      isLoading = false;
                                    });
                                  } else {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(
                                            backgroundColor: Colors.red,
                                            content: Text(
                                                "${S.of(context).ConfirmNewPassword} , ${S.of(context).wrong}")));
                                  }
                                },
                                child: Container(
                                  padding: const EdgeInsets.only(
                                      left: 20, right: 20),
                                  decoration: BoxDecoration(
                                      color: GlobalColors.primaryColor,
                                      borderRadius: BorderRadius.circular(5),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0)),
                                  child: Center(
                                    child: isLoading
                                        ? const ADLinearProgressIndicator()
                                        : Text(
                                            S.of(context).Save,
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 16,
                                            ),
                                          ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
