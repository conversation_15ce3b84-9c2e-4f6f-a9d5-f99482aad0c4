import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../bloc/contact_us_bloc.dart';
import '../../../../response/contact_user_response.dart';

class Contactus extends StatefulWidget {
  const Contactus({super.key});

  @override
  _Contactus createState() => _Contactus();
}

class _Contactus extends State<Contactus> {
  get searchController => null;
  int indexSelected = -1;

  @override
  void initState() {
    contactUsBloc.getContactData(page: 1, size: 20);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).contact),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 20,
            ),
            Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      S.of(context).contact,
                      style: const TextStyle(color: Color(0xff51565B)),
                    ),
                  ],
                )),
            const SizedBox(
              height: 10,
            ),
            Expanded(
              child: StreamBuilder<ContactUserResponse>(
                stream: contactUsBloc.contactstreamController.stream,
                builder: (context, snapshot) {
                  if (snapshot.hasData &&
                      snapshot.connectionState != ConnectionState.waiting) {
                    if (snapshot.data!.code != 1) {
                      snackbar(snapshot.data!.msg!);
                      return const SizedBox();
                    }
                    if (snapshot.data!.data!.isEmpty) {
                      return Container(
                        height: MediaQuery.of(context).size.height * 0.8,
                        child: Center(
                          child: Text(
                            S.of(context).Therearenoitems,
                            style: const TextStyle(fontSize: 25),
                          ),
                        ),
                      );
                    }
                    return ListView.builder(
                      shrinkWrap: true,
                      itemCount: snapshot.data!.data!.length,
                      itemBuilder: (BuildContext context, int index) {
                        return InkWell(
                          onTap: () async {
                            await contactUsBloc.readMessage(
                                id: snapshot.data!.data![index].id!);

                            contactUsBloc.getContactData(page: 1, size: 20);

                            setState(() {
                              indexSelected == index
                                  ? indexSelected = -1
                                  : indexSelected = index;
                            });
                          },
                          child: Container(
                            margin: const EdgeInsets.only(
                                top: 10, bottom: 5, right: 20, left: 20),
                            padding: const EdgeInsets.only(
                                top: 10, bottom: 10, right: 20, left: 20),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: snapshot.data!.data![index].haveRead
                                    ? Colors.white
                                    : Colors.grey[200]!,
                                border: Border.all(color: Colors.grey[200]!)),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        snapshot.data!.data![index].fullname!,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      Text(
                                        snapshot.data!.data![index].email!,
                                        style:
                                            const TextStyle(color: Colors.grey),
                                      ),
                                      if (indexSelected == index) ...[
                                        const SizedBox(
                                          height: 10,
                                        ),
                                        Text(
                                          snapshot.data!.data![index].message!,
                                          style: const TextStyle(
                                              color: Colors.grey),
                                        ),
                                      ]
                                    ],
                                  ),
                                ),
                                Flexible(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Text(
                                        snapshot.data!.data![index].createdAt!
                                            .split("T")[0],
                                        style: const TextStyle(
                                            color: Colors.grey, fontSize: 12),
                                      ),
                                      const SizedBox(
                                        width: 5,
                                      ),
                                      Icon(indexSelected == index
                                          ? Icons.keyboard_arrow_down_outlined
                                          : Icons.keyboard_arrow_right),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  } else {
                    return const ADCircularProgressIndicator();
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
