import 'dart:convert';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../api_provider/other_settings_api_provider.dart';
import '../../../../models/price_plan_model.dart';
import '../../../../response/generalResponse.dart';

class PricePlanForm extends StatefulWidget {
  final PricePlanModel? pricePlan;

  const PricePlanForm({super.key, this.pricePlan});

  @override
  State<PricePlanForm> createState() => _PricePlanFormState();
}

class _PricePlanFormState extends State<PricePlanForm> {
  bool isactions = false;
  bool loading = false;
  bool isLoading = false;

  TextEditingController nameEnController = TextEditingController();
  TextEditingController nameArController = TextEditingController();
  TextEditingController descriptionEnController = TextEditingController();
  TextEditingController descriptionArController = TextEditingController();

  List<PricePlanDataItem> dataItems = [];
  OtherSettingsApiProvider provider = OtherSettingsApiProvider();

  @override
  void initState() {
    super.initState();
    if (widget.pricePlan != null) {
      // Edit mode - populate fields
      nameEnController.text = widget.pricePlan!.nameEn ?? "";
      nameArController.text = widget.pricePlan!.nameAr ?? "";
      descriptionEnController.text = widget.pricePlan!.descriptionEn ?? "";
      descriptionArController.text = widget.pricePlan!.descriptionAr ?? "";
      final originalItems = widget.pricePlan!.data ?? [];
      // Sort by order field first, then assign IDs
      final sortedItems = List<PricePlanDataItem>.from(originalItems);
      sortedItems.sort((a, b) {
        final orderA = int.tryParse(a.order ?? '0') ?? 0;
        final orderB = int.tryParse(b.order ?? '0') ?? 0;
        return orderA.compareTo(orderB);
      });

      dataItems = sortedItems.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        // Ensure each item has a unique ID and correct order
        return item.id.isEmpty
            ? item.copyWith(
                id: '${DateTime.now().millisecondsSinceEpoch}_$index',
                order: (index + 1).toString(),
              )
            : item.copyWith(order: (index + 1).toString());
      }).toList();
    }

    // Add at least one data item if empty
    if (dataItems.isEmpty) {
      dataItems.add(PricePlanDataItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        order: "1",
      ));
    }
  }

  bool get isEditMode => widget.pricePlan != null;

  void addDataItem() {
    setState(() {
      dataItems.add(PricePlanDataItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        order: (dataItems.length + 1).toString(),
      ));
    });
  }

  void removeDataItem(int index) {
    if (dataItems.length > 1) {
      setState(() {
        dataItems.removeAt(index);
        // Update order numbers for remaining items
        for (int i = 0; i < dataItems.length; i++) {
          dataItems[i] = dataItems[i].copyWith(
            order: (i + 1).toString(),
          );
        }
      });
    }
  }

  void removeDataItemById(String itemId) {
    if (dataItems.length > 1) {
      setState(() {
        dataItems.removeWhere((item) => item.id == itemId);
        // Update order numbers for remaining items
        for (int i = 0; i < dataItems.length; i++) {
          dataItems[i] = dataItems[i].copyWith(
            order: (i + 1).toString(),
          );
        }
      });
    }
  }

  submit(FormData data) async {
    if (nameEnController.text.isEmpty) {
      snackbar(S.of(context).enterenn);
      return;
    }
    if (nameArController.text.isEmpty) {
      snackbar(S.of(context).enteraran);
      return;
    }

    setState(() {
      isLoading = true;
    });

    GeneralResponse successInformation;
    if (isEditMode) {
      successInformation = await provider.updatePricePlan(data);
    } else {
      successInformation = await provider.addPricePlan(data);
    }

    if (successInformation.code == 1) {
      Navigator.pop(context);
    } else {
      if (successInformation.msg == null) {
        snackbar(S.of(context).wrong);
      } else {
        snackbar(successInformation.msg!);
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  void deletePricePlan() {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: Column(
                    children: [
                      const SizedBox(height: 10),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(height: 20),
                      Center(
                          child: Text(
                        S.of(context).deletePricePlan,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 20),
                                Text(S.of(context).deleteag),
                                const SizedBox(height: 20),
                                StatefulBuilder(
                                  builder:
                                      (BuildContext context, setStateDelete) {
                                    return Center(
                                      child: GestureDetector(
                                        onTap: () async {
                                          setStateDelete(() {
                                            loading = true;
                                          });

                                          final successinformation =
                                              await provider.deletePricePlan(
                                                  widget.pricePlan!.id!);

                                          setStateDelete(() {
                                            loading = false;
                                          });
                                          if (successinformation.code == 1) {
                                            Navigator.pop(context);
                                            Navigator.pop(context, true);
                                          } else {
                                            Navigator.pop(context, true);
                                            snackbar(successinformation.msg ??
                                                S
                                                    .of(context)
                                                    .cannotDeleteBecauseUsed);
                                          }
                                        },
                                        child: Container(
                                          height: 50,
                                          width:
                                              MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              color: const Color(0xffE04E4D),
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          child: Container(
                                              padding: const EdgeInsets.all(10),
                                              child: Center(
                                                  child: loading
                                                      ? const ADLinearProgressIndicator()
                                                      : Text(
                                                          S.of(context).yesde,
                                                          style:
                                                              const TextStyle(
                                                                  color: Colors
                                                                      .white),
                                                        ))),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
            ));
  }

  Widget buildDraggableDataItemForm(int index) {
    final item = dataItems[index];

    return DragTarget<PricePlanDataItem>(
      builder: (context, candidateData, rejectedData) {
        return Draggable<PricePlanDataItem>(
          data: item,
          feedback: Material(
            elevation: 4.0,
            child: Container(
              width: MediaQuery.of(context).size.width - 40,
              child: buildDataItemForm(index),
            ),
          ),
          childWhenDragging: Container(
            margin: const EdgeInsets.only(bottom: 15),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(10),
              color: Colors.grey[100],
            ),
            child: Container(
              height: 150,
              child: Center(
                child: Text(
                  'Moving ${S.of(context).paymentPlan} ${index + 1}...',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
            ),
          ),
          child: buildDataItemForm(index),
        );
      },
      onWillAccept: (data) => data != item,
      onAccept: (draggedItem) {
        final draggedIndex = dataItems.indexOf(draggedItem);
        final targetIndex = index;

        if (draggedIndex != -1 && draggedIndex != targetIndex) {
          setState(() {
            final item = dataItems.removeAt(draggedIndex);
            dataItems.insert(targetIndex, item);

            // Update order numbers for all items
            for (int i = 0; i < dataItems.length; i++) {
              dataItems[i] = dataItems[i].copyWith(
                order: (i + 1).toString(),
              );
            }
          });
        }
      },
    );
  }

  Widget buildDataItemForm(int index) {
    final item = dataItems[index];

    return HookBuilder(
        key: ValueKey(item.id), // Use item ID as key to maintain state
        builder: (context) {
          final dateController =
              useTextEditingController(text: item.date ?? '');
          final paymentController =
              useTextEditingController(text: item.payment?.toString() ?? '');
          final installmentController =
              useTextEditingController(text: item.installment ?? '');

          // Sync controllers with item data when item changes
          useEffect(() {
            if (dateController.text != (item.date ?? '')) {
              dateController.text = item.date ?? '';
            }
            if (paymentController.text != (item.payment?.toString() ?? '')) {
              paymentController.text = item.payment?.toString() ?? '';
            }
            if (installmentController.text != (item.installment ?? '')) {
              installmentController.text = item.installment ?? '';
            }
            return null;
          }, [item.date, item.payment, item.installment]);

          return Container(
            margin: const EdgeInsets.only(bottom: 15),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with title and delete button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${S.of(context).paymentPlan} ${index + 1}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    if (dataItems.length > 1)
                      IconButton(
                        onPressed: () => removeDataItemById(item.id),
                        icon: const Icon(Icons.delete, color: Colors.red),
                      ),
                  ],
                ),
                const SizedBox(height: 10),

                // Date field
                Text(S.of(context).date, style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                Container(
                  height: 45,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(width: 0.5, color: Colors.grey[300]!),
                      color: Colors.white),
                  child: TextFormField(
                    controller: dateController,
                    onChanged: (value) {
                      setState(() {
                        final itemIndex = dataItems.indexWhere((i) => i.id == item.id);
                        if (itemIndex != -1) {
                          dataItems[itemIndex] = item.copyWith(date: value);
                        }
                      });
                    },
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 10),
                        hintText: S.of(context).enterDate,
                        hintStyle: const TextStyle(
                            color: Color(0xffB7B7B7), fontSize: 14)),
                  ),
                ),
                const SizedBox(height: 10),

                const SizedBox(height: 10),

                // Payment field
                Text(S.of(context).payment,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                Container(
                  height: 45,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(width: 0.5, color: Colors.grey[300]!),
                      color: Colors.white),
                  child: TextFormField(
                    controller: paymentController,
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      setState(() {
                        final itemIndex = dataItems.indexWhere((i) => i.id == item.id);
                        if (itemIndex != -1) {
                          dataItems[itemIndex] = item.copyWith(payment: int.tryParse(value));
                        }
                      });
                    },
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 10),
                        hintText: S.of(context).enterPaymentPercentage,
                        hintStyle: const TextStyle(
                            color: Color(0xffB7B7B7), fontSize: 14)),
                  ),
                ),
                const SizedBox(height: 10),

                // Installment field
                Text(S.of(context).installment,
                    style: const TextStyle(fontSize: 13)),
                const SizedBox(height: 5),
                Container(
                  height: 45,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(width: 0.5, color: Colors.grey[300]!),
                      color: Colors.white),
                  child: TextFormField(
                    controller: installmentController,
                    onChanged: (value) {
                      setState(() {
                        final itemIndex = dataItems.indexWhere((i) => i.id == item.id);
                        if (itemIndex != -1) {
                          dataItems[itemIndex] = item.copyWith(installment: value);
                        }
                      });
                    },
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 10),
                        hintText: S.of(context).enterInstallmentInfo,
                        hintStyle: const TextStyle(
                            color: Color(0xffB7B7B7), fontSize: 14)),
                  ),
                ),
              ],
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: GlobalColors.primaryColor,
              centerTitle: true,
              title: Text(isEditMode
                  ? S.of(context).editPricePlan
                  : S.of(context).addPricePlan),
              actions: isEditMode
                  ? [
                      Container(
                          padding: const EdgeInsets.only(left: 10, right: 10),
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                isactions = !isactions;
                              });
                            },
                            child: const Icon(
                              Icons.more_horiz,
                              color: Colors.white,
                            ),
                          ))
                    ]
                  : null,
            ),
            body: Stack(children: [
              Positioned.fill(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      // Name fields
                      Text(
                        S.of(context).enterenn,
                        style: const TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(width: 0.5, color: Colors.grey),
                            color: Colors.white),
                        child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: TextFormField(
                              controller: nameEnController,
                              decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: S.of(context).enterenn,
                                  hintStyle: const TextStyle(
                                      color: Color(0xffB7B7B7), fontSize: 14)),
                            )),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        S.of(context).enteraran,
                        style: const TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 10),
                      Container(
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(width: 0.5, color: Colors.grey),
                            color: Colors.white),
                        child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: TextFormField(
                              controller: nameArController,
                              decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: S.of(context).enteraran,
                                  hintStyle: const TextStyle(
                                      color: Color(0xffB7B7B7), fontSize: 14)),
                            )),
                      ),
                      const SizedBox(height: 20),

                      // Description fields
                      Text(
                        S.of(context).descriptionEnglish,
                        style: const TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 10),
                      Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border:
                                  Border.all(width: 0.5, color: Colors.grey),
                              color: Colors.white),
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: descriptionEnController,
                            maxLines: 3,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).enterDescriptionEnglish,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                      const SizedBox(height: 10),
                      Text(
                        S.of(context).descriptionArabic,
                        style: const TextStyle(fontSize: 13),
                      ),
                      const SizedBox(height: 10),
                      Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border:
                                  Border.all(width: 0.5, color: Colors.grey),
                              color: Colors.white),
                          child: TextFormField(
                            controller: descriptionArController,
                            maxLines: 3,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).enterDescriptionArabic,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                      const SizedBox(height: 30),

                      // Payment Plans section
                      Text(
                        S.of(context).paymentPlans,
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 15),

                      // Data items with drag and drop
                      ...dataItems.asMap().entries.map((entry) {
                        final index = entry.key;
                        return buildDraggableDataItemForm(index);
                      }).toList(),

                      const SizedBox(height: 10),

                      SizedBox(
                        height: 45,
                        child: ElevatedButton.icon(
                          onPressed: addDataItem,
                          icon: const Icon(Icons.add, size: 16),
                          label: Text(S.of(context).addMore),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: GlobalColors.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                        ),
                      ),

                      const SizedBox(height: 40),
                      !isLoading
                          ? SizedBox(
                              height: 60,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Center(
                                      child: GestureDetector(
                                          onTap: () async {
                                            FormData form = FormData.fromMap({
                                              'name[en]': nameEnController.text,
                                              'name[ar]': nameArController.text,
                                              'description[en]':
                                                  descriptionEnController.text,
                                              'description[ar]':
                                                  descriptionArController.text,
                                              'data': jsonEncode(dataItems
                                                  .map((e) => e.toJson())
                                                  .toList()),
                                              if (isEditMode)
                                                'id': widget.pricePlan!.id,
                                            });

                                            submit(form);
                                          },
                                          child: Container(
                                            height: 50,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            decoration: BoxDecoration(
                                                color:
                                                    GlobalColors.primaryColor,
                                                borderRadius:
                                                    BorderRadius.circular(5)),
                                            child: Container(
                                                padding:
                                                    const EdgeInsets.all(10),
                                                child: Center(
                                                    child: Text(
                                                  isEditMode
                                                      ? S
                                                          .of(context)
                                                          .SaveChanges
                                                      : S
                                                          .of(context)
                                                          .addPricePlan,
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                ))),
                                          )))
                                ],
                              ))
                          : const ADLinearProgressIndicator()
                    ],
                  ),
                ),
              ),
              if (isEditMode && isactions)
                Container(
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                    color: Colors.grey.withValues(alpha: 0.4),
                    child: Container(
                        padding: const EdgeInsets.only(
                          left: 10,
                          right: 10,
                          bottom: 20,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: Colors.grey,
                              ),
                              width: MediaQuery.of(context).size.width,
                              child: Column(
                                children: [
                                  const SizedBox(height: 20),
                                  GestureDetector(
                                      onTap: () {
                                        deletePricePlan();
                                      },
                                      child: Text(
                                        S.of(context).deletePricePlan,
                                        style: const TextStyle(
                                            color: Color(0xffE04E4D),
                                            fontSize: 18),
                                      )),
                                  const SizedBox(height: 20),
                                ],
                              ),
                            ),
                            const SizedBox(height: 10),
                            GestureDetector(
                                onTap: () {
                                  setState(() {
                                    isactions = !isactions;
                                  });
                                },
                                child: Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    color: Colors.white,
                                  ),
                                  width: MediaQuery.of(context).size.width,
                                  child: Center(
                                    child: Text(
                                      S.of(context).Cancel,
                                      style: const TextStyle(
                                          color: Color(0xff007AFF),
                                          fontSize: 18),
                                    ),
                                  ),
                                ))
                          ],
                        )))
            ])));
  }
}
