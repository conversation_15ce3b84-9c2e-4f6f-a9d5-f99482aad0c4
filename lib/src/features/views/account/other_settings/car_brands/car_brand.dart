import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:admin_dubai/src/features/bloc/category_bloc.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../bloc/car_brand_bloc.dart';
import '../../../../response/car_brand_response.dart';
import 'add_brand.dart';
import 'edit_brand.dart';

class CarBrands extends StatefulWidget {
  const CarBrands({super.key});

  @override
  _CarBrands createState() => _CarBrands();
}

class _CarBrands extends State<CarBrands> {
  @override
  void initState() {
    super.initState();
    categoryBloc.getMainCategories();

    carBrandBloc.getCarBrand(page: 1, size: 100);
  }

  TextEditingController searchController = TextEditingController();

  Widget listfeatures() {
    return StreamBuilder<CarBrandResponse>(
        stream: carBrandBloc.carBrand.stream,
        builder: (context, snapshot) {
          if (snapshot.hasData &&
              snapshot.connectionState != ConnectionState.waiting) {
            if (snapshot.data!.code != 1) {
              snackbar(snapshot.data!.msg!);
              return const SizedBox();
            }
            if (snapshot.data!.data!.isEmpty) {
              return Container(
                height: MediaQuery.of(context).size.height * 0.7,
                child: Center(
                  child: Text(
                    S.of(context).Therearenoitems,
                    style: const TextStyle(fontSize: 25),
                  ),
                ),
              );
            }
            return ListView.builder(
              shrinkWrap: true,
              // physics: ClampingScrollPhysics(),
              // scrollDirection: Axis.vertical,
              itemCount: snapshot.data!.data!.length,
              itemBuilder: (BuildContext ctxt, int index) {
                return GestureDetector(
                    onTap: () {},
                    child: Container(
                        padding: const EdgeInsets.only(
                            top: 20, bottom: 5, right: 20, left: 20),
                        child: GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) {
                                  return EditBrand(
                                    snapshot.data!.data![index].name!,
                                    snapshot.data!.data![index].id!,
                                  );
                                },
                              ),
                            );
                          },
                          child: Container(
                              padding: const EdgeInsets.only(
                                  top: 10, bottom: 10, right: 20, left: 20),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(color: Colors.grey[200]!)),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        snapshot.data!.data![index].name!,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                    ],
                                  ),
                                  const Icon(Icons.keyboard_arrow_right)
                                ],
                              )
                              // padding: EdgeInsets.all(20),

                              ),
                        )));
              },
            );
          }
          return const Center(
            child: ADCircularProgressIndicator(),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).CarBrands),
      ),
      body: SingleChildScrollView(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        const SizedBox(
          height: 20,
        ),
        Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.of(context).AllCarBrands,
                  style: const TextStyle(color: Color(0xff51565B)),
                ),
              ],
            )),
        const SizedBox(
          height: 10,
        ),
        listfeatures()
      ])),
      bottomNavigationBar: Container(
          padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
          // padding: EdgeInsets.only(right: 20, left: 20),
          child: GestureDetector(
              onTap: () async {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return AddBrands();
                }));
              },
              child: Container(
                height: 50,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                    color: GlobalColors.primaryColor,
                    borderRadius: BorderRadius.circular(5)),
                child: Container(
                    padding: const EdgeInsets.all(10),
                    child: Center(
                        child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.add, color: Colors.white),
                        Text(
                          S.of(context).AddNewBrand,
                          style: const TextStyle(color: Colors.white),
                        )
                      ],
                    ))),
              ))),
    ));
  }
}
