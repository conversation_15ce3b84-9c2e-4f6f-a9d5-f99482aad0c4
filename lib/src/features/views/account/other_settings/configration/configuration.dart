import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../bloc/profile_bloc.dart';
import '../../../../response/configuration_response.dart';

// ignore: must_be_immutable
class Configration extends StatefulWidget {
  Map<String, dynamic>? data;
  Configration({this.data});
  @override
  _Configration createState() => _Configration();
}

bool isLoading = false;

class _Configration extends State<Configration> {
  @override
  void initState() {
    super.initState();
    bloc2.getConfig();
  }

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _validatephone = false;
  bool isvisible = false;

  List controllers = <TextEditingController>[];

  Widget configurationWidgets(List<Configurations> configurations) {
    configurations.map((e) {
      controllers.add(TextEditingController(text: e.value ?? ''));
    }).toList();

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (var configuration in configurations) ...[
            Text(
              configuration.key ?? '',
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
                height: configuration.key == 'policy' ? 200 : 60,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(3)),
                child: Container(
                    decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(5)),
                        border: Border.all(color: Colors.black12, width: 1.0)),
                    child: TextFormField(
                      keyboardType: TextInputType.url,
                      // done button to be next line
                      textInputAction: configuration.key == 'policy'
                          ? TextInputAction.newline
                          : TextInputAction.done,
                      maxLines: configuration.key == 'policy' ? 7 : 1,
                      controller:
                          controllers[configurations.indexOf(configuration)],
                      decoration: InputDecoration(
                          contentPadding: const EdgeInsets.only(
                              left: 20, right: 20, top: 10),
                          errorText: _validatephone ? '' : null,
                          hintText: configuration.key ?? '',
                          hintStyle:
                              const TextStyle(color: Colors.grey, fontSize: 16),
                          border: InputBorder.none),
                    ))),
            const SizedBox(
              height: 10,
            ),
          ]
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).Configuration),
        ),
        body: StreamBuilder<ConfigurationResponse>(
            stream: bloc2.configurationStream.stream,
            builder: (context, snapshot) {
              if (snapshot.hasData &&
                  snapshot.connectionState != ConnectionState.waiting) {
                if (snapshot.data!.code != 1) {
                  snackbar(snapshot.data!.msg!);
                  return const SizedBox();
                }

                final configurations = snapshot.data?.confugrations ?? [];

                return Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),
                        configurationWidgets(configurations),
                        const SizedBox(height: 20),
                        !isLoading
                            ? Container(
                                height: 50,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(3)),
                                child: GestureDetector(
                                    onTap: () async {
                                      late ConfigurationResponse response;
                                      for (Configurations config
                                          in configurations) {
                                        if (!_formKey.currentState!
                                            .validate()) {
                                          return;
                                        } else {
                                          setState(() {
                                            isLoading = true;
                                          });

                                          response = await bloc2.editConfig({
                                            "id": config.id,
                                            "key": config.key,
                                            "value": controllers[configurations
                                                    .indexOf(config)]
                                                .text
                                          });
                                        }
                                      }

                                      if (response.code == 1) {
                                        setState(() {
                                          isLoading = false;
                                        });

                                        Navigator.pop(context);
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            backgroundColor: Colors.green,
                                            content: Text(S
                                                .of(context)
                                                .configurationhasbeenupdatedsuccessfully),
                                          ),
                                        );
                                      } else {
                                        setState(() {
                                          isLoading = false;
                                        });
                                        Navigator.pop(context);

                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            backgroundColor: Colors.red,
                                            content:
                                                Text("Somthing not working"),
                                          ),
                                        );
                                      }
                                    },
                                    child: Container(
                                        padding: const EdgeInsets.only(
                                            left: 20, right: 20),
                                        decoration: BoxDecoration(
                                            color: GlobalColors.primaryColor,
                                            borderRadius:
                                                BorderRadius.circular(5),
                                            border: Border.all(
                                                color: Colors.black12,
                                                width: 1.0)),
                                        child: Center(
                                          child: Text(
                                            S.of(context).Save,
                                            style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 16),
                                          ),
                                        ))))
                            : const ADLinearProgressIndicator(),
                        const SizedBox(
                          height: 20,
                        ),
                      ],
                    ),
                  ),
                );
              } else {
                return const ADCircularProgressIndicator();
              }
            }),
      ),
    );
  }
}
