import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/theme/ad_colors.dart';
import '../../../../bloc/promo_code_bloc.dart';
import '../../../../response/promo_code_list_response.dart';
import 'add_promo_code.dart';
import 'edit_promo_code.dart';

class PromoCodes extends StatefulWidget {
  const PromoCodes({Key? key}) : super(key: key);

  @override
  State<PromoCodes> createState() => _PromoCodesState();
}

class _PromoCodesState extends State<PromoCodes> {
  @override
  void initState() {
    promoCodeBloc.getPromoCodes(0, 200);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        title: Text(S.of(context).promo),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(S.of(context).AllPromoCodes,
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge!
                    .copyWith(color: ADColors.darkGray)),
            Expanded(
              child: StreamBuilder<PromoCodeListResponse?>(
                  stream: promoCodeBloc.promoCodes.stream,
                  builder: (context, snapshot) {
                    if (snapshot.hasData &&
                        snapshot.connectionState != ConnectionState.waiting) {
                      if (snapshot.data!.code != 1) {
                        snackbar(snapshot.data!.msg!);
                        return const SizedBox();
                      }
                      return ListView.builder(
                        itemCount: snapshot.data!.data!.length,
                        itemBuilder: (BuildContext context, int index) {
                          return Container(
                              padding: const EdgeInsets.all(15),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                      width: 0.5, color: Colors.grey[200]!)),
                              child: ListTile(
                                title: DefaultTextStyle.merge(
                                  style: Theme.of(context).textTheme.bodyLarge,
                                  child: Row(
                                    children: [
                                      Text(
                                          '${snapshot.data!.data![index].code} '),
                                      Text(
                                          '(${snapshot.data!.data![index].discount}% )${S.of(context).Discount}',
                                          style: const TextStyle(
                                              color: ADColors.gray))
                                    ],
                                  ),
                                ),
                                subtitle: Text(
                                    '${snapshot.data!.data![index].numberofusage} ${S.of(context).usersusedpromocode}',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w400)),
                                trailing:
                                    const Icon(Icons.keyboard_arrow_right),
                                onTap: () {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (_) => EditPromoCode(
                                                promoCode:
                                                    snapshot.data!.data![index],
                                              )));
                                },
                              ));
                        },
                      );
                    } else {
                      return const ADCircularProgressIndicator();
                    }
                  }),
            ),
            ElevatedButton(
                onPressed: () => Navigator.push(
                    context, MaterialPageRoute(builder: (_) => AddPromoCode())),
                child: Text('+ ${S.of(context).AddNewPromoCode}'))
          ],
        ),
      ),
    );
  }
}
