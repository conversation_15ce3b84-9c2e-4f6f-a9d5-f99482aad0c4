import 'dart:developer';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/services/rms_service/rms_widgets/rms_discount_drop_down.dart';
import 'package:admin_dubai/src/core/utils/ad_utils.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:queen_validators/queen_validators.dart';

import '../../../../../core/shared_widgets/ad_drop_down_button_form_field.dart';
import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/ad_text_form_field.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../bloc/other_settings_bloc.dart';
import '../../../../bloc/promo_code_bloc.dart';
import '../../../../models/agent_list_model.dart';
import '../../../../models/promo_code.dart';
import '../../../../response/agent_list_response.dart';

class EditPromoCode extends StatefulWidget {
  final PromoCode promoCode;

  const EditPromoCode({super.key, required this.promoCode});

  @override
  State<EditPromoCode> createState() => _EditPromoCodeState();
}

class _EditPromoCodeState extends State<EditPromoCode> {
  final _formKey = GlobalKey<FormState>();
  String? _code;
  String? _titleEn;
  String? _titleAr;
  String? _descriptionEn;
  String? _descriptionAr;
  late bool isPublished;
  String? _discountPercentage;
  AgentListModel? _agent;
  DateTime? _endAt;
  bool _isLoading = false;
  bool loading = false;
  bool isactions = false;
  TextEditingController codeController = TextEditingController();
  TextEditingController discountController = TextEditingController();
  TextEditingController titleController = TextEditingController();
  TextEditingController titleArController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  TextEditingController descriptionArController = TextEditingController();

  @override
  void initState() {
    codeController.text = widget.promoCode.code!;
    discountController.text = widget.promoCode.discount.toString();
    _endAt = DateTime.parse(widget.promoCode.finishat!);
    titleController.text = widget.promoCode.titleEn ?? '';
    titleArController.text = widget.promoCode.titleAr ?? '';
    descriptionController.text = widget.promoCode.descriptionEn ?? '';
    descriptionArController.text = widget.promoCode.descriptionAr ?? '';

    log('asdaskd;lsad;lskamdl; ${widget.promoCode.isPublished}');

    isPublished = widget.promoCode.isPublished == 1 ? true : false;

    othersettingsbloc.agents.sink.add(null);

    othersettingsbloc.getAgents('', 0, 100);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).Edit),
        actions: [
          Container(
              padding: const EdgeInsets.only(left: 10, right: 10),
              child: GestureDetector(
                onTap: () {
                  deletePromoCode();
                },
                child: const Icon(Icons.delete, color: Colors.white),
              ))
        ],
      ),
      body: HookBuilder(builder: (context) {
        log('afafssafasf ${widget.promoCode.rmsDiscountId}');
        final selectedDiscount = useState<int?>(widget.promoCode.rmsDiscountId);

        return Form(
          key: _formKey,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ADTextFormField(
                    controller: codeController,
                    label: S.of(context).Code,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _code = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    controller: titleController,
                    label: S.of(context).TitleEn,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _titleEn = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    controller: titleArController,
                    label: S.of(context).TitleAr,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _titleAr = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    controller: descriptionController,
                    label: S.of(context).descriptionEn,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _descriptionEn = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    controller: descriptionArController,
                    label: S.of(context).descriptionAr,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _descriptionAr = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    controller: discountController,
                    label: S.of(context).DiscountPercentage,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _discountPercentage = value,
                    keyboardType: TextInputType.number,
                  ),
                  RmsDiscountDropDownWidget(
                    selectedRMSDiscount: selectedDiscount,
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  StreamBuilder<AgentListResponse?>(
                    stream: othersettingsbloc.agents.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<AgentListResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.code != 1) {
                          snackbar(snapshot.data!.msg!);
                          return const SizedBox();
                        }
                        if (snapshot.data!.agentList.isEmpty ||
                            snapshot.data!.agentList
                                .where((element) =>
                                    element.id == widget.promoCode.agent)
                                .isEmpty) {
                          return const SizedBox();
                        }
                        return ADDropDownButtonFormField(
                          label: S.of(context).Agents,
                          onSaved: (value) => _agent = value,
                          value: snapshot.data!.agentList.firstWhere(
                            (element) => element.id == widget.promoCode.agent,
                          ),
                          items: snapshot.data!.agentList
                              .map((AgentListModel value) {
                            return DropdownMenuItem<AgentListModel>(
                              value: value,
                              child: Padding(
                                padding: const EdgeInsetsDirectional.only(
                                    start: 10.0),
                                child: Text(
                                  value.fullname!,
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ),
                            );
                          }).toList(),
                        );
                      }
                      return const Center(child: CircularProgressIndicator());
                    },
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  ListTile(
                    leading: IconButton(
                      icon: const Icon(Icons.date_range),
                      onPressed: () async {
                        DateTime? selectedDate = await showDatePicker(
                            context: context,
                            initialDate: _endAt!,
                            firstDate: DateTime(DateTime.now().year),
                            lastDate: DateTime(DateTime.now().year + 5));
                        setState(() {
                          _endAt = selectedDate;
                        });
                      },
                    ),
                    title: Text(S.of(context).endat),
                    subtitle: Text(_endAt!.toFormattedString()),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Row(
                    children: [
                      Switch(
                          value: isPublished,
                          onChanged: (value) {
                            setState(() {
                              isPublished = value;
                            });
                          }),
                      const SizedBox(
                        width: 10,
                      ),
                      Text(S.of(context).isPublished),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  StatefulBuilder(
                    builder: (BuildContext context, setStateEdit) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          _isLoading
                              ? const ADLinearProgressIndicator()
                              : ElevatedButton(
                                  onPressed: () async {
                                    if (_formKey.currentState!.validate()) {
                                      _formKey.currentState!.save();
                                      setStateEdit(() {
                                        _isLoading = true;
                                      });
                                      await promoCodeBloc.editPromoCode(
                                        id: widget.promoCode.id,
                                        code: _code,
                                        discount: _discountPercentage,
                                        finishat: _endAt!.toFormattedString(),
                                        agent: _agent!.id,
                                        title: _titleEn,
                                        titleAr: _titleAr,
                                        description: _descriptionEn,
                                        descriptionAr: _descriptionAr,
                                        isPublished: isPublished,
                                        rmsDiscountId: selectedDiscount.value,
                                      );

                                      setStateEdit(() {
                                        _isLoading = false;
                                      });
                                      Navigator.pop(context);
                                    }
                                  },
                                  child: Text(S.of(context).SaveChanges))
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  void deletePromoCode() {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        S.of(context).DeletePromoCode,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      StatefulBuilder(
                        builder: (BuildContext context, setStatedelete) {
                          return Container(
                            padding: const EdgeInsets.all(15),
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10)),
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Text(S
                                        .of(context)
                                        .Areyousureyouwanttodeletethispromocode),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Center(
                                      child: Container(
                                        // padding: EdgeInsets.only(right: 20, left: 20),
                                        child: GestureDetector(
                                          onTap: () async {
                                            setStatedelete(() {
                                              loading = true;
                                            });
                                            await promoCodeBloc.deletePromoCode(
                                                id: widget.promoCode.id);
                                            setStatedelete(() {
                                              loading = false;
                                            });
                                            Navigator.pop(context);
                                            Navigator.pop(context);
                                          },
                                          child: Container(
                                            height: 50,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            decoration: BoxDecoration(
                                                color: const Color(0xffE04E4D),
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            child: Container(
                                                padding:
                                                    const EdgeInsets.all(10),
                                                child: Center(
                                                    child: loading
                                                        ? const ADLinearProgressIndicator()
                                                        : Text(
                                                            S
                                                                .of(context)
                                                                .DeletePromoCode,
                                                            style:
                                                                const TextStyle(
                                                                    color: Colors
                                                                        .white),
                                                          ))),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  )),
            ));
  }
}
