import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/services/rms_service/rms_widgets/rms_discount_drop_down.dart';
import 'package:admin_dubai/src/core/utils/ad_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:queen_validators/queen_validators.dart';

import '../../../../../core/shared_widgets/ad_drop_down_button_form_field.dart';
import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/ad_text_form_field.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../bloc/other_settings_bloc.dart';
import '../../../../models/agent_list_model.dart';
import '../../../../repository/promo_codes_repository.dart';
import '../../../../response/agent_list_response.dart';
import 'promo_codes.dart';

class AddPromoCode extends StatefulWidget {
  const AddPromoCode({Key? key}) : super(key: key);

  @override
  State<AddPromoCode> createState() => _AddPromoCodeState();
}

class _AddPromoCodeState extends State<AddPromoCode> {
  final _formKey = GlobalKey<FormState>();
  String? _code;
  String? _titleEn;
  String? _titleAr;
  String? _descriptionEn;
  String? _descriptionAr;
  bool isPublished = false;
  String? _discountPercentage;
  AgentListModel? _agent;
  DateTime? _endAt;
  bool _isLoading = false;

  @override
  void initState() {
    _endAt = DateTime.now();
    othersettingsbloc.agents.sink.add(null);

    othersettingsbloc.getAgents('', 0, 200);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(S.of(context).AddNewPromoCode),
      ),
      body: HookBuilder(builder: (context) {
        final selectedDiscount = useState<int?>(null);

        return Form(
          key: _formKey,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ADTextFormField(
                    label: S.of(context).TitleEn,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _titleEn = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    label: S.of(context).TitleAr,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _titleAr = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    label: S.of(context).descriptionEn,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _descriptionEn = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    label: S.of(context).descriptionAr,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _descriptionAr = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    label: S.of(context).Code,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _code = value,
                    textInputAction: TextInputAction.next,
                  ),
                  ADTextFormField(
                    label: S.of(context).DiscountPercentage,
                    validator: qValidator([IsRequired()]),
                    onSaved: (value) => _discountPercentage = value,
                    keyboardType: TextInputType.number,
                  ),
                  RmsDiscountDropDownWidget(
                    selectedRMSDiscount: selectedDiscount,
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  StreamBuilder<AgentListResponse?>(
                    stream: othersettingsbloc.agents.stream,
                    builder: (BuildContext context,
                        AsyncSnapshot<AgentListResponse?> snapshot) {
                      if (snapshot.hasData &&
                          snapshot.connectionState != ConnectionState.waiting) {
                        if (snapshot.data!.code != 1) {
                          snackbar(snapshot.data!.msg!);
                          return const SizedBox();
                        }
                        if (snapshot.data!.agentList.isEmpty) {
                          return const SizedBox();
                        }
                        return ADDropDownButtonFormField(
                          label: S.of(context).Agents,
                          onSaved: (value) => _agent = value,
                          value: snapshot.data!.agentList.first,
                          items: snapshot.data!.agentList
                              .map((AgentListModel value) {
                            return DropdownMenuItem<AgentListModel>(
                              value: value,
                              child: Padding(
                                padding: const EdgeInsetsDirectional.only(
                                    start: 10.0),
                                child: Text(
                                  value.fullname!,
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ),
                            );
                          }).toList(),
                        );
                      }
                      return const Center(child: CircularProgressIndicator());
                    },
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  ListTile(
                    leading: IconButton(
                      icon: const Icon(Icons.date_range),
                      onPressed: () async {
                        DateTime? selectedDate = await showDatePicker(
                            context: context,
                            initialDate: _endAt!,
                            firstDate: DateTime(DateTime.now().year),
                            lastDate: DateTime(DateTime.now().year + 5));
                        setState(() {
                          _endAt = selectedDate;
                        });
                      },
                    ),
                    title: Text(S.of(context).endat),
                    subtitle: Text(_endAt!.toFormattedString()),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Row(
                    children: [
                      Switch(
                          value: isPublished,
                          onChanged: (value) {
                            setState(() {
                              isPublished = value;
                            });
                          }),
                      const SizedBox(
                        width: 10,
                      ),
                      Text(S.of(context).isPublished),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      _isLoading
                          ? const ADLinearProgressIndicator()
                          : ElevatedButton(
                              onPressed: () async {
                                if (_formKey.currentState!.validate()) {
                                  _formKey.currentState!.save();
                                  setState(() {
                                    _isLoading = true;
                                  });
                                  var response = await PromoCodeRepository()
                                      .addPromoCode(
                                          _code,
                                          _discountPercentage,
                                          _endAt.toString(),
                                          _agent?.id,
                                          _titleEn,
                                          _titleAr,
                                          _descriptionEn,
                                          _descriptionAr,
                                          isPublished,
                                          selectedDiscount.value);

                                  if (response.code == -1) {
                                    ScaffoldMessenger.of(context)
                                        .showSnackBar(SnackBar(
                                            backgroundColor: Colors.red,
                                            content: Text(
                                              response.msg ?? '',
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            )));
                                  } else {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(
                                            backgroundColor: Colors.green,
                                            content:
                                                Text(S.of(context).added)));
                                    Navigator.pop(context);
                                    Navigator.pushReplacement(
                                        context,
                                        MaterialPageRoute(
                                            builder: (BuildContext context) =>
                                                const PromoCodes()));
                                  }
                                  setState(() {
                                    _isLoading = false;
                                  });
                                }
                              },
                              child: Text(S.of(context).AddNewPromoCode))
                    ],
                  )
                ],
              ),
            ),
          ),
        );
      }),
    );
  }
}
