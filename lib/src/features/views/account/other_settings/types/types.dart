import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/bloc/category_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../bloc/other_settings_bloc.dart';
import '../../../../models/other_settings.dart';
import '../../../../response/other_settings_response.dart';
import 'add_type.dart';
import 'edit_type.dart';

class Types extends StatefulWidget {
  const Types({super.key});

  @override
  _Types createState() => _Types();
}

class _Types extends State<Types> {
  int pagenumber = 1;
  List<OtherSettingsModel> results = [];

  TextEditingController searchController = TextEditingController();

  Widget listtypes(OtherSettingsResponse data) {
    return results.isNotEmpty
        ? Expanded(
            child: ListView.builder(
            shrinkWrap: true,
            // physics: ClampingScrollPhysics(),
            // scrollDirection: Axis.vertical,
            itemBuilder: (BuildContext ctxt, int index) {
              return Container(
                  padding: const EdgeInsets.only(
                      top: 10, bottom: 5, right: 20, left: 20),
                  child: GestureDetector(
                    onTap: () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return EditType(
                            results[index].name!,
                            results[index].categories ?? [],
                            results[index].id!);
                      })).then((value) {
                        results.clear();
                        othersettingsbloc.types.sink.add(null);
                        othersettingsbloc.gettypes(0, 1000, "", '');
                      });
                    },
                    child: Container(
                        padding: const EdgeInsets.only(
                            top: 10, bottom: 10, right: 20, left: 20),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Colors.grey[200]!)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: results[index].image != null &&
                                          results[index].image!.isNotEmpty
                                      ? Image.network(
                                          results[index].image!,
                                          width: 50,
                                          height: 50,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Container(
                                            width: 50,
                                            height: 50,
                                            color: Colors.grey[200],
                                            child: const Center(
                                                child: Icon(
                                              Icons.image,
                                              color: Colors.grey,
                                            )),
                                          ),
                                        )
                                      : Container(
                                          width: 50,
                                          height: 50,
                                          color: Colors.grey[200],
                                          child: const Center(
                                              child: Icon(
                                            Icons.image,
                                            color: Colors.grey,
                                          )),
                                        ),
                                ),
                                const SizedBox(
                                  width: 10,
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    results[index].name != null
                                        ? Text(
                                            results[index].name!,
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold),
                                          )
                                        : Container(),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    results[index].categories != null &&
                                            results[index]
                                                .categories!
                                                .isNotEmpty
                                        ? Text(
                                            results[index]
                                                .categories!
                                                .map((e) => e.name)
                                                .join(', '),
                                            style: const TextStyle(
                                                color: Colors.grey),
                                          )
                                        : Container(),
                                  ],
                                ),
                              ],
                            ),
                            const Icon(Icons.keyboard_arrow_right)
                          ],
                        )
                        // padding: EdgeInsets.all(20),

                        ),
                  ));
            },
            itemCount: results.length,
          ))
        : nodatafound('No Types to show');
  }

  @override
  void initState() {
    super.initState();
    categoryBloc.getMainCategories();

    results.clear();

    othersettingsbloc.types.sink.add(null);
    othersettingsbloc.gettypes(0, 1000, "", '');
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
      width: 20,
      height: 20.0,
      child: SvgPicture.asset(
        'assets/filter.svg',
        semanticsLabel: 'Acme Logo',
        fit: BoxFit.cover,
      ),
    );
    return Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).Types),
      ),
      body: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        const SizedBox(
          height: 20,
        ),
        // const SizedBox(
        //   height: 10,
        // ),
        // Container(
        //   padding: const EdgeInsets.only(left: 20, right: 20),
        //   child: Container(
        //     height: 40,
        //     decoration: BoxDecoration(
        //         color: const Color(0xffF1F1F1),
        //         borderRadius: BorderRadius.circular(3)),
        //     child: Container(
        //       decoration: const BoxDecoration(
        //         borderRadius: BorderRadius.all(Radius.circular(5)),
        //       ),
        //       child: TextFormField(
        //         controller: searchController,
        //         textInputAction: TextInputAction.search,
        //         onFieldSubmitted: (value) {
        //           othersettingsbloc.types.sink.add(null);
        //           othersettingsbloc.gettypes(0, 1000, value, '');
        //         },
        //         decoration: InputDecoration(
        //           prefixIcon: const Icon(
        //             Icons.search,
        //             color: Color(0xff8B959E),
        //           ),
        //           contentPadding:
        //               const EdgeInsets.only(left: 20, right: 20, top: 5),
        //           hintText: S.of(context).Search,
        //           hintStyle:
        //               const TextStyle(color: Color(0xff8B959E), fontSize: 13),
        //           border: InputBorder.none,
        //         ),
        //       ),
        //     ),
        //   ),
        // ),
        StreamBuilder<OtherSettingsResponse?>(
          stream: othersettingsbloc.types.stream,
          builder: (context, AsyncSnapshot<OtherSettingsResponse?> snapshot) {
            if (snapshot.hasData &&
                snapshot.connectionState != ConnectionState.waiting) {
              if (snapshot.data!.code != 1) {
                snackbar(snapshot.data!.msg!);
                return const SizedBox();
              }
              results.clear();
              results.addAll(snapshot.data!.results);
              return listtypes(snapshot.data!);
            } else {
              return const Center(child: ADCircularProgressIndicator());
            }
          },
        )
      ]),
      bottomNavigationBar: Container(
          padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
          // padding: EdgeInsets.only(right: 20, left: 20),
          child: GestureDetector(
              onTap: () async {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return AddType();
                })).then((value) {
                  results.clear();

                  othersettingsbloc.gettypes(0, 1000, "", '');
                });
              },
              child: Container(
                height: 50,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                    color: GlobalColors.primaryColor,
                    borderRadius: BorderRadius.circular(5)),
                child: Container(
                    padding: const EdgeInsets.all(10),
                    child: Center(
                        child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.add, color: Colors.white),
                        Text(
                          S.of(context).Addnewtype,
                          style: const TextStyle(color: Colors.white),
                        )
                      ],
                    ))),
              ))),
    );
  }
}
