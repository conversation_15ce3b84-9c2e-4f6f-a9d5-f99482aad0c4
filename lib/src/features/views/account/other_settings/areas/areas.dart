import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../api_provider/area_api_provider.dart';
import '../../../../bloc/area_bloc.dart';
import '../../../../models/area.dart';
import '../../../../response/area_response.dart';
import '../../../../response/generalResponse.dart';
import 'add_area.dart';

class Areas extends StatefulWidget {
  const Areas({super.key});

  @override
  _Areas createState() => _Areas();
}

class _Areas extends State<Areas> {
  final RefreshController _refreshControllerwait =
      RefreshController(initialRefresh: false);
  int pagenumber = 1;
  List<AreasModel> resultsnoti = [];
  void _onRefresh() async {
    await Future.delayed(const Duration(milliseconds: 1000));
    pagenumber = 1;

    areabloc.getareas(800, "", pagenumber);

    _refreshControllerwait.refreshCompleted();
  }

  TextEditingController searchController = TextEditingController();

  String? currentvalue2;
  String? currentvalue3;
  String? currentvalue4;
  List<String> filteredAs = ['Filtered As'];
  List<String> feature = ['Feature', 'not Feature'];
  AreaApiProvider provider = AreaApiProvider();
  void deletearea(int id) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                  height: MediaQuery.of(context).size.height * 0.4,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        S.of(context).DeleteArea,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 20,
                                ),
                                Text(S.of(context).deleare),
                                const SizedBox(
                                  height: 20,
                                ),
                                Center(
                                  child: Container(
                                    // padding: EdgeInsets.only(right: 20, left: 20),
                                    child: GestureDetector(
                                      onTap: () async {
                                        GeneralResponse successinformation =
                                            await provider.deletearea(id);
                                        if (successinformation.code == 1) {
                                          Navigator.pop(context);
                                          pagenumber = 1;

                                          areabloc.getareas(400, '', 0);
                                          resultsnoti.clear();
                                        } else {
                                          if (successinformation.msg == null) {
                                            snackbar(S.of(context).wrong);
                                          } else {
                                            snackbar(successinformation.msg!);
                                          }
                                        }
                                        // _submit(rate.toString(), _comment.text);
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: const Color(0xffE04E4D),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                          padding: const EdgeInsets.all(10),
                                          child: Center(
                                            child: Text(
                                              S.of(context).DeleteArea,
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
            ));
  }

  void _onLoading() async {
    pagenumber = pagenumber + 1;

    areabloc.getareas(100, "", pagenumber);
    _refreshControllerwait.loadComplete();
  }

  @override
  void initState() {
    super.initState();
    areabloc.getareas(200, "", pagenumber);
  }

  Widget _buildAreaWidget(AreasResponse data) {
    return resultsnoti.isNotEmpty
        ? Expanded(
            child: SmartRefresher(
            enablePullDown: true,
            enablePullUp: true,
            header: const WaterDropHeader(),
            footer: CustomFooter(
              builder: (BuildContext context, LoadStatus? mode) {
                Widget body;
                if (mode == LoadStatus.idle) {
                  body = const Text("No more Data");
                } else if (mode == LoadStatus.loading) {
                  body = const CupertinoActivityIndicator();
                } else if (mode == LoadStatus.failed) {
                  body = const Text("Load Failed!Click retry!");
                } else if (mode == LoadStatus.canLoading) {
                  body = const Text("release to load more");
                } else {
                  body = const Text("No more Data");
                }
                return Container(
                  height: 55.0,
                  child: Center(child: body),
                );
              },
            ),
            controller: _refreshControllerwait,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            child: ListView.builder(
              itemCount: resultsnoti.length,
              primary: false,
              itemBuilder: (BuildContext context, int index) {
                return GestureDetector(
                    onTap: () {},
                    child: Container(
                        padding: const EdgeInsets.only(
                            top: 10, bottom: 5, right: 20, left: 20),
                        child: GestureDetector(
                          onLongPress: () {
                            deleteArea(resultsnoti[index].id);
                            // Navigator.of(context)
                            //     .push(
                            //   new MaterialPageRoute(
                            //       builder: (_) => new EditFeature(
                            //           filtredList[index].name,
                            //           filtredList[index].category,
                            //           filtredList[index].id)),
                            // )
                            //     .then((val) {
                            //   setState(() {
                            //     load = false;
                            //     filtredList.clear();
                            //     getfeatures('');
                            //   });
                            // });
                          },
                          child: Container(
                              padding: const EdgeInsets.only(
                                  top: 10, bottom: 10, right: 20, left: 20),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(color: Colors.grey[200]!)),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      resultsnoti[index].name != null
                                          ? Text(
                                              resultsnoti[index].name!,
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            )
                                          : Container(),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                    ],
                                  ),
                                  InkWell(
                                    onTap: () {
                                      deleteArea(resultsnoti[index].id);
                                    },
                                    child: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                  )
                                ],
                              )
                              // padding: EdgeInsets.all(20),

                              ),
                        )));
              },
            ),
          ))
        : nodatafound('No Areas to show');
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: GlobalColors.primaryColor,
              centerTitle: true,
              title: Text(S.of(context).Areas),
            ),
            body: Center(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    StreamBuilder<AreasResponse>(
                      stream: areabloc.subject.stream,
                      builder:
                          (context, AsyncSnapshot<AreasResponse> snapshot) {
                        if (snapshot.hasData &&
                            snapshot.connectionState !=
                                ConnectionState.waiting) {
                          if (snapshot.data!.code != 1) {
                            snackbar(snapshot.data!.msg!);
                            return const SizedBox();
                          }
                          resultsnoti.clear();

                          resultsnoti.addAll(snapshot.data!.results);
                          return _buildAreaWidget(snapshot.data!);
                        } else {
                          return const Center(
                              child: ADCircularProgressIndicator());
                        }
                      },
                    ),
                    const SizedBox(
                      height: 20,
                    )
                  ]),
            ),
            bottomNavigationBar: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                // padding: EdgeInsets.only(right: 20, left: 20),
                child: GestureDetector(
                    onTap: () async {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) {
                            return const AddArea();
                          },
                        ),
                      ).then((value) {
                        areabloc.getareas(500, '', 0);
                        resultsnoti.clear();
                      });
                      // uploadimages(context);
                    },
                    child: Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          color: GlobalColors.primaryColor,
                          borderRadius: BorderRadius.circular(5)),
                      child: Container(
                          padding: const EdgeInsets.all(10),
                          child: Center(
                              child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.add, color: Colors.white),
                              Text(
                                S.of(context).AddnewArea,
                                style: const TextStyle(color: Colors.white),
                              )
                            ],
                          ))),
                    )))));
  }

  void deleteArea(id) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Container(
            height: MediaQuery.of(context).size.height * 0.3,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  S.of(context).DeleteArea,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 20,
                          ),
                          const Center(
                              child: Text(
                                  "are you sure you want to delete this area")),
                          const SizedBox(
                            height: 20,
                          ),
                          Center(
                            child: Container(
                              // padding: EdgeInsets.only(right: 20, left: 20),
                              child: GestureDetector(
                                onTap: () async {
                                  GeneralResponse successinformation =
                                      await provider.deletearea(id);

                                  if (successinformation.code == 1) {
                                    Navigator.pop(context, true);

                                    areabloc.getareas(500, '', 0);
                                    resultsnoti.clear();
                                  } else {
                                    if (successinformation.msg == null) {
                                      snackbar(S.of(context).wrong);
                                    } else {
                                      snackbar(successinformation.msg!);
                                    }
                                  }
                                  // _submit(rate.toString(), _comment.text);
                                },
                                child: Container(
                                  height: 50,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                      color: const Color(0xffE04E4D),
                                      borderRadius: BorderRadius.circular(10)),
                                  child: Container(
                                      padding: const EdgeInsets.all(10),
                                      child: Center(
                                          child: Text(
                                        S.of(context).Yes,
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ))),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
      ),
    );
  }
}
