import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../core/utils/resources.dart';

// ignore: must_be_immutable
class OpenMap extends StatefulWidget {
  final Function onSave;
  const OpenMap({super.key, required this.onSave});

  @override
  _OpenMap createState() => _OpenMap();
}

class _OpenMap extends State<OpenMap> with TickerProviderStateMixin {
  final markers = <Marker>{};
  GoogleMapController? myMapController;
  final Set<Marker> _markers = {};
  LatLng _mainLocation = const LatLng(25.052802590388662, 55.26296216994525);
  Set<Marker> myMarker() {
    setState(() {
      _markers.add(Marker(
        // This marker id can be anything that uniquely identifies each marker.
        markerId: MarkerId(_mainLocation.toString()),
        position: _mainLocation,

        icon: BitmapDescriptor.defaultMarker,
      ));
    });

    return _markers;
  }

  // Future<bool> getPermission() async {
  //   return await Permission.location.request().isGranted;
  // }

  @override
  void initState() {
    super.initState();

    // getPermission().then((value) =>
    //     value ? Navigator.of(context).pop() : Navigator.of(context).pop());
    setState(() {
      _mainLocation = const LatLng(25.052802590388662, 55.26296216994525);
    });

    _markers.add(
      Marker(
        // This marker id can be anything that uniquely identifies each marker.
        markerId: MarkerId(_mainLocation.toString()),
        position: _mainLocation,

        icon: BitmapDescriptor.defaultMarker,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              child: GoogleMap(
                initialCameraPosition: CameraPosition(
                  target: _mainLocation,
                  zoom: 13.0,
                ),
                markers: Set<Marker>.of(_markers),
                mapType: MapType.normal,
                onMapCreated: (controller) {
                  setState(() {
                    myMapController = controller;
                  });
                },
                onTap: (LatLng latLng) async {
                  print("Gddggd");
                  print(latLng);
                  _markers.clear();
                  _markers.add(Marker(
                      markerId: const MarkerId('mark'), position: latLng));
                  setState(() {});
                },
              ),
            ),
            Container(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    child: const Icon(Icons.keyboard_arrow_right, size: 70),
                    onTap: () {
                      Navigator.pop(context);
                      // scaffoldKey.currentState.openDrawer();
                    },
                  )
                ],
              ),
            ),
            Container(
              alignment: Alignment.bottomCenter,
              padding: const EdgeInsets.only(
                  right: 10, left: 10, bottom: 15, top: 10),
              child: GestureDetector(
                onTap: () async {
                  Navigator.pop(context);

                  widget.onSave(_markers);

                  // return Future(() => false);
                },
                child: Container(
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                      color: GlobalColors.primaryColor,
                      borderRadius: BorderRadius.circular(5)),
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    child: Center(
                      child: Text(
                        S.of(context).Save,
                        style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
