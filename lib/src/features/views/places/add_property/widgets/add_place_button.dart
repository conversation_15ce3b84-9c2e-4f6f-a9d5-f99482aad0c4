import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';

class AddPlaceButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool isEdit;

  const AddPlaceButton({Key? key, required this.onTap, required this.isEdit})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 50,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
              color: GlobalColors.primaryColor,
              borderRadius: BorderRadius.circular(5)),
          child: Container(
              padding: const EdgeInsets.all(10),
              child: Center(
                  child: Text(
                isEdit ? S.of(context).Edit : S.of(context).AddNewDestination,
                style: const TextStyle(color: Colors.white),
              ))),
        ),
      ),
    );
  }
}
