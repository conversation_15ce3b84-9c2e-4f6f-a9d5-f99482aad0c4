import 'package:flutter/material.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../api_provider/property_api_provider.dart';
import '../../../../bloc/property_bloc.dart';
import '../../../../response/property_list_response.dart';

void deleteProperties(id, index,
    {required BuildContext context, required isLoading}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.3,
                decoration: new BoxDecoration(
                    color: const Color(0xffF5F6F7),
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        height: 5, width: 50, color: const Color(0xffD2D4D6)),
                    const SizedBox(
                      height: 20,
                    ),
                    Center(
                        child: Text(
                      S.of(context).DeleteProperty,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    )),
                    Container(
                      padding: const EdgeInsets.all(15),
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              // SizedBox(
                              //   height: 20,
                              // ),
                              // Text(S.of(context).),
                              // SizedBox(
                              //   height: 20,
                              // ),
                              StatefulBuilder(
                                builder: (BuildContext context, refrechState) {
                                  return Center(
                                    child: GestureDetector(
                                      onTap: () async {
                                        refrechState(() {
                                          isLoading = true;
                                        });

                                        var response =
                                            await PropertyApiProvider()
                                                .deletePropertie(id);
                                        print(response.code);
                                        print(response.msg ?? '');
                                        if (response.code == 1) {
                                          PropertyListResponse
                                              propertyListResponse =
                                              propertyBloc.properties.value;

                                          propertyListResponse
                                              .properties!.properties!
                                              .removeAt(index);
                                          propertyBloc.properties.sink
                                              .add(propertyListResponse);
                                          Navigator.pop(context);
                                        } else {
                                          Navigator.pop(context);
                                          snackbar(response.msg ?? '');
                                        }
                                        refrechState(() {
                                          isLoading = false;
                                        });
                                      },
                                      child: Container(
                                        height: 50,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: const Color(0xffE04E4D),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Container(
                                          padding: const EdgeInsets.all(10),
                                          child: Center(
                                            child: isLoading
                                                ? const ADLinearProgressIndicator()
                                                : Text(
                                                    S
                                                        .of(context)
                                                        .yeDeleteproperty,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                )),
          ));
}
