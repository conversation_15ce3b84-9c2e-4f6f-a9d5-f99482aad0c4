import 'package:admin_dubai/src/features/models/request_mode_list.dart';
import 'package:admin_dubai/src/features/views/items_page/widgets/item_search_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../core/utils/fade_tans_animation.dart';
import '../../../core/utils/resources.dart';
import '../../../core/utils/screen_utils.dart';
import '../requests_and_items/request_and_items_page/requests_and_items.dart';

// ignore: must_be_immutable
class RequestsPage extends StatefulWidget {
  final List<RequestListModel> requests;
  ItemsPageType? pageType;

  RequestsPage({Key? key, this.pageType, required this.requests})
      : super(key: key);

  @override
  _RequestsPageState createState() => _RequestsPageState();
}

class _RequestsPageState extends State<RequestsPage> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
                horizontal: ScreensHelper.fromWidth(3),
                vertical: ScreensHelper.fromWidth(6)),
            child: Column(
              children: [
                FadeTransAnimation(
                  delayInMillisecond: 400,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "All Holiday Homes",
                            style: TextStyle(fontSize: 16, color: Colors.black),
                          ),
                        ],
                      ),
                      InkWell(
                        onTap: () {},
                        child: SvgPicture.asset(
                          DubaiPagesIcons.filterIcon,
                          allowDrawingOutsideViewBox: false,
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(
                  height: ScreensHelper.fromWidth(3),
                ),
                FadeTransAnimation(
                    delayInMillisecond: 600, child: const ItemSearchWidget()),
                SizedBox(
                  height: ScreensHelper.fromWidth(3),
                ),
                FadeTransAnimation(
                  delayInMillisecond: 800,
                  child: GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: widget.requests.length,
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2, childAspectRatio: 2 / 3),
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: () {
                            // Navigator.push(context,
                            //     MaterialPageRoute(builder: (ctx) {
                            //       return RequestDetailsPage(
                            //         carRent: filtredRequests[index],
                            //       );
                            //     }));
                          },
                          // child: RequestDetailsCardWidget(
                          //   type: ItemsPageType.CAR,
                          //   carItem: filtredRequests[index!],
                          // ),
                        );
                        // Padding(
                        //   padding:
                        //       EdgeInsets.all(ScreensHelper.fromWidth(0.5)),
                        //   child: InkWell(
                        //     onTap: () {
                        //       Navigator.push(context,
                        //           MaterialPageRoute(builder: (ctx) {
                        //         return ItemDetailsPage();
                        //       }));
                        //     },
                        //     child: Padding(
                        //       padding:
                        //           EdgeInsets.all(ScreensHelper.fromWidth(1)),
                        //       child: Container(
                        //         decoration: BoxDecoration(
                        //           color: Colors.black.withOpacity(0.9),
                        //           borderRadius: BorderRadius.circular(
                        //               ScreensHelper.fromWidth(2)),
                        //           image: DecorationImage(
                        //             fit: BoxFit.cover,
                        //             colorFilter: ColorFilter.mode(
                        //                 Colors.black.withOpacity(0.45),
                        //                 BlendMode.dstATop),
                        //             image: NetworkImage(
                        //                 widget.requests[index].image!),
                        //           ),
                        //         ),
                        //         height: ScreensHelper.fromHeight(60),
                        //         child: Padding(
                        //           padding: EdgeInsets.symmetric(
                        //               horizontal:
                        //                   ScreensHelper.fromWidth(2.5),
                        //               vertical: ScreensHelper.fromWidth(1.2)),
                        //           child: Align(
                        //             alignment: Alignment.bottomLeft,
                        //             child: SizedBox(
                        //               height: ScreensHelper.fromHeight(6),
                        //               child: Column(
                        //                 crossAxisAlignment:
                        //                     CrossAxisAlignment.start,
                        //                 children: [
                        //                   Text(
                        //                     widget.requests[index].name!,
                        //                     style: TextStyle(
                        //                         color: Colors.white,
                        //                         fontSize: 15),
                        //                   ),
                        //                   SizedBox(
                        //                     height:
                        //                         ScreensHelper.fromHeight(1),
                        //                   ),
                        //                   // Text(
                        //                   //   widget.requests[index].price! +
                        //                   //       'AED',
                        //                   //   style: TextStyle(
                        //                   //       fontSize: 15,
                        //                   //       color: Colors.white),
                        //                   // ),
                        //                 ],
                        //               ),
                        //             ),
                        //           ),
                        //         ),
                        //       ),
                        //     ),
                        //   ));
                      }),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
