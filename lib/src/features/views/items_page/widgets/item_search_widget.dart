import 'package:flutter/material.dart';

import '../../../../core/utils/base_validator.dart';
import '../../../../core/utils/resources.dart';
import '../../../../core/utils/screen_utils.dart';

class ItemSearchWidget extends StatefulWidget {
  const ItemSearchWidget({Key? key}) : super(key: key);

  @override
  _ItemSearchWidgetState createState() => _ItemSearchWidgetState();
}

class _ItemSearchWidgetState extends State<ItemSearchWidget> {
  bool triedToSubmit = false;
  bool _searchValidation = true;
  final _key = GlobalKey<FormFieldState<String>>();
  final _controller = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      style: const TextStyle(
          decorationThickness: 0, decorationColor: Color(0xFF), fontSize: 14),
      key: _key,
      controller: _controller,
      focusNode: searchFocusNode,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        filled: true,
        prefixIcon: Padding(
          padding: EdgeInsets.all(
            ScreensHelper.fromWidth(4),
          ),
          child: Icon(
            Icons.search,
            color: GlobalColors.borderSearchColor,
          ),
        ),
        fillColor: const Color(0xFFF1F1F1),
        focusedErrorBorder: InputBorder.none,
        border: InputBorder.none,
        focusedBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        // border: OutlineInputBorder(
        //   borderSide: BorderSide(
        //       color: GlobalColors.borderSearchColor, width: 2.0),
        // ),
        contentPadding:
            const EdgeInsets.only(left: 15, bottom: 11, top: 15, right: 15),
        isDense: true,
        hintText: 'Search',
      ),
      validator: (value) {
        return BaseValidator.validateValue(
          context,
          value!,
          [],
          _searchValidation,
        );
      },
      onChanged: (value) {
        if (!triedToSubmit) return;
        _searchValidation = true;
        if (_key.currentState!.validate()) {
          setState(() {
            _searchValidation = false;
          });
        }
      },
      onFieldSubmitted: (term) {
        triedToSubmit = true;
        setState(() {});
//        _fieldFocusChange(context, myFocusNodeUserName, myFocusNodePassword);
      },
    );
  }
}
