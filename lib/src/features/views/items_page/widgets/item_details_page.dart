import 'package:flutter/material.dart';

import '../../../../core/utils/fade_tans_animation.dart';
import '../../../../core/utils/resources.dart';
import '../../../../core/utils/screen_utils.dart';
import 'status_details_widget.dart';

// ignore: must_be_immutable
class ItemDetailsPage extends StatefulWidget {
  ItemDetailsPage({Key? key, this.title}) : super(key: key);
  String? title;

  @override
  _ItemDetailsPageState createState() => _ItemDetailsPageState();
}

class _ItemDetailsPageState extends State<ItemDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(widget.title ?? "Holiday Homes 1"),
            SizedBox(
              width: ScreensHelper.fromWidth(10),
            )
          ],
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            FadeTransAnimation(
              delayInMillisecond: 700,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreensHelper.fromWidth(3),
                        vertical: ScreensHelper.fromWidth(4)),
                    child: const Text(
                      "Current",
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),
            StatusDetailsWidget(
              isCurrent: true,
            ),
            SizedBox(
              height: ScreensHelper.fromWidth(3),
            ),
            FadeTransAnimation(
              delayInMillisecond: 700,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: ScreensHelper.fromWidth(3),
                        vertical: ScreensHelper.fromWidth(4)),
                    child: const Text(
                      "Recent Updates",
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),
            StatusDetailsWidget(
              isCurrent: false,
            ),
            StatusDetailsWidget(
              isCurrent: false,
            ),
          ],
        ),
      ),
    );
  }
}
