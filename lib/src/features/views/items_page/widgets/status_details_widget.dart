import 'package:admin_dubai/src/features/views/items_page/widgets/remove_sheet.dart';
import 'package:flutter/material.dart';

import '../../../../core/utils/fade_tans_animation.dart';
import '../../../../core/utils/resources.dart';
import '../../../../core/utils/screen_utils.dart';

class StatusDetailsWidget extends StatefulWidget {
  StatusDetailsWidget({Key? key, this.isCurrent}) : super(key: key);
  bool? isCurrent;

  @override
  _StatusDetailsWidgetState createState() => _StatusDetailsWidgetState();
}

class _StatusDetailsWidgetState extends State<StatusDetailsWidget> {
  @override
  Widget build(BuildContext context) {
    return FadeTransAnimation(
      delayInMillisecond: 900,
      child: Padding(
        padding: EdgeInsets.symmetric(
            vertical: ScreensHelper.fromHeight(1),
            horizontal: ScreensHelper.fromWidth(3)),
        child: Container(
          decoration: BoxDecoration(
              border: Border.all(color: GlobalColors.borderGrayColor),
              borderRadius: BorderRadius.circular(ScreensHelper.fromWidth(2))),
          child: Column(
            children: [
              _buildTitleWithActions(widget.isCurrent!),
              Divider(
                height: ScreensHelper.fromHeight(0.5),
                color: GlobalColors.borderGrayColor,
              ),
              _buildTitleWithValue("Availability", "12/2/2021 - 17/2/2021"),
              _buildTitleWithValue("Price Per Night", "700 AED"),
              _buildTitleWithValue("Type", "Normal"),
            ],
          ),
        ),
      ),
    );
  }

  _buildTitleWithValue(String title, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: ScreensHelper.fromWidth(3),
          vertical: ScreensHelper.fromWidth(2.5)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style:
                    TextStyle(fontSize: 12, color: GlobalColors.textGrayColor),
              ),
            ],
          ),
          Text(
            value,
            style: TextStyle(fontSize: 14, color: GlobalColors.textGrayColor),
          )
        ],
      ),
    );
  }

  _buildTitleWithActions(bool isCurrent) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: ScreensHelper.fromWidth(3),
          vertical: ScreensHelper.fromWidth(4)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Updated on 12/2/2021 ",
                style: TextStyle(fontSize: 14, color: Colors.black),
              ),
            ],
          ),
          isCurrent
              ? Row(
                  children: [
                    InkWell(
                      onTap: () {
                        showModalBottomSheet(
                            context: context,
                            shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(25.0))),
                            isScrollControlled: true,
                            backgroundColor: GlobalColors.fillDialogColor,
                            builder: (ctx) {
                              return const RemoveSheet();
                            });
                      },
                      child: Text(
                        "Remove",
                        style: TextStyle(fontSize: 13, color: GlobalColors.red),
                      ),
                    ),
                    SizedBox(
                      width: ScreensHelper.fromWidth(3),
                    ),
                    Text(
                      "Edit",
                      style: TextStyle(fontSize: 13, color: GlobalColors.blue),
                    ),
                  ],
                )
              : Container()
        ],
      ),
    );
  }
}
