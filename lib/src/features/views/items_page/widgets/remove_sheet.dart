import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/utils/fade_tans_animation.dart';
import '../../../../core/utils/resources.dart';
import '../../../../core/utils/rounded_animated_button.dart';
import '../../../../core/utils/screen_utils.dart';

class RemoveSheet extends StatefulWidget {
  const RemoveSheet({Key? key}) : super(key: key);

  @override
  _RemoveSheetState createState() => _RemoveSheetState();
}

class _RemoveSheetState extends State<RemoveSheet> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: AnimatedPadding(
        padding: MediaQuery.of(context).viewInsets,
        duration: const Duration(milliseconds: 500),
        child: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: ScreensHelper.fromWidth(10),
                child: Divider(
                  color: Colors.grey.withOpacity(0.5),
                  thickness: 4,
                  height: ScreensHelper.fromHeight(4),
                ),
              ),
              SizedBox(
                height: ScreensHelper.fromHeight(2.3),
              ),
              FadeTransAnimation(
                delayInMillisecond: 500,
                child: const Text(
                  "Remove Update",
                  style: TextStyle(fontSize: 16),
                ),
              ),
              SizedBox(
                height: ScreensHelper.fromHeight(2.3),
              ),
              FadeTransAnimation(
                delayInMillisecond: 600,
                child: Padding(
                  padding: EdgeInsets.all(ScreensHelper.fromWidth(3)),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius:
                            BorderRadius.circular(ScreensHelper.fromWidth(3))),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: ScreenUtil().setWidth(85).toDouble()),
                      child: Column(
                        children: [
                          SizedBox(
                            height: ScreensHelper.fromHeight(2.3),
                          ),
                          FadeTransAnimation(
                            delayInMillisecond: 800,
                            child: const Text(
                              "Are you sure you want to remove this update?",
                              style: TextStyle(fontSize: 14),
                            ),
                          ),
                          SizedBox(
                            height: ScreensHelper.fromHeight(3),
                          ),
                          FadeTransAnimation(
                            delayInMillisecond: 800,
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal:
                                      ScreenUtil().setWidth(5).toDouble()),
                              child: RoundedAnimatedButton(
                                color: GlobalColors.red,
                                text: 'Yes, Remove Update',
                                onPressed: () {},
                              ),
                            ),
                          ),
                          SizedBox(
                            height: ScreensHelper.fromHeight(2.3),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
