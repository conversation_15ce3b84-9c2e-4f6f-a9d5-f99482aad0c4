import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/models/main_category_model.dart';
import 'package:admin_dubai/src/features/views/featured_videos/widgets/filter_featured_video.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/svg.dart';

import '../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../core/shared_widgets/snack_bar.dart';
import '../../../core/utils/resources.dart';
import '../../bloc/featured_bloc.dart';
import '../../response/featued_video_response.dart';
import '../video_details/video_details.dart';
import 'widgets/change_video_type.dart';

class FeaturedVideos extends StatefulWidget {
  @override
  _FeaturedVideos createState() => _FeaturedVideos();
}

class _FeaturedVideos extends State<FeaturedVideos> {
  bool isLoading = false;
  TextEditingController searchController = TextEditingController();
  MainCategoryModel? categorySelected;
  int? selectedAsValue;
  String? selectedAsString;
  bool? loading = false;
  @override
  void initState() {
    super.initState();

    featuredBloc.getFeaturedVideos(
      page: 1,
      size: 100,
      filteras: -1,
    );
  }

  Widget _buildCategoryWidget() {
    return StreamBuilder<FeaturedVideoResponse?>(
      stream: featuredBloc.featuredtreamController.stream,
      builder: (context, snapshot) {
        print("aaaaaaaaaaaaaaaaa");
        if (snapshot.hasData &&
            snapshot.connectionState != ConnectionState.waiting) {
          if (snapshot.data!.code != 1) {
            snackbar(snapshot.data!.msg!);
            return const SizedBox();
          }
          if (snapshot.data!.data!.isEmpty) {
            return nodatafound('No Feature video to show');
          }
          return MasonryGridView.count(
            shrinkWrap: true,
            padding:
                const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
            crossAxisCount: 2,
            physics: const NeverScrollableScrollPhysics(),
            mainAxisSpacing: 8,
            itemCount: snapshot.data!.data!.length,
            primary: false,
            itemBuilder: (context, index) {
              return Column(
                children: [
                  const SizedBox(
                    width: 5,
                  ),
                  Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: Image.network(
                          '${snapshot.data!.data![index].photo}',
                          height: 288,
                          width: MediaQuery.of(context).size.width * 0.45,
                          fit: BoxFit.fill,
                        ),
                      ),
                      Positioned(
                        bottom: 12,
                        left: 10,
                        child: Column(
                          children: [
                            Text(
                              snapshot.data!.data![index].title ?? '',
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 9),
                            ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          print("=========================");
                          print(snapshot.data!.data![index].title);
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (BuildContext context) => ChewieDemo(
                                id: 0,
                                categoryName: "Areas",
                                video: snapshot.data!.data![index].video,
                                name: snapshot.data!.data![index].title,
                              ),
                            ),
                          );
                        },
                        child: Container(
                          height: 288,
                          width: MediaQuery.of(context).size.width * 0.45,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              color: Colors.grey.withOpacity(0.3)),
                        ),
                      ),
                      Positioned(
                        top: 10,
                        right: 10,
                        child: GestureDetector(
                          onTap: () {
                            changeVideoType(
                                context,
                                snapshot.data!.data![index].id,
                                index,
                                snapshot.data!.data![index].featuredCategory,
                                snapshot.data!.data![index].featuredHome,
                                isLoading);
                          },
                          child: Center(
                            child: Container(
                              child: const Icon(
                                Icons.more_horiz,
                                size: 30,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              );
            },
          );
        } else {
          return const ADCircularProgressIndicator();
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final Widget svg2 = SizedBox(
        width: 20,
        height: 20.0,
        child: SvgPicture.asset(
          'assets/filter.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).FeaturedVideos),
      ),
      body: SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const SizedBox(
            height: 20,
          ),
          Container(
              padding: const EdgeInsets.only(left: 20, right: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    S.of(context).AllFeaturedVideos,
                    style: const TextStyle(color: Color(0xff51565B)),
                  ),
                  GestureDetector(
                    onTap: () {
                      filter(context, loading, featuredBloc, categorySelected,
                          selectedAsValue, selectedAsString);
                    },
                    child: svg2,
                  )
                ],
              )),
          const SizedBox(
            height: 10,
          ),
          Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                  color: const Color(0xffF1F1F1),
                  borderRadius: BorderRadius.circular(3)),
              child: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                ),
                child: TextFormField(
                  controller: searchController,
                  textInputAction: TextInputAction.search,
                  onFieldSubmitted: (value) {
                    featuredBloc.getFeaturedVideos(
                        page: 1, size: 100, filteras: -1, key: value);
                  },
                  decoration: InputDecoration(
                      prefixIcon: const Icon(
                        Icons.search,
                        color: Color(0xff8B959E),
                      ),
                      contentPadding:
                          const EdgeInsets.only(left: 20, right: 20, top: 5),
                      hintText: S.of(context).Searchplacesandlocations,
                      hintStyle: const TextStyle(
                          color: Color(0xff8B959E), fontSize: 13),
                      border: InputBorder.none),
                ),
              ),
            ),
          ),
          _buildCategoryWidget(),
          const SizedBox(
            height: 20,
          )
        ]),
      ),
    ));
  }
}
