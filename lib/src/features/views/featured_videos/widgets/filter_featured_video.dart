import 'package:admin_dubai/src/core/shared_widgets/main_category_drop_down.dart';
import 'package:admin_dubai/src/features/bloc/featured_bloc.dart';
import 'package:admin_dubai/src/features/models/main_category_model.dart';
import 'package:flutter/material.dart';

import '../../../../../generated/l10n.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/utils/resources.dart';

void filter(BuildContext context, loading, FeaturedBloc featuredBloc,
    MainCategoryModel? categorySelected, selectedAsValue, selectedAsString) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(builder: (context, stateSetter) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.50,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: SingleChildScrollView(
                      child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 30, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  height: 10,
                                  color: Colors.transparent,
                                ),

                                Text(S.of(context).Filter,
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold)),

                                // Spacer(),
                                GestureDetector(
                                  onTap: () async {
                                    stateSetter(() {
                                      loading = true;
                                    });
                                    await featuredBloc.getFeaturedVideos(
                                      page: 1,
                                      size: 200,
                                      filteras: -1,
                                    );
                                    categorySelected = null;
                                    selectedAsValue = -1;
                                    selectedAsString = null;

                                    stateSetter(() {
                                      loading = false;
                                    });
                                    Navigator.pop(context);
                                  },
                                  child: Text(
                                    S.of(context).Reset,
                                    style: const TextStyle(
                                        color: Color(0xff51565B)),
                                  ),
                                )
                              ])),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Filtered As',
                                  style: TextStyle(fontSize: 13),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Container(
                                  child: Container(
                                    // width: 120,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      border: Border.all(
                                          color: Colors.black12, width: 1.0),
                                    ),
                                    child: DropdownButton<String>(
                                        isExpanded: true,
                                        hint: const Padding(
                                          padding: EdgeInsets.only(
                                              left: 20, right: 20),
                                          child: Text(
                                            'Filtered As',
                                            style: TextStyle(
                                                color: Color(0xffB7B7B7)),
                                          ),
                                        ),
                                        value: selectedAsString,
                                        underline: const SizedBox(),
                                        iconEnabledColor: Colors.black,
                                        items: const [
                                          DropdownMenuItem<String>(
                                            value: "homepage",
                                            child: Text("homepage"),
                                          ),
                                          DropdownMenuItem<String>(
                                            value: "category",
                                            child: Text("category"),
                                          ),
                                        ],
                                        onChanged: (value) {
                                          stateSetter(() {
                                            print(value);
                                            selectedAsString = value;
                                            selectedAsValue =
                                                value == "homepage" ? 0 : 1;
                                          });
                                        }),
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                const Text(
                                  'Category',
                                ),
                                const SizedBox(
                                  height: 10,
                                ),

                                // Container(
                                //   child: Container(
                                //     // width: 120,
                                //     height: 50,
                                //     decoration: BoxDecoration(
                                //       borderRadius: BorderRadius.circular(5),
                                //       border: Border.all(
                                //           color: Colors.black12, width: 1.0),
                                //     ),
                                //     child: DropdownButton<String>(
                                //       isExpanded: true,
                                //       hint: const Padding(
                                //         padding: EdgeInsets.only(
                                //             left: 20, right: 20),
                                //         child: Text(
                                //           'Category',
                                //           style: TextStyle(
                                //               color: Color(0xffB7B7B7)),
                                //         ),
                                //       ),
                                //       underline: const SizedBox(),
                                //       value: categorySelected,
                                //       iconEnabledColor: Colors.black,
                                //       items: [
                                //         "hotels",
                                //         "restaurants",
                                //         "shops",
                                //         "activities",
                                //         "hoilday_home",
                                //         "car_rent",
                                //         "luxury"
                                //       ]
                                //           .map(
                                //             (e) => DropdownMenuItem<String>(
                                //               value: e,
                                //               child: Text(e),
                                //             ),
                                //           )
                                //           .toList(),
                                //       onChanged: (value) {
                                //         stateSetter(() {
                                //           categorySelected = value;
                                //         });
                                //       },
                                //     ),
                                //   ),
                                // ),

                                MainCategoryDropDown(
                                  selectedCategory: categorySelected,
                                  onChanged: (value) {
                                    stateSetter(() {
                                      categorySelected = value;
                                    });
                                  },
                                ),

                                const SizedBox(
                                  height: 20,
                                ),
                                Center(
                                  child: GestureDetector(
                                    onTap: () async {
                                      stateSetter(() {
                                        loading = true;
                                      });
                                      await featuredBloc.getFeaturedVideos(
                                          page: 1,
                                          size: 100,
                                          filteras: selectedAsValue,
                                          category: categorySelected?.id);
                                      Navigator.pop(context);

                                      stateSetter(() {
                                        loading = false;
                                      });
                                    },
                                    child: loading!
                                        ? const ADLinearProgressIndicator()
                                        : Container(
                                            height: 50,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            decoration: BoxDecoration(
                                                color:
                                                    GlobalColors.primaryColor,
                                                borderRadius:
                                                    BorderRadius.circular(5)),
                                            child: Container(
                                              padding: const EdgeInsets.all(10),
                                              child: Center(
                                                child: Text(
                                                  S.of(context).ApplyFilter,
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                ),
                                              ),
                                            ),
                                          ),
                                  ),
                                ),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
                ));
          }));
}
