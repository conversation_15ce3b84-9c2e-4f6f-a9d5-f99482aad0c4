import 'package:flutter/material.dart';

import '../../../../../generated/l10n.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../bloc/featured_bloc.dart';

void changeVideoType(
    BuildContext context, id, index, isCategory, isHome, isLoading) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    enableDrag: true,
    backgroundColor: Colors.transparent,
    builder: (context) => Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.15,
        decoration: const BoxDecoration(
          color: Color(0xffcfcfce),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25.0), topRight: Radius.circular(25.0)),
        ),
        child: Container(
          padding: const EdgeInsets.all(15),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                StatefulBuilder(
                  builder: (BuildContext context, setState1) {
                    return GestureDetector(
                      onTap: () async {
                        try {
                          setState1(() {
                            isLoading = true;
                          });
                          if (isHome == 1) {
                            var response = await featuredBloc
                                .removefeaturedvideofromhome(id: id);
                            if (response.code == 1) isHome = 0;
                          } else {
                            var response = await featuredBloc
                                .addfeaturedvideotohome(id: id);
                            if (response.code == 1) isHome = 1;
                          }
                          featuredBloc.featuredtreamController.value!
                              .data![index].featuredHome = isHome;

                          featuredBloc.featuredtreamController.sink
                              .add(featuredBloc.featuredtreamController.value);

                          setState1(() {
                            isLoading = false;
                          });
                        } catch (e) {
                          isLoading = false;
                        }
                      },
                      child: Container(
                        alignment: Alignment.center,
                        child: isLoading
                            ? const LinearProgressIndicator()
                            : Text(
                                isHome == 1
                                    ? S.of(context).RemoveApplicationfeature
                                    : S.of(context).AddApplicationfeature,
                                style: TextStyle(
                                  color: isHome == 1 ? Colors.red : Colors.blue,
                                  fontSize: 20,
                                ),
                              ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 15),
                StatefulBuilder(
                  builder: (BuildContext context, setState2) {
                    return GestureDetector(
                      onTap: () async {
                        try {
                          setState2(() {
                            isLoading = true;
                          });
                          if (isCategory == 1) {
                            var response = await featuredBloc
                                .removefeaturedvideofromcategory(id: id);
                            if (response.code == 1) isCategory = 0;
                          } else {
                            var response = await featuredBloc
                                .addfeaturedvideotocategory(id: id);
                            if (response.code == 1) isCategory = 1;
                          }
                          featuredBloc.featuredtreamController.value!
                              .data![index].featuredCategory = isCategory;

                          featuredBloc.featuredtreamController.sink
                              .add(featuredBloc.featuredtreamController.value);

                          setState2(() {
                            isLoading = false;
                          });
                        } catch (e) {
                          isLoading = false;
                        }
                      },
                      child: Container(
                        alignment: Alignment.center,
                        child: isLoading
                            ? const ADLinearProgressIndicator()
                            : Text(
                                isCategory == 1
                                    ? S.of(context).Removefromcategoryfeature
                                    : S.of(context).AddCaterogryfeature,
                                style: TextStyle(
                                  color: isCategory == 1
                                      ? Colors.red
                                      : Colors.blue,
                                  fontSize: 20,
                                ),
                              ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    ),
  );
}
