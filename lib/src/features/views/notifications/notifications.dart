import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/material.dart';

import '../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../core/shared_widgets/snack_bar.dart';
import '../../../core/utils/resources.dart';
import '../../bloc/notification_bloc.dart';
import '../../response/notifications_response.dart';

class Notifications extends StatefulWidget {
  const Notifications({super.key});

  @override
  _Notifications createState() => _Notifications();
}

class _Notifications extends State<Notifications> {
  @override
  void initState() {
    super.initState();
    notificationBloc.getNotifications(page: 1, size: 500);
  }

  Widget listnotifications() {
    return StreamBuilder<NotificationsResponse>(
      stream: notificationBloc.notificationstreamController.stream,
      builder: (context, snapshot) {
        if (snapshot.hasData &&
            snapshot.connectionState != ConnectionState.waiting) {
          if (snapshot.data!.code != 1) {
            snackbar(snapshot.data!.msg!);
            return const SizedBox();
          }
          if (snapshot.data!.data!.isEmpty) {
            return nodatafound('No Notification to show');
          }
          return ListView.builder(
            itemCount: snapshot.data!.data!.length,
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            itemBuilder: (BuildContext ctxt, int index) {
              return GestureDetector(
                onTap: () {},
                child: Container(
                  padding: const EdgeInsets.only(
                      top: 10, bottom: 5, right: 20, left: 20),
                  child:
                      // padding: EdgeInsets.all(20),
                      Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(snapshot.data!.data![index].date!),
                      // SizedBox(
                      //   height: 10,
                      // ),
                      Container(
                        child: Column(
                          children: [
                            for (var i = 0;
                                i < snapshot.data!.data![index].items!.length;
                                i++)
                              Container(
                                padding: const EdgeInsets.only(top: 10),
                                child: Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: const Color(0xffF1F1F1),
                                      border: Border.all(
                                          color: const Color(0xffF1F1F2),
                                          width: 1)),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          Container(
                                            height: 5,
                                            width: 5,
                                            decoration: BoxDecoration(
                                                color: Colors.black,
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                          ),
                                          const SizedBox(
                                            width: 20,
                                          ),
                                          Text(
                                            snapshot.data!.data![index]
                                                    .items![i].title ??
                                                " ",
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold),
                                          )
                                        ],
                                      ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      Container(
                                        padding: const EdgeInsets.only(
                                            left: 20, right: 20),
                                        child: Text(
                                          snapshot.data!.data![index].items![i]
                                              .message!,
                                          style: const TextStyle(
                                            color: Color(0xff8B959E),
                                            fontSize: 14,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        } else {
          return const Center(
            child: ADCircularProgressIndicator(),
          );
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).Notifications),
      ),
      body: listnotifications(),
    ));
  }
}
