import 'package:rxdart/rxdart.dart';

import '../repository/discussions_repo.dart';
import '../response/discussions_response.dart';
import '../response/generalResponse.dart';

class DiscussionsBloc {
  final DiscussionsRepo _repository = DiscussionsRepo();

  final BehaviorSubject<DiscussionsResponse?> discussionStreamController =
      BehaviorSubject<DiscussionsResponse?>();

  getDiscussions(
      {int? id, int? page = 1, int? size = 20, String? categoryName}) async {
    DiscussionsResponse response =
        await _repository.getDiscussions(id, page, size, categoryName);
    discussionStreamController.sink.add(response);
  }

  deleteDiscussions({int? id, int? index}) async {
    GeneralResponse response = await _repository.deleteDiscussions(id);

    if (response.code == 1) {
      discussionStreamController.value!.data?.removeAt(index!);
    }
    discussionStreamController.sink.add(discussionStreamController.value);

    return response;
  }

  replycommentdiscussion({int? id, String? reply}) async {
    GeneralResponse response =
        await _repository.replycommentdiscussion(id, reply);

    discussionStreamController.sink.add(discussionStreamController.value);

    return response;
  }

  deletediscussion({int? id}) async {
    GeneralResponse response = await _repository.deletediscussion(
      id,
    );

    await getDiscussions(id: id, page: 1, size: 50, categoryName: "");

    return response;
  }

  Future<GeneralResponse> sendDiscussions(
      {int? id, String? comment, String? categoryName}) async {
    GeneralResponse response =
        await _repository.snedDiscussion(id, comment, categoryName);
    if (response.code == 1) {
      discussionStreamController.sink.add(null);
      discussionsBloc.getDiscussions(
          id: id, page: 1, size: 50, categoryName: categoryName);
    }

    discussionStreamController.sink.add(discussionStreamController.value);
    return response;
  }

  Future<GeneralResponse> editComment(
      {int? id, String? comment, String? categoryName}) async {
    GeneralResponse response =
        await _repository.editComment(id, comment, categoryName);
    if (response.code == 1) {
      // discussionsBloc.getDiscussions(
      //     id: id, page: 1, size: 50, categoryName: categoryName);
    }

    discussionStreamController.sink.add(discussionStreamController.value);
    return response;
  }

  BehaviorSubject<DiscussionsResponse?> get subject =>
      discussionStreamController;
}

final discussionsBloc = DiscussionsBloc();
