import 'package:rxdart/rxdart.dart';

import '../repository/notification_repo.dart';
import '../response/notifications_response.dart';

class NotificationBloc {
  final NotificationRepository _repository = NotificationRepository();

  final BehaviorSubject<NotificationsResponse> notificationstreamController =
      BehaviorSubject<NotificationsResponse>();

  getNotifications({int? page = 1, int? size = 200}) async {
    NotificationsResponse response =
        await _repository.getNotification(page, size);
    notificationstreamController.sink.add(response);
  }

  BehaviorSubject<NotificationsResponse> get subject =>
      notificationstreamController;
}

final notificationBloc = NotificationBloc();
