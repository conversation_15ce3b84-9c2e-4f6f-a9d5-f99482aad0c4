import 'package:rxdart/rxdart.dart';

import '../repository/promo_codes_repository.dart';
import '../response/generalResponse.dart';
import '../response/promo_code_list_response.dart';

class PromoCodeBloc {
  final PromoCodeRepository _repository = PromoCodeRepository();

  final BehaviorSubject<PromoCodeListResponse?> _promoCodes =
      BehaviorSubject<PromoCodeListResponse>();

  getPromoCodes([int? page, int? size]) async {
    PromoCodeListResponse response =
        await _repository.getPromoCodes(page, size);
    _promoCodes.sink.add(response);
  }

  deletePromoCode({int? id}) async {
    GeneralResponse response = await _repository.deletePromoCode(id);
    if (response.code == 1) {
      getPromoCodes(0, 200);
    }
  }

  editPromoCode(
      {int? id,
      String? code,
      String? discount,
      String? finishat,
      int? agent,
      String? title,
      String? titleAr,
      String? description,
      String? descriptionAr,
      bool? isPublished,
      int? rmsDiscountId}) async {
    GeneralResponse response = await _repository.editPromoCode(
        id,
        code,
        discount,
        finishat,
        agent,
        title,
        titleAr,
        description,
        descriptionAr,
        isPublished,
        rmsDiscountId);
    if (response.code == 1) {
      getPromoCodes(0, 200);
    }
  }

  BehaviorSubject<PromoCodeListResponse?> get promoCodes => _promoCodes;
}

final PromoCodeBloc promoCodeBloc = PromoCodeBloc();
