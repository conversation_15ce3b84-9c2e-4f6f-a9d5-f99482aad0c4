import 'package:rxdart/rxdart.dart';

import '../repository/featured_repo.dart';
import '../response/featued_video_response.dart';
import '../response/generalResponse.dart';

class FeaturedBloc {
  final FeatureRepo _repository = FeatureRepo();
  final BehaviorSubject<FeaturedVideoResponse?> featuredtreamController =
      BehaviorSubject<FeaturedVideoResponse>();
  Future<int> getFeaturedVideos(
      {int? page = 1,
      int? size = 200,
      int? filteras,
      int? category,
      String? key}) async {
    FeaturedVideoResponse response = await _repository.getFeaturedVideos(
        page, size, filteras, category, key);
    featuredtreamController.sink.add(response);
    return response.code!;
  }

  Future<GeneralResponse> addfeaturedvideotocategory({int? id}) async {
    GeneralResponse response = await _repository.addfeaturedvideotocategory(id);
    return response;
  }

  Future<GeneralResponse> addfeaturedvideotohome({int? id}) async {
    GeneralResponse response = await _repository.addfeaturedvideotohome(id);

    return response;
  }

  Future<GeneralResponse> removefeaturedvideofromcategory({int? id}) async {
    GeneralResponse response =
        await _repository.removefeaturedvideofromcategory(id);

    return response;
  }

  Future<GeneralResponse> removefeaturedvideofromhome({int? id}) async {
    GeneralResponse response =
        await _repository.removefeaturedvideofromhome(id);

    return response;
  }

  BehaviorSubject<FeaturedVideoResponse?> get subject =>
      featuredtreamController;
}

final featuredBloc = FeaturedBloc();
