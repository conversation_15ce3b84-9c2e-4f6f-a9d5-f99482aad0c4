import '../repository/auth_repository.dart';
import '../response/auth_response.dart';

class AuthBloc {
  static bool isEnglish = true;
  final AuthRepository _repository = AuthRepository();

  login(String email, String password, phoneToken) async {
    int code = 0;
    String message = 'Something went wrong.';
    AuthResponse response =
        await _repository.login(email, password, phoneToken);

    if (response.code == 1) {
      code = response.code!;
      message = response.msg ?? '';

      return {
        'code': code,
        'msg': message,
        'token': response.token,
        'data': response.results
      };
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }

  sendrequestpassword(String email) async {
    int code = 0;
    String message = 'Something went wrong.';
    AuthResponse response = await _repository.sendrequestpassword(email);

    if (response.code == 1) {
      code = response.code!;
      message = response.msg ?? '';

      return {'code': code, 'msg': message};
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }

  verfiycode(String email, String codes) async {
    int code = 0;
    String message = 'Something went wrong.';
    AuthResponse response = await _repository.verfiycode(email, codes);

    if (response.code == 1) {
      code = response.code!;
      message = response.msg ?? '';

      return {'code': code, 'msg': message};
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }

  resetpassword(String email, String password) async {
    int code = 0;
    String message = 'Something went wrong.';
    AuthResponse response = await _repository.resetpassword(email, password);

    if (response.code == 1) {
      code = response.code!;
      message = response.msg ?? '';

      return {'code': code, 'msg': message};
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }
}

final bloc = AuthBloc();
