import 'package:rxdart/rxdart.dart';

import '../repository/car_rentals_reposotiry.dart';
import '../response/car_rental_list_response.dart';

class CarRentalBloc {
  final CarRentalsRepository _repository = CarRentalsRepository();

  final BehaviorSubject<CarRentalListResponse?> _carRentals =
      BehaviorSubject<CarRentalListResponse?>();

  getCarRentals([int? page, int? size, String? key]) async {
    CarRentalListResponse? response =
        await _repository.getCarRentals(page, size, key);
    _carRentals.sink.add(response);
  }

  BehaviorSubject<CarRentalListResponse?> get carRentals => _carRentals;
}

final CarRentalBloc carRentalBloc = CarRentalBloc();
