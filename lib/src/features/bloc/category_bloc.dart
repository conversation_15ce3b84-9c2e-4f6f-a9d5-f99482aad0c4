import 'package:admin_dubai/src/features/models/main_category_model.dart';
import 'package:rxdart/rxdart.dart';

import '../repository/category_repository.dart';
import '../response/category_images_response.dart';
import '../response/category_reels_response.dart';
import '../response/category_response.dart';

class CategoryBloc {
  final CategoryRepository _repository = CategoryRepository();

  final BehaviorSubject<CategoryResponse?> _subject =
      BehaviorSubject<CategoryResponse?>();

  final BehaviorSubject<CategoryDetailsResponse?> _subject2 =
      BehaviorSubject<CategoryDetailsResponse?>();

  final BehaviorSubject<CategoryImagesResponse?> _subject3 =
      BehaviorSubject<CategoryImagesResponse?>();

  final BehaviorSubject<CategoryReelsResponse?> _subject4 =
      BehaviorSubject<CategoryReelsResponse?>();

  final BehaviorSubject<List<MainCategoryModel>?> _subject5 =
      BehaviorSubject<List<MainCategoryModel>?>();

  getCategories(String category, [int? page, int? size, String? key]) async {
    CategoryResponse response =
        await _repository.getCategories(category, page, size, key);
    _subject.sink.add(response);
  }

  Future<void> getMainCategories() async {
    var response = await _repository.getMainCategories();
    _subject5.sink.add(response);
  }

  getCategoryDetails(int id, String? categoryName) async {
    CategoryDetailsResponse response =
        await _repository.getCategoryDetails(id, categoryName);
    _subject2.sink.add(response);
    return response.result;
  }

  // getMainCategoryImages(int id, String? categoryName) async {
  //   CategoryImagesResponse response =
  //       await _repository.getMainCategoryImages(id, categoryName);
  //   _subject3.sink.add(response);
  //   return response.images;
  // }

  getMainCategoryReels(
    int id,
  ) async {
    CategoryReelsResponse response = await _repository.getMainCategoryReels(
      id,
    );
    _subject4.sink.add(response);
    return response.reels;
  }

  BehaviorSubject<CategoryResponse?> get subject => _subject;

  BehaviorSubject<CategoryDetailsResponse?> get subject2 => _subject2;

  BehaviorSubject<CategoryImagesResponse?> get subject3 => _subject3;

  BehaviorSubject<CategoryReelsResponse?> get subject4 => _subject4;

  BehaviorSubject<List<MainCategoryModel>?> get mainCategoriesSubject =>
      _subject5;
}

final categoryBloc = CategoryBloc();
