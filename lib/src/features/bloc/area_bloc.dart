import 'package:rxdart/rxdart.dart';

import '../repository/area_repository.dart';
import '../response/area_response.dart';

class AreasBloc {
  final AreasRepository _repository = AreasRepository();

  final BehaviorSubject<AreasResponse> _subject =
      BehaviorSubject<AreasResponse>();

  getareas([int? size, String? key, int? page]) async {
    AreasResponse response = await _repository.getareas(page, size, key);
    _subject.sink.add(response);
  }

  // editProfile(
  //     String name,
  //     String email,
  //    ) async {
  //   int code = 0;
  //   String message = 'Something went wrong.';
  //   ProfileResponse response = await _repository.editProfile(
  //       name, email,);

  //   if (response.code == "1") {
  //     code = response.code!;
  //     message = response.msg ?? '';

  //     return {'code': code, 'msg': message};
  //   } else {
  //     return {'code': response.code, 'msg': response.msg};
  //   }
  // }

  // changePassword(
  //   String password,
  // ) async {
  //   String code = "0";
  //   String message = 'Something went wrong.';
  //   GeneralResponse response = await _repository.changePassword(password);

  //   if (response.code == "1") {
  //     code = response.code!;
  //     message = response.msg ?? '';

  //     return {'code': code, 'msg': message};
  //   } else {
  //     return {'code': response.code, 'msg': response.msg};
  //   }
  // }

  BehaviorSubject<AreasResponse> get subject => _subject;
}

final areabloc = AreasBloc();
