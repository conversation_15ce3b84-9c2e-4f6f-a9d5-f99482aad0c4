import 'package:rxdart/rxdart.dart';

import '../repository/holiday_homs_repository.dart';
import '../response/holiday_homes_response.dart';

class HolidayHomeBloc {
  final HolidayHomesRepository _repository = HolidayHomesRepository();

  final BehaviorSubject<HolidayHomesResponse> _holidayHomes =
      BehaviorSubject<HolidayHomesResponse>();

  getHolidayHomes([int? page, int? size, String? value]) async {
    HolidayHomesResponse response =
        await _repository.getHolidayHomes(page, size, value);
    _holidayHomes.sink.add(response);
  }

  BehaviorSubject<HolidayHomesResponse> get holidayHomes => _holidayHomes;
}

final HolidayHomeBloc holidayHomeBloc = HolidayHomeBloc();
