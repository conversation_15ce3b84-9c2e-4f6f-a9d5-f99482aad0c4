import 'package:rxdart/rxdart.dart';

import '../repository/car_year_repo.dart';
import '../response/car_year_response.dart';
import '../response/generalResponse.dart';

class CarYearBloc {
  final CarYearsRepository _repository = CarYearsRepository();
  final BehaviorSubject<CarYearResponse> _carYear =
      BehaviorSubject<CarYearResponse>();
  final BehaviorSubject<CarYearResponse> _carYearDeatils =
      BehaviorSubject<CarYearResponse>();

  getCarYear({int? page, int? size}) async {
    CarYearResponse response = await _repository.getCarYears(page, size);
    _carYear.sink.add(response);
  }

  getCarYearDeatils({int? id}) async {
    CarYearResponse response = await _repository.getCarYearDeatils(id);
    _carYearDeatils.sink.add(response);
  }

  addCarYear({String? year}) async {
    GeneralResponse response = await _repository.addCarYear(year);
    if (response.code == 1) {
      getCarYear(page: 1, size: 200);
    }
  }

  deleteCarYear({int? id}) async {
    GeneralResponse response = await _repository.deleteCarYear(id);
    if (response.code == 1) {
      getCarYear(page: 1, size: 200);
    }
  }

  editCarYear({
    int? id,
    String? year,
  }) async {
    GeneralResponse response = await _repository.editCarYear(
      id,
      year,
    );
    if (response.code == 1) {
      getCarYear(page: 1, size: 200);
    }
  }

  BehaviorSubject<CarYearResponse> get carYear => _carYear;
  BehaviorSubject<CarYearResponse> get carYearDeatils => _carYearDeatils;
}

final CarYearBloc carYearBloc = CarYearBloc();
