import 'package:rxdart/rxdart.dart';

import '../repository/contact_us_repo.dart';
import '../response/contact_user_response.dart';

class ContactUsBloc {
  final ConactUsRepository _repository = ConactUsRepository();

  final BehaviorSubject<ContactUserResponse> contactstreamController =
      BehaviorSubject<ContactUserResponse>();

  getContactData({int? page = 1, int? size = 20}) async {
    ContactUserResponse response = await _repository.contactUsData(page, size);
    contactstreamController.sink.add(response);
  }

  // post read
  Future<void> readMessage({required int? id}) async {
    await _repository.readMessage(id);
  }

  BehaviorSubject<ContactUserResponse> get subject => contactstreamController;
}

final contactUsBloc = ContactUsBloc();
