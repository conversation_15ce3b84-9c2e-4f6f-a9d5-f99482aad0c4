import 'package:rxdart/rxdart.dart';

import '../repository/agent_repository.dart';
import '../repository/other_settings_repoistory.dart';
import '../repository/years_repository.dart';
import '../response/agent_list_response.dart';
import '../response/other_settings_response.dart';
import '../response/price_plan_response.dart';
import '../response/property_status_response.dart';
import '../response/year_list_response.dart';

class OtherSettingsBloc {
  final OtherSettingsRepository _repository = OtherSettingsRepository();
  final AgentRepository _agentRepository = AgentRepository();
  final YearsRepository _yearsRepository = YearsRepository();

  final BehaviorSubject<OtherSettingsResponse?> _features =
      BehaviorSubject<OtherSettingsResponse?>();
  final BehaviorSubject<OtherSettingsResponse?> _carBrands =
      BehaviorSubject<OtherSettingsResponse?>();
  final BehaviorSubject<OtherSettingsResponse?> _types =
      BehaviorSubject<OtherSettingsResponse?>();
  final BehaviorSubject<OtherSettingsResponse?> _locations =
      BehaviorSubject<OtherSettingsResponse?>();
  final BehaviorSubject<OtherSettingsResponse?> _adminFeatures =
      BehaviorSubject<OtherSettingsResponse?>();
  final BehaviorSubject<AgentListResponse?> _agents =
      BehaviorSubject<AgentListResponse?>();
  final BehaviorSubject<YearListResponse?> _years =
      BehaviorSubject<YearListResponse?>();
  final BehaviorSubject<PropertyStatusResponse?> _propertyStatus =
      BehaviorSubject<PropertyStatusResponse?>();
  final BehaviorSubject<PricePlanResponse?> _pricePlans =
      BehaviorSubject<PricePlanResponse?>();

  getCarBrands([int? page, int? size, String? key]) async {
    OtherSettingsResponse? response =
        await _repository.getCarBrands(page, size, key);
    _carBrands.sink.add(response);
  }

  gettypes(int? page, int? size, String? key, String category) async {
    OtherSettingsResponse? response =
        await _repository.gettypes(page, size, key, category);
    _types.sink.add(response);
  }

  getLocations() async {
    OtherSettingsResponse? response = await _repository.getLocations();
    _locations.sink.add(response);
  }

  getAdminFeatures(String category) async {
    OtherSettingsResponse? response =
        await _repository.getAdminFeatures(category);
    _adminFeatures.sink.add(response);
  }

  getAgents(String category, int? page, int? size) async {
    AgentListResponse response =
        await _agentRepository.getAgent(category, page, size);
    _agents.sink.add(response);
  }

  getYears([int? page, int? size, String? key]) async {
    YearListResponse response =
        await _yearsRepository.getYears(page, size, key);
    _years.sink.add(response);
  }

  getPropertyStatus([int? page, int? size, String? key]) async {
    PropertyStatusResponse response =
        await _repository.getPropertyStatus(page, size, key);
    _propertyStatus.sink.add(response);
  }

  getPricePlans([int? page, int? size, String? key]) async {
    PricePlanResponse response =
        await _repository.getPricePlans(page, size, key);
    _pricePlans.sink.add(response);
  }

  BehaviorSubject<OtherSettingsResponse?> get features => _features;
  BehaviorSubject<OtherSettingsResponse?> get carBrands => _carBrands;
  BehaviorSubject<OtherSettingsResponse?> get types => _types;
  BehaviorSubject<OtherSettingsResponse?> get locations => _locations;
  BehaviorSubject<OtherSettingsResponse?> get adminFeatures => _adminFeatures;
  BehaviorSubject<AgentListResponse?> get agents => _agents;
  BehaviorSubject<YearListResponse?> get years => _years;
  BehaviorSubject<PropertyStatusResponse?> get propertyStatus =>
      _propertyStatus;
  BehaviorSubject<PricePlanResponse?> get pricePlans => _pricePlans;
}

final othersettingsbloc = OtherSettingsBloc();
