import 'package:rxdart/rxdart.dart';

import '../repository/profile_reposirory.dart';
import '../response/configuration_response.dart';
import '../response/generalResponse.dart';
import '../response/profile_response.dart';

class ProfileBloc {
  final ProfileRepository _repository = ProfileRepository();

  final BehaviorSubject<ProfileResponse> _subject =
      BehaviorSubject<ProfileResponse>();
  final BehaviorSubject<ConfigurationResponse> configurationStream =
      BehaviorSubject<ConfigurationResponse>();

  final BehaviorSubject<AboutResponse> _subject2 =
      BehaviorSubject<AboutResponse>();

  getProfile() async {
    ProfileResponse response = await _repository.getProfile();
    _subject.sink.add(response);
  }

  getConfig() async {
    ConfigurationResponse response = await _repository.getConfig();
    configurationStream.sink.add(response);
  }

  Future<ConfigurationResponse> editConfig(Map info) async {
    ConfigurationResponse response = await _repository.editConfig(info);

    configurationStream.sink.add(response);

    return response;
  }

  Future<GeneralResponse> changePassword(
      String email, String newPassword) async {
    GeneralResponse response =
        await _repository.changePassword(email, newPassword);

    return response;
  }

  editProfile(String name, String email, String phone) async {
    int code = 0;
    String message = 'Something went wrong.';
    ProfileResponse response =
        await _repository.editProfile(name, email, phone);

    if (response.code == 1) {
      code = response.code!;
      message = response.msg ?? '';

      return {'code': code, 'msg': message};
    } else {
      return {'code': response.code, 'msg': response.msg};
    }
  }

  // changePassword(
  //   String password,
  // ) async {
  //   String code = "0";
  //   String message = 'Something went wrong.';
  //   GeneralResponse response = await _repository.changePassword(password);

  //   if (response.code == "1") {
  //     code = response.code!;
  //     message = response.msg ?? '';

  //     return {'code': code, 'msg': message};
  //   } else {
  //     return {'code': response.code, 'msg': response.msg};
  //   }
  // }

  BehaviorSubject<ProfileResponse> get subject => _subject;

  BehaviorSubject<AboutResponse> get subject2 => _subject2;
}

final bloc2 = ProfileBloc();
