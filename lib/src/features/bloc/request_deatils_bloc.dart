import 'package:rxdart/rxdart.dart';

import '../repository/request_repository.dart';
import '../response/holiday_home_details_response.dart';

class RequestDeatilsBloc {
  final RequestRepository _repository = RequestRepository();

  final BehaviorSubject<DeatilsResonse> deatilsStream =
      BehaviorSubject<DeatilsResonse>();

  // getRequestDeatils({int? id, String? categoryName}) async {
  // DeatilsResonse response =
  // await _repository.getRequestDeatils(id, categoryName);
  // deatilsStream.sink.add(response);
  // }
}

final requestDeatilsBloc = RequestDeatilsBloc();
