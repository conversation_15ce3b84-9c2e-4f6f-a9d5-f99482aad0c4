import 'package:rxdart/rxdart.dart';

import '../repository/car_brand_repo.dart';
import '../response/car_brand_deatils.dart';
import '../response/car_brand_response.dart';
import '../response/generalResponse.dart';

class CarBrandBloc {
  final CarBrandsRepository _repository = CarBrandsRepository();
  final BehaviorSubject<CarBrandResponse> _carBrand =
      BehaviorSubject<CarBrandResponse>();
  final BehaviorSubject<CarBrandDeatils> _carBrandDeatils =
      BehaviorSubject<CarBrandDeatils>();

  getCarBrand({int? page, int? size}) async {
    CarBrandResponse response = await _repository.getCarBrands(page, size);
    _carBrand.sink.add(response);
  }

  getCarBrandDeatils({int? id}) async {
    CarBrandDeatils response = await _repository.getCarBrandDeatils(id);
    _carBrandDeatils.sink.add(response);
  }

  addCarBrand({String? arabicName, String? englishName}) async {
    GeneralResponse response =
        await _repository.addCarBrand(arabicName, englishName);
    if (response.code == 1) {
      getCarBrand(page: 1, size: 200);
    }
  }

  deleteCarBrand({int? id}) async {
    GeneralResponse response = await _repository.deleteCarBrand(id);
    if (response.code == 1) {
      getCarBrand(page: 1, size: 200);
    }
  }

  editCarBrand({int? id, String? arabicName, String? englishName}) async {
    GeneralResponse response =
        await _repository.editCarBrand(id, arabicName, englishName);
    if (response.code == 1) {
      getCarBrand(page: 1, size: 200);
    }
  }

  BehaviorSubject<CarBrandResponse> get carBrand => _carBrand;
  BehaviorSubject<CarBrandDeatils> get carBrandDeatils => _carBrandDeatils;
}

final CarBrandBloc carBrandBloc = CarBrandBloc();
