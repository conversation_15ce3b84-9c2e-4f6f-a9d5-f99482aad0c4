import 'package:rxdart/rxdart.dart';

import '../repository/properties_reposotiry.dart';
import '../response/property_list_response.dart';

class PropertyBloc {
  final PropertiesRepository _repository = PropertiesRepository();

  final BehaviorSubject<PropertyListResponse> _properties =
      BehaviorSubject<PropertyListResponse>();

  BehaviorSubject<PropertyListResponse> get properties => _properties;
}

final PropertyBloc propertyBloc = PropertyBloc();
