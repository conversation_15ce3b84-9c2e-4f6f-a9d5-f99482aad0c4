class AlbumsResponse {
  int? code;
  String? msg;
  List<AlbumsData>? data;

  AlbumsResponse({this.code, this.msg, this.data});

  AlbumsResponse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data?.add(AlbumsData.fromJson(v));
      });
    }
  }
  AlbumsResponse.withError(String? errorValue)
      : data = [],
        code = 0,
        msg = "fail";
}

class AlbumsData {
  int? id;
  String? name;
  String? type;
  int? category;
  String? label;
  int? location;
  int? numberofstarts;
  double? latitude;
  double? longitude;
  int? media;
  int? startprice;
  int? endprice;
  String? currency;
  String? endpricecurrency;
  String? greviewlink;
  String? phone;
  String? website;
  String? instagram;
  String? createdAt;
  String? updatedAt;
  int? rating;
  List<Photos>? photos;

  AlbumsData(
      {this.id,
      this.name,
      this.type,
      this.category,
      this.label,
      this.location,
      this.numberofstarts,
      this.latitude,
      this.longitude,
      this.media,
      this.startprice,
      this.endprice,
      this.currency,
      this.endpricecurrency,
      this.greviewlink,
      this.phone,
      this.website,
      this.instagram,
      this.createdAt,
      this.updatedAt,
      this.rating,
      this.photos});

  AlbumsData.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    name = json['name'];
    type = json['type'].toString();
    category = json['category'];
    label = json['label'];
    location = json['location'];
    numberofstarts = json['numberofstarts'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    media = json['media'];
    startprice = json['startprice'];
    endprice = json['endprice'];
    currency = json['currency'];
    endpricecurrency = json['endpricecurrency'];
    greviewlink = json['greviewlink'];
    phone = json['phone'];
    website = json['website'];
    instagram = json['instagram'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    rating = json['rating'];
    if (json['images'] != null) {
      photos = <Photos>[];
      json['images'].forEach((v) {
        photos?.add(Photos.fromJson(v));
      });
    }
  }
}

class Photos {
  int? id;
  // int? mainCategoryId;
  String? image;
  // String? createdat;

  Photos({
    this.id,
    this.image,
  });

  Photos.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    // mainCategoryId = json['main_category_id'];
    image = json['url'];
    // createdat = json['createdat'];
  }

  Map<String?, dynamic> toJson() {
    final Map<String?, dynamic> data = Map<String?, dynamic>();
    data['id'] = this.id;
    data['image'] = this.image;
    return data;
  }
}
