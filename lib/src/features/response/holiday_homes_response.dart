import '../models/holiday_home_list.dart';

class HolidayHomesResponse {
  HolidayHomesResponse({
    this.code,
    this.msg,
    this.data,
  });

  HolidayHomesResponse.fromJson(dynamic json) {
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? HolidayHomeList.fromJson(json['data']) : null;
  }
  int? code;
  String? msg;
  HolidayHomeList? data;
  String? error;

  HolidayHomesResponse.withError(String? errorValue)
      : error = errorValue,
        code = 0,
        msg = "fail";
}
