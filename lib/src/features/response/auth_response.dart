class AuthResponse {
  final Map<String?, dynamic> results;
  final String? error;
  final int? code;
  final String? msg;
  final String? token;
  AuthResponse(this.results, this.error, this.code, this.msg, this.token);

  AuthResponse.fromJson(Map<String?, dynamic> json)
      : results = json['data'].runtimeType == int ? {} : json['data'],

//       :results=json['data'][0],
        error = "",
        code = int.parse(json["code"].toString()),
        token = json['access_token'],
        msg = json["msg"];

  AuthResponse.withError(String? errorValue)
      : results = {},
        error = errorValue,
        code = 0,
        token = "",
        msg = errorValue;
  AuthResponse.fromJsonadd(Map<String?, dynamic> json)
      : results = json['data'],
        error = "",
        code = int.parse(json["code"].toString()),
        token = "",
        msg = json["msg"];
}
