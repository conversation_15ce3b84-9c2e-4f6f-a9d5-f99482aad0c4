import '../models/category_model.dart';
import '../models/featured_video.dart';

class CategoryResponse {
  final List<CategoryModel> category;
  final List<FeaturedVideo> featuredvideo;
  final String? error;
  final int? code;
  final String? msg;
  CategoryResponse.fromJson(Map parsedJson)
      : category = parsedJson['data'].runtimeType == List
            ? (parsedJson['data'] as List)
                .map((p) => CategoryModel.fromJson(p))
                .toList()
            : parsedJson['data']['data'] != null
                ? (parsedJson['data']['data']['data'] as List)
                    .map((p) => CategoryModel.fromJson(p))
                    .toList()
                : [],
        featuredvideo = parsedJson['data'].runtimeType == List
            ? []
            : parsedJson['data']['featured'] != null
                ? (parsedJson['data']['featured']['data'] as List)
                    .map((p) => FeaturedVideo.fromJson(p))
                    .toList()
                : [],
        error = "",
        code = 1,
        msg = "success";
  CategoryResponse.withError(String? errorValue)
      : category = [],
        featuredvideo = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}

class CategoryDetailsResponse {
  final CategoryDetails? result;
  final String? error;
  final int? code;
  final String? msg;
  CategoryDetailsResponse(this.result, this.error, this.code, this.msg);

  CategoryDetailsResponse.fromJson(Map<String?, dynamic> parsedJson)
      : result = CategoryDetails.fromJson(parsedJson['data']['data'][0]),
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  CategoryDetailsResponse.withError(String? errorValue)
      : result = null,
        error = errorValue,
        code = 0,
        msg = "fail";
}

class AboutResponse {
  final dynamic results;
  final String? error;
  final String? code;
  final String? msg;
  AboutResponse(this.results, this.error, this.code, this.msg);

  AboutResponse.fromJson(Map<String?, dynamic> parsedJson)
      : results = parsedJson['data'],

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  AboutResponse.withError(String? errorValue)
      : results = [],
        error = errorValue,
        code = "0",
        msg = "fail";
}
