class GeneralResponse {
  final String? error;
  final int? code;
  final String? msg;
  final dynamic data;
  GeneralResponse({this.error, this.code, this.msg, this.data});

  GeneralResponse.fromJson(Map<String?, dynamic> parsedJson)
      : data = parsedJson['data'],
        error = parsedJson['errors'].toString(),
        code = parsedJson['code'],
        msg = parsedJson['message'];

  GeneralResponse.withError(String? errorValue)
      : data = {},
        error = errorValue,
        code = 0,
        msg = errorValue;
}

class MaxPriceResponse {
  final String? error;
  final String? code;
  final String? msg;
  final int? data;
  MaxPriceResponse({this.error, this.code, this.msg, this.data});

  MaxPriceResponse.fromJson(Map<String?, dynamic> parsedJson)
      : data = parsedJson['data'],
        error = "",
        code = parsedJson['code'].toString(),
        msg = parsedJson['message'];

  MaxPriceResponse.withError(String? errorValue)
      : data = 1,
        error = errorValue,
        code = "0",
        msg = errorValue;
}

class GeneralGetResponse {
  final dynamic results;
  final String? error;
  final int? code;
  final String? msg;
  GeneralGetResponse(this.results, this.error, this.code, this.msg);

  GeneralGetResponse.fromJson(Map<String?, dynamic> parsedJson)
      : results = parsedJson['data'],

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  GeneralGetResponse.withError(String? errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
