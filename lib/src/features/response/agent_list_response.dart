import '../models/agent_list_model.dart';

class AgentListResponse {
  final List<AgentListModel> agentList;
  final String? error;
  final int? code;
  final String? msg;

  AgentListResponse.fromJson(Map parsedJson)
      : agentList = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => AgentListModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  AgentListResponse.withError(String? errorValue)
      : agentList = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
