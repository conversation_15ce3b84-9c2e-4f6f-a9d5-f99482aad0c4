class CarBrandResponse {
  int? code;
  String? msg;
  List<Data>? data;

  CarBrandResponse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data?.add(Data.fromJson(v));
      });
    }
  }
  CarBrandResponse.withError(String? errorValue)
      : code = 0,
        msg = errorValue;
}

class Data {
  int? id;
  String? name;

  Data.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }
}
