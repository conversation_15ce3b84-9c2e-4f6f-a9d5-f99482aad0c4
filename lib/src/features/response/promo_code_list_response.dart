import '../models/promo_code.dart';

class PromoCodeListResponse {
  PromoCodeListResponse({
    this.code,
    this.msg,
    this.data,
  });

  PromoCodeListResponse.fromJson(dynamic json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data?.add(PromoCode.fromJson(v));
      });
    }
  }
  int? code;
  String? msg;
  List<PromoCode>? data;
  String? error;

  PromoCodeListResponse.withError(String? errorValue)
      : error = errorValue,
        code = 0,
        msg = "fail";
}
