import 'package:equatable/equatable.dart';

import '../models/price_plan_model.dart';

class PricePlanResponse extends Equatable {
  final List<PricePlanModel> results;
  final String? error;
  final int? code;
  final String? msg;
  final bool? status;
  
  const PricePlanResponse(this.results, this.error, this.code, this.msg, this.status);

  PricePlanResponse.fromJson(Map<String?, dynamic> parsedJson)
      : results = (parsedJson['data'] as List)
            .map((p) => PricePlanModel.fromJson(p))
            .toList(),
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['message'],
        status = parsedJson['status'];

  PricePlanResponse.withError(String? errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail",
        status = false;

  @override
  List<Object?> get props => [results, error, code, msg, status];
}
