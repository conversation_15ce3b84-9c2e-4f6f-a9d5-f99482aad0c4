class NotificationsResponse {
  int? code;
  String? msg;
  List<Data>? data;

  NotificationsResponse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data?.add(Data.fromJson(v));
      });
    }
  }
  NotificationsResponse.withError(this.msg) : code = 0;
}

class Data {
  String? date;
  List<Notifications>? items;

  Data({this.date, required this.items});

  Data.fromJson(Map<String?, dynamic> json) {
    date = json['date'];
    if (json['items'] != null) {
      items = <Notifications>[];
      json['items'].forEach((v) {
        items?.add(Notifications.fromJson(v));
      });
    }
  }
}

class Notifications {
  int? id;
  String? title;
  String? message;

  Notifications.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    title = json['title'];
    message = json['message'];
  }
}
