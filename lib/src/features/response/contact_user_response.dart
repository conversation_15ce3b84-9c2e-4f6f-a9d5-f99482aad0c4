class ContactUserResponse {
  int? code;
  String? msg;
  List<Data>? data;

  ContactUserResponse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data?.add(new Data.fromJson(v));
      });
    }
  }
  ContactUserResponse.withError(String? errorValue)
      : code = 0,
        msg = errorValue;
}

class Data {
  int? id;
  String? email;
  String? message;
  String? fullname;
  bool haveRead = false;
  String? createdAt;

  Data.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    email = json['email'];
    message = json['message'];
    fullname = json['fullname'];
    createdAt = json['created_at'];
    haveRead = json['read'] == 1 ? true : false;
  }
}
