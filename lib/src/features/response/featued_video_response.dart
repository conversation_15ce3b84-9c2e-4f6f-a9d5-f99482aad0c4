import 'dart:developer';

class FeaturedVideoResponse {
  int? code;
  String? msg;
  List<Data>? data;
  FeaturedVideoResponse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      log('DDD ${json['data']}');
      data = [];
      json['data']['data'].forEach((v) {
        data?.add(Data.fromJson(v));
      });
    }
  }
  FeaturedVideoResponse.withError(String? errorValue)
      : code = 0,
        msg = errorValue;
}

class Data {
  int? id;
  String? title;
  String? photo;
  String? video;
  int? featuredCategory;
  int? featuredHome;

  Data.fromJson(Map<String?, dynamic> json) {
    log('asdkjsdasdsad ${json}');
    id = json['id'];
    title = json['name'];
    photo = json['images'].isNotEmpty ? json['images'][0]['url'] : '';
    video = json["video"];
    featuredCategory = json['featuredCategory'];
    featuredHome = json['featuredHome'];
  }
}
