import '../models/area.dart';

class AreasResponse {
  final List<AreasModel> results;
  final String? error;
  final int? code;
  final String? msg;
  AreasResponse(this.results, this.error, this.code, this.msg);

  AreasResponse.fromJson(Map<String?, dynamic> parsedJson)
      : results = (parsedJson['data'] as List)
            .map((p) => AreasModel.fromJson(p))
            .toList(),

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  AreasResponse.withError(String? errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
