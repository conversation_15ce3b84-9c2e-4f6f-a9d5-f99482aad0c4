import 'package:equatable/equatable.dart';

import '../models/property_status_model.dart';

class PropertyStatusResponse extends Equatable {
  final List<PropertyStatusModel> results;
  final String? error;
  final int? code;
  final String? msg;
  
  const PropertyStatusResponse(this.results, this.error, this.code, this.msg);

  PropertyStatusResponse.fromJson(Map<String?, dynamic> parsedJson)
      : results = (parsedJson['data'] as List)
            .map((p) => PropertyStatusModel.fromJson(p))
            .toList(),
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  PropertyStatusResponse.withError(String? errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail";

  @override
  List<Object?> get props => [results, error, code, msg];
}
