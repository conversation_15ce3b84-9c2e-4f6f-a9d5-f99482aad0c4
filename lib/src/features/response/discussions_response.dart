class DiscussionsResponse {
  int? code;
  String? msg;
  List<DiscussionData>? data;

  DiscussionsResponse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = <DiscussionData>[];
      json['data'].forEach((v) {
        data?.add(new DiscussionData.fromJson(v));
      });
    }
  }
  DiscussionsResponse.withError(String? errorValue)
      : code = 0,
        msg = errorValue;
}

class DiscussionData {
  String? comment;
  String? fullname;
  int? userId;
  int? id;
  String? reply;
  DiscussionData.fromJson(Map<String?, dynamic> json) {
    comment = json['comment'];
    fullname = json['user_id'] != null ? json['user_id']['name'] : null;
    userId = json['user_id'] != null ? json['user_id']['id'] : null;
    id = json['id'];
    reply = json['reply'];
  }
}
