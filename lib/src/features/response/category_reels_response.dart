import 'dart:developer';

class CategoryReelsResponse {
  CategoryReelsResponse({
    this.code,
    this.msg,
    this.reels,
  });

  CategoryReelsResponse.fromJson(dynamic json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      reels = [];
      json['data'].forEach((v) {
        reels?.add(Reel.fromJson(v));
      });
    }
  }
  int? code;
  String? msg;
  List<Reel>? reels;

  CategoryReelsResponse.withError(String? errorValue) {
    code = 0;
    msg = "fail";
  }
}

class Reel {
  Reel.fromJson(dynamic json) {
    log('asdladsa $json');
    id = json["id"];
    title = json['title'];
    video = json['url'];
    createat = json['createat'];
  }
  String? title;
  String? video;
  int? id;
  String? createat;
}
