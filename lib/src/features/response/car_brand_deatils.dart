class CarBrandDeatils {
  int? code;
  String? msg;
  List<Data>? data;

  CarBrandDeatils.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data?.add(new Data.fromJson(v));
      });
    }
  }
  CarBrandDeatils.withError(String? errorValue)
      : code = 0,
        msg = errorValue;
}

class Data {
  String? nameEn;
  String? nameAr;

  Data.fromJson(Map<String?, dynamic> json) {
    nameEn = json['name_en'];
    nameAr = json['name_ar'];
  }
}
