import 'package:equatable/equatable.dart';

import '../models/other_settings.dart';

class OtherSettingsResponse extends Equatable {
  final List<OtherSettingsModel> results;
  final String? error;
  final int? code;
  final String? msg;
  const OtherSettingsResponse(this.results, this.error, this.code, this.msg);

  OtherSettingsResponse.fromJson(Map<String?, dynamic> parsedJson)
      : results = (parsedJson['data'] as List)
            .map((p) => OtherSettingsModel.fromJson(p))
            .toList(),

//       :results=json['data'][0],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  OtherSettingsResponse.withError(String? errorValue)
      : results = [],
        error = errorValue,
        code = 0,
        msg = "fail";

  @override
  List<Object?> get props => [results, error, code, msg];
}
