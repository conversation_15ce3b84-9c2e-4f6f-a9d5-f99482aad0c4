class CarYearResponse {
  int? code;
  String? msg;
  List<Data>? data;

  CarYearResponse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data?.add(Data.fromJson(v));
      });
    }
  }
  CarYearResponse.withError(String? errorValue)
      : code = 0,
        msg = errorValue;
}

class Data {
  int? id;
  String? year;

  Data.fromJson(Map<String?, dynamic> json) {
    id = json['id'];
    year = json['year'];
  }
}
