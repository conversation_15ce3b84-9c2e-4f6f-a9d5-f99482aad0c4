import '../models/property_list.dart';

class PropertyListResponse {
  PropertyListResponse({
    this.code,
    this.msg,
    this.properties,
  });

  PropertyListResponse.fromJson(dynamic json) {
    code = json['code'];
    msg = json['msg'];
    properties =
        json['data'] != null ? PropertyList.fromJson(json['data']) : null;
  }
  int? code;
  String? msg;
  PropertyList? properties;
  String? error;

  PropertyListResponse.withError(String? errorValue)
      : error = errorValue,
        code = 0,
        msg = "fail";
}
