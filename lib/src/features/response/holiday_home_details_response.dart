class DeatilsResonse {
  int? code;
  String? msg;
  Data? data;
  DeatilsResonse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }
  DeatilsResonse.withError(String? errorValue) {
    code = 0;
    msg = errorValue;
  }
}

class Data {
  Deatils? deatils;
  Data.fromJson(Map<String?, dynamic> json) {
    deatils = json['holiday_home'] != null
        ? new Deatils.fromJson(json['holiday_home'])
        : new Deatils.fromJson(json['car_rent']);
  }
}

class Deatils {
  int? holidayHomeId;
  String? name;
  String? status;
  String? fullname;
  String? requestedat;
  String? locationName;
  int? numberofpeople;
  String? total;
  String? image;
  String? phone;
  String? startdate;
  String? enddate;
  String? agentname;
  String? agentphone;
  int? days;
  String? subtotal;
  String? vat;
  String? fee;
  String? discount;
  int? car;
  int? isprivatedrop;
  int? isdropoff;
  String? usernote;
  String? pricecar;
  String? privatedriverprice;

  Deatils.fromJson(Map<String?, dynamic> json) {
    holidayHomeId = json['holiday_home_id'];
    name = json['name'];
    status = json['status'];
    fullname = json['fullname'];
    requestedat = json['requestedat'];
    locationName = json['location_name'];
    numberofpeople = json['numberofpeople'];
    total = json['total'];
    image = json['image'];
    phone = json['phone'];
    startdate = json['startdate'];
    enddate = json['enddate'];
    agentname = json['agentname'];
    agentphone = json['agentphone'];
    days = json['days'];
    subtotal = json['subtotal'];
    vat = json['vat'];
    fee = json['fee'];
    discount = json['discount'];
    car = json['car'];
    isprivatedrop = json['isprivatedrop'];
    isdropoff = json['isdropoff'];
    usernote = json['usernote'];
    pricecar = json['pricecar'];
    privatedriverprice = json['privatedriverprice'];
  }
}
