import '../models/year.dart';

class YearListResponse {
  int? code;
  String? msg;
  String? error;
  List<Year>? years;

  YearListResponse({
    this.code,
    this.msg,
    this.years,
  });

  YearListResponse.fromJson(dynamic json) {
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      years = [];
      json['data'].forEach((v) {
        years?.add(Year.fromJson(v));
      });
    }
  }

  YearListResponse.withError(String? errorValue)
      : years = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
