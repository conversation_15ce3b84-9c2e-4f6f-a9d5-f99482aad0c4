import '../models/request_mode_list.dart';

class RequestListResponse {
  final List<RequestListModel> reqList;
  final String? error;
  final int? code;
  final String? msg;

  RequestListResponse.fromJson(Map parsedJson)
      : reqList = (parsedJson['data']['car_rent'] as List).isNotEmpty
            ? (parsedJson['data']['car_rent'] as List)
                .map((p) => RequestListModel.fromJson(p))
                .toList()
            : (parsedJson['data']['holiday_home'] as List).isNotEmpty
                ? (parsedJson['data']['holiday_home'] as List)
                    .map((p) => RequestListModel.fromJson(p))
                    .toList()
                : [],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  RequestListResponse.withError(String? errorValue)
      : reqList = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
