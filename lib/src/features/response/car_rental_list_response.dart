import '../models/car_rental_list.dart';

class CarRentalListResponse {
  int? code;
  String? msg;
  CarRentalList? carRentals;
  String? error;

  CarRentalListResponse({
    this.code,
    this.msg,
    this.carRentals,
  });

  CarRentalListResponse.fromJson(dynamic json) {
    code = json['code'];
    msg = json['msg'];
    carRentals =
        json['data'] != null ? CarRentalList.fromJson(json['data']) : null;
  }

  CarRentalListResponse.withError(String? errorValue)
      : error = errorValue,
        code = 0,
        msg = "fail";
}
