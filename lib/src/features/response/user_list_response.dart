import '../models/user_list_model.dart';

class UserListResponse {
  final List<UserListModel> userList;
  final List<CountryModel> countryList;
  final String? error;
  final int? code;
  final String? msg;

  UserListResponse.fromJson(Map parsedJson)
      : userList = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => UserListModel.fromJson(p))
                .toList()
            : [],
        countryList = parsedJson['all_countries'] != null
            ? (parsedJson['all_countries'] as List)
                .map((p) => CountryModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  UserListResponse.withError(String? errorValue)
      : userList = [],
        countryList = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
