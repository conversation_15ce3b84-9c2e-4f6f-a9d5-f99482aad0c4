class CategoryImagesResponse {
  int? code;
  String? msg;
  List<String>? images;
  List<int>? imagesId;
  List<String>? createdat;
  CategoryImagesResponse.fromJson(Map<String?, dynamic> json) {
    code = json['code'];
    msg = json['msg'];

    if (json['data'] != null) {
      images = [];
      imagesId = [];
      createdat = [];
      json['data'].forEach((v) {
        images?.add(v["image"]);
        imagesId?.add(v["id"]);
        createdat?.add(v["createdat"].split(" ")[0]);
      });
    }
  }
  CategoryImagesResponse.withError(String? errorValue) {
    code = 0;
    msg = "errorValue";
  }
}
