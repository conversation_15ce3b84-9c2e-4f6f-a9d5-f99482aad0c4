class ConfigurationResponse {
  int? code;
  String? msg;
  List<Configurations>? confugrations;

  ConfigurationResponse.fromJson(dynamic json) {
    print('asdsadsadsadsa ${json['data']}');

    if (json['data'] == 1 || json['data'] == -1) {
      code = json['data'];
      msg = json['msg'];
      confugrations = null;
      return;
    }
    code = json['code'];
    msg = json['msg'];
    confugrations = json['data'] != null
        ? (json['data'] as List).map((i) => Configurations.fromJson(i)).toList()
        : null;
  }
  ConfigurationResponse.withError(String? errorValue)
      : code = 0,
        msg = "fail";
}

class Configurations {
  int? id;
  String? key;
  String? value;
  // int? vat;
  // int? tourismfees;
  // String? youtubeLink;
  // String? instagram;
  // TermsCondtionAr? termsCondtionEn;
  // TermsCondtionAr? termsCondtionAr;

  Configurations.fromJson(Map<dynamic, dynamic> json) {
    id = json['id'];
    key = json['key'];
    value = json['value'];
  }
}

class TermsCondtionAr {
  int? id;
  String? termsCondtion;

  TermsCondtionAr.fromJson(Map<dynamic, dynamic> json) {
    id = json['id'];
    termsCondtion = json['terms_condtion'];
  }
}
