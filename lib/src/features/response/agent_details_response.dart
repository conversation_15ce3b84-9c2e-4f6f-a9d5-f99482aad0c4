import '../models/agnet_deails_model.dart';

class AgentDetailsResponse {
  final List<AgentDetailsModel> agenItem;
  final String? error;
  final int? code;
  final String? msg;

  AgentDetailsResponse.fromJson(Map parsedJson)
      : agenItem = parsedJson['data'] != null
            ? (parsedJson['data'] as List)
                .map((p) => AgentDetailsModel.fromJson(p))
                .toList()
            : [],
        error = "",
        code = parsedJson['code'],
        msg = parsedJson['msg'];

  AgentDetailsResponse.withError(String? errorValue)
      : agenItem = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
