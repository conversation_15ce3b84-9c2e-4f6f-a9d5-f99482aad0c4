import '../api_provider/request_api_provider.dart';
import '../response/request_list_response.dart';

class RequestRepository {
  RequestApiProvider _apiProvider = RequestApiProvider();

  Future<RequestListResponse> getRequestList(
          String? category, String? val, String? date, String? agentId,
          [int? page, int? size]) =>
      _apiProvider.getRequestList(category!, val!, date!, agentId!, page, size);
  // Future<DeatilsResonse> getRequestDeatils(int? id, String? categoryName) =>
  //     _apiProvider.getRequestDeatils(id: id, categoryName: categoryName);

/*  Future<AgentDetailsResponse> getAgentDetails(String? id) =>
      _apiProvider.getAgentDetails(id);*/
}
