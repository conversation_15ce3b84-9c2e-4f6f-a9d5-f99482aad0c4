import 'package:dio/dio.dart';

import '../api_provider/place_api_provider.dart';
import '../response/generalResponse.dart';

class PlacesRepository {
  PlaceApiProvider _apiProvider = PlaceApiProvider();
  // Future<AreasResponse> getareas([int? page, int? size, String? key]) =>
  //     _apiProvider.getareas(size, key, page);
  Future<GeneralResponse> addPlace(FormData data) =>
      _apiProvider.addPlace(data);
  Future<GeneralResponse> editPlace(FormData data) =>
      _apiProvider.editPlace(data);
}
