import 'package:admin_dubai/src/features/models/main_category_model.dart';
import 'package:dio/dio.dart';

import '../api_provider/category_api_provider.dart';
import '../response/category_reels_response.dart';
import '../response/category_response.dart';
import '../response/generalResponse.dart';

class CategoryRepository {
  final CategoryApiProvider _apiProvider = CategoryApiProvider();
  Future<CategoryResponse> getCategories(String? category,
          [int? page, int? size, String? key]) =>
      _apiProvider.getCategories(category!, page, size, key);

  Future<CategoryDetailsResponse> getCategoryDetails(
          int? id, String? categoryName) =>
      _apiProvider.getMainCategoryDetails(id: id, categoryName: categoryName);

  // Future<CategoryImagesResponse> getMainCategoryImages(
  //         int? id, String? categoryName) =>
  //     _apiProvider.getMainCategoryImages(id!, categoryName);

  Future<List<MainCategoryModel>> getMainCategories() =>
      _apiProvider.getMainCategories();

  Future<CategoryReelsResponse> getMainCategoryReels(
    int? id,
  ) =>
      _apiProvider.getMainCategoryReals(
        id!,
      );

  //! edit Price
  Future<GeneralResponse> editStartEndPrice(FormData data) =>
      _apiProvider.editStartEndPrice(data);
}
