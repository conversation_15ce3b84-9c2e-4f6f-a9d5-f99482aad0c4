import 'package:dio/dio.dart';

import '../api_provider/hotel_api_provider.dart';
import '../response/album_response.dart';
import '../response/generalResponse.dart';

class HotelsRepository {
  final HotelApiProvider _apiProvider = HotelApiProvider();
  // Future<AreasResponse> getareas([int? page, int? size, String? key]) =>
  //     _apiProvider.getareas(size, key, page);
  Future<GeneralResponse> addHotel(FormData data) =>
      _apiProvider.addHotel(data);

  Future<GeneralResponse> editHotel(FormData data) =>
      _apiProvider.editHotel(data);

  Future<GeneralResponse> uploadCategoryImages(
          FormData data, String? categoryName) =>
      _apiProvider.uploadCategoryImages(data, categoryName);

  Future<GeneralResponse> uploadCategoryVideo(FormData data) =>
      _apiProvider.uploadCategoryVideo(
        data,
      );

  Future<GeneralResponse> removeHotel(int? id) => _apiProvider.deleteHotel(id!);
  Future<GeneralResponse> deleteImage(
          int? id, String? categoryName, int? albumId) =>
      _apiProvider.deleteImage(id!, categoryName, albumId!);

  Future<GeneralResponse> deleteVideo(int? id, String? categoryName) =>
      _apiProvider.deleteVideo(id!, categoryName);
  Future<GeneralResponse> addalbum(form) => _apiProvider.addAlbum(form);
  Future<GeneralResponse> updateAlbum(form) => _apiProvider.updateAlbum(form);
  Future<GeneralResponse> deleteAlbum(id) => _apiProvider.deleteAlbum(id);
  Future<AlbumsResponse> getAlbums(categoryId) =>
      _apiProvider.getAlbums(categoryId);
}
