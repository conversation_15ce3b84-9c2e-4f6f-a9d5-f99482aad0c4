import '../api_provider/auth_api_provider.dart';
import '../response/auth_response.dart';

class AuthRepository {
  AuthApiProvider _apiProvider = AuthApiProvider();
  Future<AuthResponse> login(
          String? email, String? password, String? phoneToken) =>
      _apiProvider.login(email!, password!, phoneToken);
  Future<AuthResponse> sendrequestpassword(String? email) =>
      _apiProvider.sendrequestpassword(email!);
  Future<AuthResponse> verfiycode(String? email, String? code) =>
      _apiProvider.verfiycode(email!, code!);
  Future<AuthResponse> resetpassword(String? email, String? password) =>
      _apiProvider.resetpassword(email!, password!);
}
