import 'package:dio/dio.dart';

import '../api_provider/shop_api_provider.dart';
import '../response/generalResponse.dart';

class ShopsRepository {
  ShopApiProvider _apiProvider = ShopApiProvider();
  // Future<AreasResponse> getareas([int? page, int? size, String? key]) =>
  //     _apiProvider.getareas(size, key, page);
  Future<GeneralResponse> addShop(FormData data) => _apiProvider.addShop(data);
  Future<GeneralResponse> editShop(FormData data) =>
      _apiProvider.editShop(data);
}
