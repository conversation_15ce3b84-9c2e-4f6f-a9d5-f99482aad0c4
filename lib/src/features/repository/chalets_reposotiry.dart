import 'package:admin_dubai/src/features/api_provider/chalet_api_provider.dart';
import 'package:dio/dio.dart';

import '../response/generalResponse.dart';

class ChaletsRepository {
  final ChaletApiProvider _apiProvider = ChaletApiProvider();

  Future<GeneralResponse> addChalet(FormData data) =>
      _apiProvider.addChalet(data);

  Future<GeneralResponse> editChalet(FormData data) =>
      _apiProvider.editChalet(data);
}
