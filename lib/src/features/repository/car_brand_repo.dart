import '../api_provider/car_brand_api_provider.dart';
import '../response/car_brand_deatils.dart';
import '../response/car_brand_response.dart';
import '../response/generalResponse.dart';

class CarBrandsRepository {
  CarBrandApiProvider _apiProvider = CarBrandApiProvider();

  Future<CarBrandResponse> getCarBrands(int? page, int? size) =>
      _apiProvider.getCarBrands(page, size);
  Future<GeneralResponse> addCarBrand(
          String? arabicName, String? englishName) =>
      _apiProvider.addCarBrand(arabicName, englishName);
  Future<GeneralResponse> deleteCarBrand(int? id) =>
      _apiProvider.deleteCarBrand(id!);
  Future<GeneralResponse> editCarBrand(
          int? id, String? arabicName, String? englishName) =>
      _apiProvider.editCarBrand(id!, arabicName, englishName);
  Future<CarBrandDeatils> getCarBrandDeatils(int? id) =>
      _apiProvider.getCarBrandById(id!);
  // Future<CarBrandResponse> getCarBrandById(int? id) =>
  //     _apiProvider.getCarBrandById(id);
  // Future<CarBrandResponse> updateCardBrand(int? page, int? size) =>
  //     _apiProvider.updateCardBrand(page, size);
  // Future<CarBrandResponse> deleteCarBrand(int? id) =>
  //     _apiProvider.deleteCarBrand(id);
}
