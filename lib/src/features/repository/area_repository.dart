import 'package:dio/dio.dart';

import '../api_provider/area_api_provider.dart';
import '../response/area_response.dart';
import '../response/generalResponse.dart';

class AreasRepository {
  AreaApiProvider _apiProvider = AreaApiProvider();
  Future<AreasResponse> getareas([int? page, int? size, String? key]) =>
      _apiProvider.getareas(size, key, page);
  Future<GeneralResponse> addarea(FormData data) => _apiProvider.addarea(data);
}
