import '../api_provider/promo_code_api_provider.dart';
import '../response/generalResponse.dart';
import '../response/promo_code_list_response.dart';

class PromoCodeRepository {
  PromoCodeApiProvider _apiProvider = PromoCodeApiProvider();

  Future<PromoCodeListResponse> getPromoCodes([int? page, int? size]) =>
      _apiProvider.getPromoCodes(page, size);

  Future<GeneralResponse> addPromoCode(
          String? code,
          String? discount,
          String? finishAt,
          int? agentId,
          String? title,
          String? titleAr,
          String? description,
          String? descriptionAr,
          bool? isPublished,
          int? rmsDiscountId) =>
      _apiProvider.addPromoCode(code!, discount!, finishAt!, agentId!, title,
          titleAr, description, descriptionAr, isPublished, rmsDiscountId);

  Future<GeneralResponse> deletePromoCode(int? id) =>
      _apiProvider.deletePromoCode(id!);

  Future<GeneralResponse> editPromoCode(
          int? id,
          String? code,
          String? discount,
          String? finishat,
          int? agent,
          title,
          titleAr,
          description,
          descriptionAr,
          isPublished,
          int? rmsDiscountId) =>
      _apiProvider.editPromoCode(
          id!,
          code!,
          discount!,
          finishat!,
          agent!,
          title,
          titleAr,
          description,
          descriptionAr,
          isPublished,
          rmsDiscountId);
}
