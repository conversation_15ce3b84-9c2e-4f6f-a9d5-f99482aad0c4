import '../api_provider/profile_api_provider.dart';
import '../response/configuration_response.dart';
import '../response/generalResponse.dart';
import '../response/profile_response.dart';

class ProfileRepository {
  final ProfileApiProvider _apiProvider = ProfileApiProvider();
  Future<ProfileResponse> getProfile() => _apiProvider.getProfile();
  Future<ConfigurationResponse> getConfig() => _apiProvider.getConfig();
  Future<ConfigurationResponse> editConfig(Map info) =>
      _apiProvider.editConfig(info);

  Future<ProfileResponse> editProfile(
          String? name, String? email, String? phone) =>
      _apiProvider.editProfile(name!, email!, phone!);
  Future<GeneralResponse> changePassword(
          String? oldPassword, String? newPassword) =>
      _apiProvider.changePassword(oldPassword!, newPassword!);
  // Future<GeneralResponse> changePassword(String? password) =>
  //     _apiProvider.changePassword(password);
}
