import '../api_provider/discussions_api_provider.dart';
import '../response/discussions_response.dart';
import '../response/generalResponse.dart';

class DiscussionsRepo {
  DiscussionsApiProvider _apiProvider = DiscussionsApiProvider();

  Future<DiscussionsResponse> getDiscussions(
          int? id, int? page, int? size, String? categoryName) =>
      _apiProvider.getDiscussions(
          id: id, page: page, size: size, categoryName: categoryName);
  Future<GeneralResponse> deleteDiscussions(int? id) {
    Future<GeneralResponse> response = _apiProvider.deleteDiscussions(id: id);
    return response;
  }

  Future<GeneralResponse> replycommentdiscussion(int? id, String? reply) {
    Future<GeneralResponse> response =
        _apiProvider.replycommentdiscussion(id: id, reply: reply);
    return response;
  }

  Future<GeneralResponse> deletediscussion(
    int? id,
  ) {
    Future<GeneralResponse> response = _apiProvider.deletediscussion(
      id: id,
    );
    return response;
  }

  Future<GeneralResponse> snedDiscussion(
          int? id, String? comment, String? categoryName) =>
      _apiProvider.sendDiscussions(
          id: id, comment: comment, categoryName: categoryName);
  Future<GeneralResponse> editComment(
          int? id, String? comment, String? categoryName) =>
      _apiProvider.editComment(
          id: id, comment: comment, categoryName: categoryName);
}
