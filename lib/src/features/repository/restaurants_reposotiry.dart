import 'package:dio/dio.dart';

import '../api_provider/resturant_api_provider.dart';
import '../response/generalResponse.dart';

class RestaurantsRepository {
  RestaurantApiProvider _apiProvider = RestaurantApiProvider();
  // Future<AreasResponse> getareas([int? page, int? size, String? key]) =>
  //     _apiProvider.getareas(size, key, page);
  Future<GeneralResponse> addRestaurant(FormData data) =>
      _apiProvider.addRestaurant(data);
  Future<GeneralResponse> editRestaurant(FormData data) =>
      _apiProvider.editRestaurant(data);
}
