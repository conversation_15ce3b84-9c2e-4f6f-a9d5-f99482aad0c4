import '../api_provider/featured_api_provider.dart';
import '../response/featued_video_response.dart';
import '../response/generalResponse.dart';

class FeatureRepo {
  final FeaturedApiProvider _apiProvider = FeaturedApiProvider();

  Future<FeaturedVideoResponse> getFeaturedVideos(
          int? page, int? size, int? filteras, int? category, String? key) =>
      _apiProvider.getFeaturedVideos(
          page: page,
          size: size,
          filteras: filteras,
          category: category,
          key: key);
  Future<GeneralResponse> addfeaturedvideotocategory(int? id) =>
      _apiProvider.addfeaturedvideotocategory(id: id);

  Future<GeneralResponse> addfeaturedvideotohome(int? id) =>
      _apiProvider.addfeaturedvideotohome(id: id);

  Future<GeneralResponse> removefeaturedvideofromcategory(int? id) =>
      _apiProvider.removefeaturedvideofromcategory(id: id);

  Future<GeneralResponse> removefeaturedvideofromhome(int? id) =>
      _apiProvider.removefeaturedvideofromhome(id: id);
}
