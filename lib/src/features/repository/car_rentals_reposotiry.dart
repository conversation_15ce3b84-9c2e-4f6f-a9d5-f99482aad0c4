import 'package:dio/dio.dart';

import '../api_provider/car_rental_api_provider.dart';
import '../response/car_rental_list_response.dart';
import '../response/generalResponse.dart';

class CarRentalsRepository {
  CarRentalApiProvider _apiProvider = CarRentalApiProvider();
  // Future<AreasResponse> getareas([int? page, int? size, String? key]) =>
  //     _apiProvider.getareas(size, key, page);
  Future<GeneralResponse> addCarRental(FormData data) =>
      _apiProvider.addCarRental(data);

  Future<GeneralResponse> editCarRental(FormData data) =>
      _apiProvider.editCarRental(data);

  Future<CarRentalListResponse> getCarRentals(
          [int? page, int? size, String? key]) =>
      _apiProvider.getCarRentals(page, size, key);
  Future<GeneralResponse> deleteCarRent(int? id) =>
      _apiProvider.deleteCarRent(id!);

  //! edit Price
  Future<GeneralResponse> editStartEndPrice(FormData data) =>
      _apiProvider.editStartEndPrice(data);
}
