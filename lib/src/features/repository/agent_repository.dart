import 'dart:io';

import '../api_provider/agent_api_provider.dart';
import '../response/agent_details_response.dart';
import '../response/agent_list_response.dart';
import '../response/generalResponse.dart';

class AgentRepository {
  AgentApiProvider _apiProvider = AgentApiProvider();

  Future<AgentListResponse> getAgent(String? category,
          [int? page, int? size]) =>
      _apiProvider.getAgent(category!, page, size);

  Future<GeneralResponse> addAgent(
          String? companyname,
          String? categ,
          String? datev,
          String? name,
          String? nameAr,
          String? email,
          String? password,
          String? mobile,
          String? code,
          File? image) =>
      _apiProvider.addAgent(companyname!, categ!, datev!, name!, nameAr!,
          email!, password!, mobile!, code!, image);

  Future<GeneralResponse> updateAgent(
          String? companyname,
          String? categ,
          String? datev,
          String? name,
          String? nameAr,
          String? email,
          String? password,
          String? mobile,
          String? code,
          String? id,
          File? image) =>
      _apiProvider.updateAgent(
          companyname ?? '',
          categ ?? '',
          datev ?? '',
          name ?? '',
          nameAr ?? '',
          email ?? '',
          password ?? '',
          mobile ?? '',
          code ?? '',
          id ?? '',
          image);

  Future<GeneralResponse> deleteAgent(String? id) =>
      _apiProvider.deleteAgent(id!);

  Future<AgentDetailsResponse> getAgentDetails(String? id) =>
      _apiProvider.getAgentDetails(id!);
}
