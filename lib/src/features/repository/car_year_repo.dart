import '../api_provider/car_year_api_provider.dart';
import '../response/car_year_response.dart';
import '../response/generalResponse.dart';

class CarYearsRepository {
  final CarYearApiProvider _apiProvider = CarYearApiProvider();

  Future<CarYearResponse> getCarYears(int? page, int? size) =>
      _apiProvider.getCarYears(page, size);
  Future<GeneralResponse> addCarYear(String? year) =>
      _apiProvider.addCarYear(year);
  Future<GeneralResponse> deleteCarYear(int? id) =>
      _apiProvider.deleteCarYear(id!);
  Future<GeneralResponse> editCarYear(int? id, String? year) =>
      _apiProvider.editCarYear(id!, year);
  Future<CarYearResponse> getCarYearDeatils(int? id) =>
      _apiProvider.getCarYearById(id!);
}
