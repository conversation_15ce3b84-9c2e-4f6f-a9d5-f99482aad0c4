import 'package:dio/dio.dart';

import '../api_provider/holiday_home_api_provider.dart';
import '../response/generalResponse.dart';
import '../response/holiday_homes_response.dart';

class HolidayHomesRepository {
  final HolidayHomeApiProvider _apiProvider = HolidayHomeApiProvider();
  Future<HolidayHomesResponse> getHolidayHomes(
          [int? page, int? size, String? key]) =>
      _apiProvider.getHolidayHomes(page, size, key);

  Future<GeneralResponse> addHolidayHome(FormData data) =>
      _apiProvider.addHolidayHome(data);

  Future<GeneralResponse> editHolidayHome(FormData data) =>
      _apiProvider.editHolidayHome(data);

  //! edit Price
  Future<GeneralResponse> editStartEndPrice(FormData data) =>
      _apiProvider.editStartEndPrice(data);

  Future<GeneralResponse> deleteHolidayHome(int? id) =>
      _apiProvider.deleteHolidayHome(id!);
}
