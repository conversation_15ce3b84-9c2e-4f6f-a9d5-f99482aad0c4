import 'package:dio/dio.dart';

import '../api_provider/activity_api_provider.dart';
import '../response/generalResponse.dart';

class ActivitiesRepository {
  ActivityApiProvider _apiProvider = ActivityApiProvider();

  Future<GeneralResponse> addActivity(FormData data) =>
      _apiProvider.addActivity(data);

  Future<GeneralResponse> editActivity(FormData data) =>
      _apiProvider.editActivity(data);
}
