import 'package:dio/dio.dart';

import '../api_provider/property_api_provider.dart';
import '../response/generalResponse.dart';

class PropertiesRepository {
  PropertyApiProvider _apiProvider = PropertyApiProvider();

  Future<GeneralResponse> addProperty(FormData data) =>
      _apiProvider.addProperty(data);

  // Future<GeneralResponse?> addProperty(FormData data) =>
  //     _apiProvider.addProperty(data);
  Future<GeneralResponse> editProperty(FormData data) =>
      _apiProvider.editProperty(data);

  Future<GeneralResponse> deletePropertie(int? id) =>
      _apiProvider.deletePropertie(id!);

  //! edit Price
  Future<GeneralResponse> editStartEndPrice(FormData data) =>
      _apiProvider.editStartEndPrice(data);
}
