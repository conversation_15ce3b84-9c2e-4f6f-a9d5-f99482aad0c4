import 'package:dio/dio.dart';

import '../api_provider/project_api_provider.dart';
import '../response/generalResponse.dart';

class ProjectRepository {
  final ProjectApiProvider _apiProvider = ProjectApiProvider();

  Future<GeneralResponse> addProject(FormData data) =>
      _apiProvider.addProject(data);

  Future<GeneralResponse> editProject(FormData data) =>
      _apiProvider.editProject(data);

  Future<GeneralResponse> deleteProject(int id) =>
      _apiProvider.deleteProject(id);
}
