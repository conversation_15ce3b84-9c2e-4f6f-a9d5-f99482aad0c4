// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "AED": MessageLookupByLibrary.simpleMessage("درهم اماراتي"),
    "Accepted": MessageLookupByLibrary.simpleMessage("مقبول"),
    "Activities": MessageLookupByLibrary.simpleMessage("أنشطة"),
    "ActivityName": MessageLookupByLibrary.simpleMessage("اسم النشاط"),
    "Actvities": MessageLookupByLibrary.simpleMessage("النشاطات"),
    "AddAgent": MessageLookupByLibrary.simpleMessage("اضافة عميل"),
    "AddApplicationfeature": MessageLookupByLibrary.simpleMessage(
      "اضافة ميزات للتطبيق",
    ),
    "AddCaterogryfeature": MessageLookupByLibrary.simpleMessage(
      "اضافة ميزة للفئة",
    ),
    "AddNewActivity": MessageLookupByLibrary.simpleMessage("اضافة نشاط جديد"),
    "AddNewArea": MessageLookupByLibrary.simpleMessage(" اضافة منطقة جديدة"),
    "AddNewBrand": MessageLookupByLibrary.simpleMessage(
      "اضافة علامة تجارية جديدة",
    ),
    "AddNewChalet": MessageLookupByLibrary.simpleMessage("اضافة شاليه جديد"),
    "AddNewDestination": MessageLookupByLibrary.simpleMessage(
      "اضافة وجهة جديدة",
    ),
    "AddNewFeature": MessageLookupByLibrary.simpleMessage("اضافة ميزة جديدة"),
    "AddNewPromoCode": MessageLookupByLibrary.simpleMessage(
      "أضف رمز ترويجي جديد",
    ),
    "AddNewRentalCars": MessageLookupByLibrary.simpleMessage(
      "+ إضافة تأجير سيارات جديدة",
    ),
    "AddNewYear": MessageLookupByLibrary.simpleMessage("اضافة سنة جديدة"),
    "AddNewagent": MessageLookupByLibrary.simpleMessage("اضافة عميل جديد"),
    "AddPropertyStatus": MessageLookupByLibrary.simpleMessage(
      "اضافة حالة عقار",
    ),
    "AddnewArea": MessageLookupByLibrary.simpleMessage("اضافة منطقة جديدة"),
    "AddnewHolidayHome": MessageLookupByLibrary.simpleMessage(
      "اضافة بيت اجازة جديد",
    ),
    "AddnewPromoCode": MessageLookupByLibrary.simpleMessage(
      "اضافة رمز ترويجي جديد",
    ),
    "AddnewProperty": MessageLookupByLibrary.simpleMessage("اضافة عقار جديد"),
    "Addnewhotel": MessageLookupByLibrary.simpleMessage("اضافة فندق جديد"),
    "Addnewrentalcar": MessageLookupByLibrary.simpleMessage(
      "اضافة سيارة جديدة للتأجير",
    ),
    "Addnewrentalcars": MessageLookupByLibrary.simpleMessage(
      "اضافة سيارات جديدة للتأجير",
    ),
    "Addnewresturant": MessageLookupByLibrary.simpleMessage(
      "اضافة مطاعم جديدة",
    ),
    "Addnewshop": MessageLookupByLibrary.simpleMessage("اضافة متجر جديد"),
    "Addnewtype": MessageLookupByLibrary.simpleMessage("اضافة نوع جديد"),
    "AdminAccountInformation": MessageLookupByLibrary.simpleMessage(
      "معلومات حساب المسؤول",
    ),
    "Agent": MessageLookupByLibrary.simpleMessage("العميل"),
    "AgentCompanyName": MessageLookupByLibrary.simpleMessage("وكيل اسم الشركة"),
    "AgentNote": MessageLookupByLibrary.simpleMessage("ملاحظة الوكيل"),
    "Agentcompanyname": MessageLookupByLibrary.simpleMessage("اسم شركة العميل"),
    "Agentname": MessageLookupByLibrary.simpleMessage("اسم العميل"),
    "Agentnote": MessageLookupByLibrary.simpleMessage("ملاحظة العميل"),
    "Agents": MessageLookupByLibrary.simpleMessage("الوكلاء"),
    "Agenty": MessageLookupByLibrary.simpleMessage("وكالة"),
    "AgreedPrice": MessageLookupByLibrary.simpleMessage("السعر المتفق عليه"),
    "AgreementTotalPrice": MessageLookupByLibrary.simpleMessage(
      "السعر الإجمالي للاتفاقية",
    ),
    "AlMalekFahedStreet": MessageLookupByLibrary.simpleMessage(
      "شارع الملك فهد",
    ),
    "AllAgents": MessageLookupByLibrary.simpleMessage("كل العملاء"),
    "AllAreas": MessageLookupByLibrary.simpleMessage("جميع المناطق"),
    "AllCarBrands": MessageLookupByLibrary.simpleMessage(
      "جميع ماركات السيارات",
    ),
    "AllCarYears": MessageLookupByLibrary.simpleMessage("جميع سنوات السيارات"),
    "AllCategories": MessageLookupByLibrary.simpleMessage("كل الفئات"),
    "AllDestinations": MessageLookupByLibrary.simpleMessage("جميع الوجهات"),
    "AllFeaturedVideos": MessageLookupByLibrary.simpleMessage(
      "جميع مقاطع الفيديو المميزة",
    ),
    "AllFeatures": MessageLookupByLibrary.simpleMessage("كل المميزات"),
    "AllHotels": MessageLookupByLibrary.simpleMessage("كل الفنادق"),
    "AllPromoCodes": MessageLookupByLibrary.simpleMessage(
      "جميع الرموز الترويجية",
    ),
    "AllPromocodes": MessageLookupByLibrary.simpleMessage(
      "كل الرموز الترويجية",
    ),
    "AllProperties": MessageLookupByLibrary.simpleMessage("جميع العقارات"),
    "AllRentalCars": MessageLookupByLibrary.simpleMessage(
      " كل السيارات المؤجورة",
    ),
    "AllRentalcars": MessageLookupByLibrary.simpleMessage(
      "اضافة سيارات للتأجير",
    ),
    "AllRequests": MessageLookupByLibrary.simpleMessage("كل الطلبات"),
    "AllUsers": MessageLookupByLibrary.simpleMessage("جميع المستخدمين"),
    "Allactivities": MessageLookupByLibrary.simpleMessage("جميع النشاطات"),
    "Allchalets": MessageLookupByLibrary.simpleMessage("جميع الشاليهات"),
    "Allholidayhomes": MessageLookupByLibrary.simpleMessage(
      "جميع بيوت الاجازة",
    ),
    "Allresturants": MessageLookupByLibrary.simpleMessage("كل المطاعم"),
    "Allshops": MessageLookupByLibrary.simpleMessage("جميع الكوفي شوب"),
    "Alltypes": MessageLookupByLibrary.simpleMessage("كل الأنواع"),
    "Almalekfahedstreet": MessageLookupByLibrary.simpleMessage(
      "شارع الملك فهد",
    ),
    "ApplyFilter": MessageLookupByLibrary.simpleMessage("تطبيق عامل التصفية"),
    "Arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "Areaname": MessageLookupByLibrary.simpleMessage("اسم المنطقة"),
    "Areas": MessageLookupByLibrary.simpleMessage("المناطق"),
    "AreyousureyouwanttodeletethisHolidayhome":
        MessageLookupByLibrary.simpleMessage(
          "هل انت متاكد انك تريد حذف بيت الاجازة هذا",
        ),
    "AreyousureyouwanttodeletethisShopifyesyouwontbeabletoseeagain":
        MessageLookupByLibrary.simpleMessage(
          "هل انت متاكد انك تريد حذف هذا المتجر ، اذا وافقت لن تستطيع رؤيته مجددا",
        ),
    "Areyousureyouwanttodeletethisactivitcaroseeagain":
        MessageLookupByLibrary.simpleMessage(
          "هل انت متاكد انك تريد حذف هذا التأجير، اذا وافقت لن تستطيع رؤيته مجددا",
        ),
    "Areyousureyouwanttodeletethisactivityifyesyouwontbeabletoseeagain":
        MessageLookupByLibrary.simpleMessage(
          "هل انت متاكد انك تريد حذف هذا النشاط، اذا وافقت لن تستطيع رؤيته مجددا",
        ),
    "Areyousureyouwanttodeletethisbrand": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف هذه العلامة التجارية",
    ),
    "Areyousureyouwanttodeletethischaletifyesyouwontbeabletoseeagain":
        MessageLookupByLibrary.simpleMessage(
          "هل انت متاكد انك تريد حذف هذا الشاليه ، اذا وافقت لن تستطيع رؤيته مجددا",
        ),
    "Areyousureyouwanttodeletethisfeature":
        MessageLookupByLibrary.simpleMessage("هل حقا تريد حذف هذه الميزة"),
    "Areyousureyouwanttodeletethishotelifyesyouwontbeabletoseeagain":
        MessageLookupByLibrary.simpleMessage(
          "هل انت متاكد انك تريد حذف هذا الفندق، اذا وافقت لن تستطيع رؤيته مجددا",
        ),
    "Areyousureyouwanttodeletethisreelvideo":
        MessageLookupByLibrary.simpleMessage(
          "هل انت متاكد انك تريد حذف الفيديو القصير",
        ),
    "Areyousureyouwanttodeletethisrentalcar": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف سيارة الاجرة هذه، اذا وافقت لن تستطيع مشاهدتها مجددا",
    ),
    "Areyousureyouwanttodeletethisresturant":
        MessageLookupByLibrary.simpleMessage(
          "هل انت متاكد انك تريد حذف هذا المطعم، اذا وافقت لن تستطيع رؤيته مجددا",
        ),
    "Areyousureyouwanttodeletethistype": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف هذا النوع",
    ),
    "Areyousureyouwanttodeletethisyear": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف هذه السنة ، اذا وافقت لن تستطيع رؤيتها مجددا",
    ),
    "Areyyousureyouwanttodeletethisagent": MessageLookupByLibrary.simpleMessage(
      "هل حقا تريد حذف هذا العميل",
    ),
    "AskingPrice": MessageLookupByLibrary.simpleMessage("السعر المطلوب"),
    "Assignasfeaturedvideoincatpage": MessageLookupByLibrary.simpleMessage(
      "تعيين كفيديو مميز في صفحة الفئات",
    ),
    "Assignasfeaturedvideoinhomepage": MessageLookupByLibrary.simpleMessage(
      "تعيين كفيديو مميز في الصفحة الرئيسية",
    ),
    "Averagep": MessageLookupByLibrary.simpleMessage("السعر المتوسط"),
    "Averagepriceper": MessageLookupByLibrary.simpleMessage(
      "متوسط السعر لكل شخص",
    ),
    "BMW": MessageLookupByLibrary.simpleMessage("بي ام دبليو"),
    "Balcony": MessageLookupByLibrary.simpleMessage("شرفة"),
    "BasicInformation": MessageLookupByLibrary.simpleMessage("معلومات اساسية"),
    "Basicinformation": MessageLookupByLibrary.simpleMessage(
      "المعلومات الأساسية",
    ),
    "Bigrooms": MessageLookupByLibrary.simpleMessage("غرف كبيرة"),
    "Brand": MessageLookupByLibrary.simpleMessage("الماركة"),
    "BrandName": MessageLookupByLibrary.simpleMessage("اسم العلامة التجارية"),
    "BurjKhalifaview": MessageLookupByLibrary.simpleMessage(
      "منظر على برج خليفة",
    ),
    "CallAgent": MessageLookupByLibrary.simpleMessage("الاتصال بالعميل"),
    "Callclient": MessageLookupByLibrary.simpleMessage("الاتصال بالوكيل"),
    "Cancel": MessageLookupByLibrary.simpleMessage("الغاء"),
    "Canceled": MessageLookupByLibrary.simpleMessage("تم الالغاء"),
    "CarBrands": MessageLookupByLibrary.simpleMessage(
      "علامات السيارات التجارية",
    ),
    "CarName": MessageLookupByLibrary.simpleMessage("اسم السيارة"),
    "CarProductionYear": MessageLookupByLibrary.simpleMessage(
      "سنة إنتاج السيارة",
    ),
    "CarRental": MessageLookupByLibrary.simpleMessage("تاجير سيارة"),
    "CarYears": MessageLookupByLibrary.simpleMessage("سنوات السيارات"),
    "Carbrads": MessageLookupByLibrary.simpleMessage(
      "علامات السيارات التجارية",
    ),
    "Carname": MessageLookupByLibrary.simpleMessage("اسم السيارة"),
    "Carrental": MessageLookupByLibrary.simpleMessage("تاجير السيارات"),
    "CarrentalAgets": MessageLookupByLibrary.simpleMessage(
      "عملا تاجير السيارات",
    ),
    "Categories": MessageLookupByLibrary.simpleMessage("الفئات"),
    "Category": MessageLookupByLibrary.simpleMessage("فئة"),
    "Chalet": MessageLookupByLibrary.simpleMessage("شاليه"),
    "Chalets": MessageLookupByLibrary.simpleMessage("الشاليهات"),
    "ChangePassword": MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
    "Chevrolet": MessageLookupByLibrary.simpleMessage("شيفروليه"),
    "ChooseCategories": MessageLookupByLibrary.simpleMessage("اختيار الأقسام"),
    "ChooseCategory": MessageLookupByLibrary.simpleMessage("اختر الفئة"),
    "Code": MessageLookupByLibrary.simpleMessage("الرمز"),
    "Configuration": MessageLookupByLibrary.simpleMessage("ترتيب"),
    "ConfirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "تاكيد كلمة المرور الجديدة",
    ),
    "Contactfinishdate": MessageLookupByLibrary.simpleMessage(
      "تاريخ انتهاء جهة الاتصال",
    ),
    "Continue": MessageLookupByLibrary.simpleMessage("المتابعة"),
    "ContractFinishDate": MessageLookupByLibrary.simpleMessage(
      "تاريخ انتهاء العقد",
    ),
    "Country": MessageLookupByLibrary.simpleMessage("الدولة"),
    "DayPrice": MessageLookupByLibrary.simpleMessage("سعر اليوم"),
    "DeleteActivity": MessageLookupByLibrary.simpleMessage("حذف النشاط"),
    "DeleteAgent": MessageLookupByLibrary.simpleMessage("حذف عميل"),
    "DeleteArea": MessageLookupByLibrary.simpleMessage("حذف المنطقة"),
    "DeleteBrand": MessageLookupByLibrary.simpleMessage("حذف العلامة التجارية"),
    "DeleteChalet": MessageLookupByLibrary.simpleMessage("حذف الشاليه"),
    "DeleteComment": MessageLookupByLibrary.simpleMessage("حذف التعليق"),
    "DeleteHolidayhome": MessageLookupByLibrary.simpleMessage("حذف بيت اجازة"),
    "DeletePromoCode": MessageLookupByLibrary.simpleMessage(
      "حذف الرمز الترويجي",
    ),
    "DeleteProperty": MessageLookupByLibrary.simpleMessage("حذف العقار"),
    "DeleteReelVideo": MessageLookupByLibrary.simpleMessage(
      "حذف الفيديو القصير",
    ),
    "DeleteRentalcar": MessageLookupByLibrary.simpleMessage(
      "حذف سيارة التأجير",
    ),
    "DeleteResturant": MessageLookupByLibrary.simpleMessage("حذف مطعم"),
    "DeleteShop": MessageLookupByLibrary.simpleMessage("حذف المتجر"),
    "DeleteYear": MessageLookupByLibrary.simpleMessage("حذف السنة"),
    "Deletefeature": MessageLookupByLibrary.simpleMessage("حذف الميزة"),
    "Deletehotel": MessageLookupByLibrary.simpleMessage("حذف الفندق"),
    "Deletetype": MessageLookupByLibrary.simpleMessage("حذف نوع"),
    "Denied": MessageLookupByLibrary.simpleMessage("مرفوض"),
    "Description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "Destination": MessageLookupByLibrary.simpleMessage("الوجهة"),
    "Destinations": MessageLookupByLibrary.simpleMessage("الوجهات"),
    "Discount": MessageLookupByLibrary.simpleMessage("الحسم"),
    "DiscountPercentage": MessageLookupByLibrary.simpleMessage(
      "نسبة الحسم المئوية",
    ),
    "Discussion": MessageLookupByLibrary.simpleMessage("المناقشة"),
    "DriverPricePerDay": MessageLookupByLibrary.simpleMessage(
      "سعر السائق في اليوم",
    ),
    "DriverPriceperday": MessageLookupByLibrary.simpleMessage(
      "تكلفة السائق لكل يوم",
    ),
    "Dubaicitycenter": MessageLookupByLibrary.simpleMessage("مركز مدينة دبي"),
    "Edit": MessageLookupByLibrary.simpleMessage("تعديل"),
    "EditReelvideos": MessageLookupByLibrary.simpleMessage(
      "تعديل الفيديوهات القصيرة",
    ),
    "EditYear": MessageLookupByLibrary.simpleMessage("تعديل السنة"),
    "Editcomment": MessageLookupByLibrary.simpleMessage("تعديل التعليق"),
    "Editimages": MessageLookupByLibrary.simpleMessage("تعديل الصور"),
    "Email": MessageLookupByLibrary.simpleMessage("البريد الألكتروني "),
    "EmailAddress": MessageLookupByLibrary.simpleMessage("البريد الالكتروني"),
    "EmailAdress": MessageLookupByLibrary.simpleMessage("البريد الالكتروني"),
    "English": MessageLookupByLibrary.simpleMessage("الانجليزية"),
    "EnterNewPassword": MessageLookupByLibrary.simpleMessage(
      "ادخل كلمة المرور ",
    ),
    "EnteraNewPassword": MessageLookupByLibrary.simpleMessage(
      "ادخل كلمة مرور جديدة تكون سهلة التذكر",
    ),
    "EnterdigitsOTP": MessageLookupByLibrary.simpleMessage(
      "ادخل الرمز الرباعي",
    ),
    "Exit": MessageLookupByLibrary.simpleMessage("الخروج"),
    "ExtraInformation": MessageLookupByLibrary.simpleMessage("معلومات اضافية"),
    "Farm": MessageLookupByLibrary.simpleMessage("مزرعة"),
    "FeaturedVideos": MessageLookupByLibrary.simpleMessage("مقاطع فيديو مميزة"),
    "Features": MessageLookupByLibrary.simpleMessage("الميزات"),
    "Fee": MessageLookupByLibrary.simpleMessage("مصاريف"),
    "FeturedVideos": MessageLookupByLibrary.simpleMessage("الفيديوهات الأحدث"),
    "Filter": MessageLookupByLibrary.simpleMessage("منقي"),
    "Filteredas": MessageLookupByLibrary.simpleMessage("التصفية على انها"),
    "ForgotPassword": MessageLookupByLibrary.simpleMessage("نسيت كلمة السر"),
    "From": MessageLookupByLibrary.simpleMessage("من"),
    "Fullname": MessageLookupByLibrary.simpleMessage("الاسم الكامل بالانجلزي"),
    "FullnameAr": MessageLookupByLibrary.simpleMessage("الاسم الكامل بالعربي"),
    "GoogleReviewName": MessageLookupByLibrary.simpleMessage("اسم مراجعة جوجل"),
    "HideComment": MessageLookupByLibrary.simpleMessage("اخفاء التعليق"),
    "HolidayHome": MessageLookupByLibrary.simpleMessage("المصايف"),
    "HolidayHome1": MessageLookupByLibrary.simpleMessage("منزل عطلة 1"),
    "HolidayHomes": MessageLookupByLibrary.simpleMessage("بيوت الاجازة"),
    "HolidayHomesAgents": MessageLookupByLibrary.simpleMessage(
      "عملاء بيوت العطلة",
    ),
    "HomeSize": MessageLookupByLibrary.simpleMessage(" حجم المنزل "),
    "HotelName": MessageLookupByLibrary.simpleMessage("اسم فندق"),
    "Hotelname": MessageLookupByLibrary.simpleMessage("اسم الفندق"),
    "Hotels": MessageLookupByLibrary.simpleMessage("الفنادق"),
    "Jointhediscussion": MessageLookupByLibrary.simpleMessage(
      "الانضمام للمناقشة",
    ),
    "Kia": MessageLookupByLibrary.simpleMessage("كيا"),
    "Language": MessageLookupByLibrary.simpleMessage("اللغة"),
    "LargeSelectionofLuxuryHotelsforyouandyourlovedoneBookonlinetoday":
        MessageLookupByLibrary.simpleMessage(
          "شقة كبيرة في فندق فخم لك ولمن تحب،احجز اليوم الكترونيا",
        ),
    "Location": MessageLookupByLibrary.simpleMessage(" موقع "),
    "Login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "Logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "MainAgentInformation": MessageLookupByLibrary.simpleMessage(
      "معلومات العميل الرئيسية",
    ),
    "ManageFeatures": MessageLookupByLibrary.simpleMessage(
      "إدارة الميزات والأنواع والعلامات التجارية والأشياء الأخرى",
    ),
    "Manageallfeaturesfromoneplace": MessageLookupByLibrary.simpleMessage(
      "ادارة جميع الميزات من مكان واحد",
    ),
    "MediaUpload": MessageLookupByLibrary.simpleMessage("رفع وسائط"),
    "Message": MessageLookupByLibrary.simpleMessage("رسائل"),
    "NightPrice": MessageLookupByLibrary.simpleMessage("سعر الليلة"),
    "NormalDays": MessageLookupByLibrary.simpleMessage("الأيام العادية"),
    "NormalPrices": MessageLookupByLibrary.simpleMessage("الأسعار العادية"),
    "Note": MessageLookupByLibrary.simpleMessage("ملاحظة"),
    "Notifications": MessageLookupByLibrary.simpleMessage("الاشعارات"),
    "NumberOfDays": MessageLookupByLibrary.simpleMessage("عدد الأيام"),
    "NumberofPeople": MessageLookupByLibrary.simpleMessage("عدد الاشخاص"),
    "Numberofbedrooms": MessageLookupByLibrary.simpleMessage("ارقام الغرف "),
    "Numberofdays": MessageLookupByLibrary.simpleMessage("عدد الايام"),
    "Numberofpeople": MessageLookupByLibrary.simpleMessage("عدد الأشخاص"),
    "Othersettigs": MessageLookupByLibrary.simpleMessage("اعدادات اخرى"),
    "Othersettings": MessageLookupByLibrary.simpleMessage("اعدادات اخرى"),
    "Password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "Pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
    "PhoneNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
    "PickupDropOff": MessageLookupByLibrary.simpleMessage("الركوب/النزول"),
    "Pleaseiwantaverycleanandbigcar": MessageLookupByLibrary.simpleMessage(
      "رجاءا اريد سيارة كبيرة ونظيفة جدا",
    ),
    "Price": MessageLookupByLibrary.simpleMessage("السعر"),
    "PriceBreakdown": MessageLookupByLibrary.simpleMessage("تحطيم الاسعار"),
    "PriceDetails": MessageLookupByLibrary.simpleMessage("توضيح التسعيرة"),
    "PriceRangePerDay": MessageLookupByLibrary.simpleMessage(
      "نطاق السعر لكل يوم",
    ),
    "Pricepernight": MessageLookupByLibrary.simpleMessage("السعر لكل ليلة"),
    "Pricerangeperday": MessageLookupByLibrary.simpleMessage("السعر لكل يوم"),
    "Pricerangepernight": MessageLookupByLibrary.simpleMessage(
      "مجال السعر لكل ليلة",
    ),
    "Pricestartsfrom": MessageLookupByLibrary.simpleMessage("السعر يبدأ من"),
    "PrivateDriver": MessageLookupByLibrary.simpleMessage("سائق خاص"),
    "PromoSubtotal": MessageLookupByLibrary.simpleMessage(
      "المجموع الفرعي للعرض الترويجي",
    ),
    "Properety": MessageLookupByLibrary.simpleMessage("ملكية"),
    "Properties": MessageLookupByLibrary.simpleMessage("العقارات"),
    "Property": MessageLookupByLibrary.simpleMessage(" ملكية"),
    "Propertyname": MessageLookupByLibrary.simpleMessage("اسم العقار"),
    "RangeRover": MessageLookupByLibrary.simpleMessage("رانج روفر"),
    "ReUploadreelvideo": MessageLookupByLibrary.simpleMessage(
      "اعادة رفع الفيديوهات",
    ),
    "RemoveApplicationfeature": MessageLookupByLibrary.simpleMessage(
      "حذف ميزة من التطبيق",
    ),
    "Removefromcategoryfeature": MessageLookupByLibrary.simpleMessage(
      "الإزالة من فئة الميزة",
    ),
    "RequestDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الطلب"),
    "Requesteddate": MessageLookupByLibrary.simpleMessage("تاريخ الطلب"),
    "Requestedon": MessageLookupByLibrary.simpleMessage("تاريخ الطلب"),
    "Requestforcarrentapproved": MessageLookupByLibrary.simpleMessage(
      "تمت الموافقة على طلب تاجير السيارة",
    ),
    "Requests": MessageLookupByLibrary.simpleMessage("الطلبات"),
    "Reset": MessageLookupByLibrary.simpleMessage("إعادة ضبط"),
    "Resetpassword": MessageLookupByLibrary.simpleMessage(
      "اعادة تعيين كلمة المرور",
    ),
    "Restaurants": MessageLookupByLibrary.simpleMessage("مطاعم"),
    "Resturantname": MessageLookupByLibrary.simpleMessage("اسم المطعم"),
    "Resturants": MessageLookupByLibrary.simpleMessage("المطاعم"),
    "Reviews": MessageLookupByLibrary.simpleMessage("تعليقات"),
    "RoofTop": MessageLookupByLibrary.simpleMessage("السطح"),
    "Save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "SaveChanges": MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
    "ScheduleDaysPrices": MessageLookupByLibrary.simpleMessage("أسعار الجدولة"),
    "Search": MessageLookupByLibrary.simpleMessage("بحث"),
    "SearchAreas": MessageLookupByLibrary.simpleMessage(" بحث عن منطقة"),
    "SearchCars": MessageLookupByLibrary.simpleMessage(" البحث عن السيارات"),
    "Searchcars": MessageLookupByLibrary.simpleMessage("البحث في السيارات"),
    "Searchplacesandlocations": MessageLookupByLibrary.simpleMessage(
      " البحث عن الأماكن والمواقع",
    ),
    "Seaview": MessageLookupByLibrary.simpleMessage("منظر بحري"),
    "SenddigitsOTP": MessageLookupByLibrary.simpleMessage("ارسل الرمز الرباعي"),
    "SetLocationonmap": MessageLookupByLibrary.simpleMessage(
      "تعيين الموقع على الخريطة؟",
    ),
    "Setallfeatures": MessageLookupByLibrary.simpleMessage(" ضبط كل الميزات "),
    "Setlocationonmap": MessageLookupByLibrary.simpleMessage(
      "تحديد الموقع على الخريطة",
    ),
    "Shopname": MessageLookupByLibrary.simpleMessage("اسم المتجر"),
    "Shops": MessageLookupByLibrary.simpleMessage("كوفي شوب"),
    "ShowSchedulePrices": MessageLookupByLibrary.simpleMessage(
      "عرض أسعار الجدولة",
    ),
    "Status": MessageLookupByLibrary.simpleMessage("حالة الطلب"),
    "Submit": MessageLookupByLibrary.simpleMessage("ارسال"),
    "Subtotal": MessageLookupByLibrary.simpleMessage("المجموع الفرعي"),
    "Tabheretoreelvideo": MessageLookupByLibrary.simpleMessage(
      "اضغط هنا لبكرة الفيديو",
    ),
    "Tabheretouploadimage": MessageLookupByLibrary.simpleMessage(
      "انقر هنا لرفع الصورة",
    ),
    "Tabheretouploadmainvideo": MessageLookupByLibrary.simpleMessage(
      "اضغط هنا لتحميل الفيديو الرئيسي",
    ),
    "TabheretouploadmainvideoAr": MessageLookupByLibrary.simpleMessage(
      "اضغط هنا لتحميل الفيديو الرئيسي بالعربي",
    ),
    "Tabheretouploadreel": MessageLookupByLibrary.simpleMessage(
      "اضغط هنا لتحميل ",
    ),
    "Tabheretouploadvideo": MessageLookupByLibrary.simpleMessage(
      "انقر هنا لرفع الصور",
    ),
    "Tesla": MessageLookupByLibrary.simpleMessage("تيسلا"),
    "TeslaModel3": MessageLookupByLibrary.simpleMessage("تيسلا الموديل 3"),
    "ThereAreNoUsers": MessageLookupByLibrary.simpleMessage("لا يوجد مستخدمين"),
    "Therearenoitems": MessageLookupByLibrary.simpleMessage("لا يوجد عناصر"),
    "Thisplaceiseverythingyouneedforthebeststay":
        MessageLookupByLibrary.simpleMessage(
          "هذا المكان هو كل ما تحتاجه للحصول على أفضل إقامة ، وموقع مثالي ، وإطلالة وجو.",
        ),
    "Title": MessageLookupByLibrary.simpleMessage("العنوان"),
    "TitleAr": MessageLookupByLibrary.simpleMessage("العنوان بالعربي"),
    "TitleEn": MessageLookupByLibrary.simpleMessage("العنوان بالانكليزي"),
    "To": MessageLookupByLibrary.simpleMessage("الى"),
    "TotalPrice": MessageLookupByLibrary.simpleMessage("اجمالي السعر"),
    "Tourismfee": MessageLookupByLibrary.simpleMessage(
      "ضريبة السياحة لخمس ليال",
    ),
    "Toyota": MessageLookupByLibrary.simpleMessage("تويوتا"),
    "TranslateComment": MessageLookupByLibrary.simpleMessage("ترجمة التعليق"),
    "TurnedOn": MessageLookupByLibrary.simpleMessage("تم التشغيل"),
    "Type": MessageLookupByLibrary.simpleMessage("النوع"),
    "Types": MessageLookupByLibrary.simpleMessage("انواع"),
    "UploadImage": MessageLookupByLibrary.simpleMessage("تحميل الصور"),
    "UploadMainVideo": MessageLookupByLibrary.simpleMessage(
      "تحميل الفيديو الرئيسي",
    ),
    "UploadMainVideoAr": MessageLookupByLibrary.simpleMessage(
      "تحميل الفيديو الرئيسي بالعربي",
    ),
    "UploadPhotos": MessageLookupByLibrary.simpleMessage("رفع الصور"),
    "UploadReel": MessageLookupByLibrary.simpleMessage("تحميل "),
    "Uploadimages": MessageLookupByLibrary.simpleMessage("رفع صور"),
    "Uploadreelvideos": MessageLookupByLibrary.simpleMessage(
      "رفع فيديوهات قصيرة",
    ),
    "Uploadvideos": MessageLookupByLibrary.simpleMessage("رفع فيديوهات"),
    "UserInformation": MessageLookupByLibrary.simpleMessage("معلومات المستخدم"),
    "Users": MessageLookupByLibrary.simpleMessage("المستخدمين"),
    "Vat": MessageLookupByLibrary.simpleMessage("الضريبة المضافة"),
    "ViewDiscussion": MessageLookupByLibrary.simpleMessage("رؤية المناقشة"),
    "WelcometoDashboard": MessageLookupByLibrary.simpleMessage(
      "أهلا بك في لوحة التحكم",
    ),
    "Writedownyourdescription": MessageLookupByLibrary.simpleMessage(
      "اكتب الوصف الخاص بك",
    ),
    "Year": MessageLookupByLibrary.simpleMessage("السنة"),
    "Yes": MessageLookupByLibrary.simpleMessage("نعم"),
    "YesDeletehotel": MessageLookupByLibrary.simpleMessage("نعم حذف الفندق"),
    "YouTube": MessageLookupByLibrary.simpleMessage("YouTube"),
    "YourrequestforHyundaiSonataNo": MessageLookupByLibrary.simpleMessage(
      "تمت الموافقة على طلبك للحصول على Hyundai Sonata رقم 380282",
    ),
    "activityara": MessageLookupByLibrary.simpleMessage("اسم النشاط بالعربي"),
    "activityeng": MessageLookupByLibrary.simpleMessage(
      "اسم النشاط بالانكليزي",
    ),
    "addFloorPlan": MessageLookupByLibrary.simpleMessage("إضافة مخطط طابق"),
    "addMore": MessageLookupByLibrary.simpleMessage("إضافة المزيد"),
    "addNewProject": MessageLookupByLibrary.simpleMessage("إضافة مشروع جديد"),
    "addPricePlan": MessageLookupByLibrary.simpleMessage("إضافة خطة أسعار"),
    "addProject": MessageLookupByLibrary.simpleMessage("إضافة مشروع"),
    "addProjectPlan": MessageLookupByLibrary.simpleMessage("إضافة خطة مشروع"),
    "addcar": MessageLookupByLibrary.simpleMessage("اضافة تأجير سيارة جديدة"),
    "added": MessageLookupByLibrary.simpleMessage("تم الاصافة بنجاح"),
    "addedon": MessageLookupByLibrary.simpleMessage("أضيف في"),
    "addres": MessageLookupByLibrary.simpleMessage("إضافة مطعم جديد"),
    "allcar": MessageLookupByLibrary.simpleMessage("كل التاجيرات"),
    "allres": MessageLookupByLibrary.simpleMessage("جميع المطاعم"),
    "androidUsers": MessageLookupByLibrary.simpleMessage("مستخدمين الاندرويد"),
    "arabicDescriptionSaved": MessageLookupByLibrary.simpleMessage(
      "تم حفظ الوصف العربي بنجاح",
    ),
    "arabicName": MessageLookupByLibrary.simpleMessage("الاسم بالعربي"),
    "arabicnum": MessageLookupByLibrary.simpleMessage("اسم السيارة بالعربي"),
    "arahome": MessageLookupByLibrary.simpleMessage("اسم المنزل بالعربي"),
    "arapro": MessageLookupByLibrary.simpleMessage("اسم المنشأة بالعربي"),
    "areaar": MessageLookupByLibrary.simpleMessage("اسم المنطقة بالعربي"),
    "areaen": MessageLookupByLibrary.simpleMessage("اسم المنطقة بالانكليزي"),
    "areyousureyouwanttodelete": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف هذا، اذا وافقت لن تستطيع رؤيته مجددا",
    ),
    "areyousureyouwanttodeletethisimage": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف هذه الصورة",
    ),
    "bedroomsArabic": MessageLookupByLibrary.simpleMessage("غرف النوم (عربي)"),
    "bedroomsEnglish": MessageLookupByLibrary.simpleMessage(
      "غرف النوم (إنجليزي)",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "cannotDeleteBecauseItIsUsedInVideo": MessageLookupByLibrary.simpleMessage(
      "لا يمكن حذفه لانه مستخدم في عقار",
    ),
    "cannotDeleteBecauseUsed": MessageLookupByLibrary.simpleMessage(
      "لا يمكن الحذف لأنها قيد الاستخدام",
    ),
    "carr": MessageLookupByLibrary.simpleMessage("تأجير سيارات"),
    "cash": MessageLookupByLibrary.simpleMessage("نقدي"),
    "chaletara": MessageLookupByLibrary.simpleMessage("اسم الشاليه بالعربي"),
    "chaleteng": MessageLookupByLibrary.simpleMessage("اسم الشاليه بالانكليزي"),
    "checkemail": MessageLookupByLibrary.simpleMessage(
      "تم إرسال رمز OTP المكون من 4 أرقام إلى بريدك الإلكتروني ، يرجى التحقق منه وإدخاله للمتابعة",
    ),
    "choosecategory": MessageLookupByLibrary.simpleMessage("اختر الفئة"),
    "configurationhasbeenupdatedsuccessfully":
        MessageLookupByLibrary.simpleMessage("تم تحديث الاعدادات بنجاح"),
    "contact": MessageLookupByLibrary.simpleMessage("تواصل معنا"),
    "date": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "days": MessageLookupByLibrary.simpleMessage("أيام"),
    "deaen": MessageLookupByLibrary.simpleMessage("الوصف بالانكليزي"),
    "deleare": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف هذه المنطقة ، اذا وافقت لن تستطيع رؤيته مجددا",
    ),
    "delecar": MessageLookupByLibrary.simpleMessage("حذف تأجير السيارات"),
    "delemsgv": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف الفيديو، اذا وافقت لن تستطيع مشاهدتها مجددا",
    ),
    "deleres": MessageLookupByLibrary.simpleMessage("حذف المطعم"),
    "deletePricePlan": MessageLookupByLibrary.simpleMessage("حذف خطة الأسعار"),
    "deleteag": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذا الوكيل",
    ),
    "deletehome": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف هذا المنزل، اذا وافقت لن تستطيع رؤيته مجددا",
    ),
    "deleteimage": MessageLookupByLibrary.simpleMessage("حذف الصورة"),
    "deletepro": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك ترد حذف الرمز الترويجي",
    ),
    "deletepropertystatus": MessageLookupByLibrary.simpleMessage(
      "حذف حالة العقار",
    ),
    "deletepropertystatusDesc": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف حالة العقار، اذا وافقت لن تستطيع رؤيته مجددا",
    ),
    "deletev": MessageLookupByLibrary.simpleMessage("حذف الفيديو"),
    "deletmsgres": MessageLookupByLibrary.simpleMessage(
      "هل انت متاكد انك تريد حذف هذا المطعم ، اذا وافقت لن تستطيع رؤيته مجددا",
    ),
    "desara": MessageLookupByLibrary.simpleMessage("الوصف بالعربي"),
    "descriptionAr": MessageLookupByLibrary.simpleMessage("الوصف بالعربي"),
    "descriptionArabic": MessageLookupByLibrary.simpleMessage(
      "الوصف (بالعربية)",
    ),
    "descriptionEn": MessageLookupByLibrary.simpleMessage("الوصف بالانكليزي"),
    "descriptionEnglish": MessageLookupByLibrary.simpleMessage(
      "الوصف (بالإنجليزية)",
    ),
    "descriptions": MessageLookupByLibrary.simpleMessage("الأوصاف"),
    "displayProjectInCategory": MessageLookupByLibrary.simpleMessage(
      "عرض هذا المشروع بشكل بارز في فئته",
    ),
    "displayProjectOnHomePage": MessageLookupByLibrary.simpleMessage(
      "عرض هذا المشروع في الصفحة الرئيسية",
    ),
    "editPrice": MessageLookupByLibrary.simpleMessage("تعديل السعر"),
    "editPricePlan": MessageLookupByLibrary.simpleMessage("تعديل خطة الأسعار"),
    "editedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم التعديل بنجاح",
    ),
    "endDate": MessageLookupByLibrary.simpleMessage("تاريخ النهاية"),
    "endat": MessageLookupByLibrary.simpleMessage("ينتهي في"),
    "endsize": MessageLookupByLibrary.simpleMessage("الحجم النهائي"),
    "englishDescriptionSaved": MessageLookupByLibrary.simpleMessage(
      "تم حفظ الوصف الإنجليزي بنجاح",
    ),
    "englishName": MessageLookupByLibrary.simpleMessage("الاسم بالإنجليزي"),
    "engnum": MessageLookupByLibrary.simpleMessage("اسم السيارة بالانكليزي"),
    "enhome": MessageLookupByLibrary.simpleMessage("اسم المنزل بالانكليزي"),
    "enpro": MessageLookupByLibrary.simpleMessage("اسم المنشأة بالانكليزي"),
    "enterArabicDescription": MessageLookupByLibrary.simpleMessage(
      "أدخل الوصف بالعربي",
    ),
    "enterArabicName": MessageLookupByLibrary.simpleMessage(
      "أدخل الاسم بالعربي",
    ),
    "enterBedroomsArabic": MessageLookupByLibrary.simpleMessage(
      "أدخل غرف النوم بالعربي",
    ),
    "enterBedroomsEnglish": MessageLookupByLibrary.simpleMessage(
      "أدخل غرف النوم بالإنجليزي",
    ),
    "enterDate": MessageLookupByLibrary.simpleMessage("أدخل التاريخ"),
    "enterDescriptionArabic": MessageLookupByLibrary.simpleMessage(
      "أدخل الوصف بالعربية",
    ),
    "enterDescriptionEnglish": MessageLookupByLibrary.simpleMessage(
      "أدخل الوصف بالإنجليزية",
    ),
    "enterEndingPrice": MessageLookupByLibrary.simpleMessage(
      "أدخل السعر النهائي",
    ),
    "enterEnglishDescription": MessageLookupByLibrary.simpleMessage(
      "أدخل الوصف بالإنجليزي",
    ),
    "enterEnglishName": MessageLookupByLibrary.simpleMessage(
      "أدخل الاسم بالإنجليزي",
    ),
    "enterFloorPlanNameArabic": MessageLookupByLibrary.simpleMessage(
      "أدخل اسم مخطط الطابق بالعربي",
    ),
    "enterFloorPlanNameEnglish": MessageLookupByLibrary.simpleMessage(
      "أدخل اسم مخطط الطابق بالإنجليزي",
    ),
    "enterInstallmentInfo": MessageLookupByLibrary.simpleMessage(
      "أدخل معلومات القسط",
    ),
    "enterOrder": MessageLookupByLibrary.simpleMessage("أدخل الترتيب"),
    "enterPaymentPercentage": MessageLookupByLibrary.simpleMessage(
      "أدخل نسبة الدفع",
    ),
    "enterSpaceSizeArabic": MessageLookupByLibrary.simpleMessage(
      "أدخل حجم المساحة بالعربي",
    ),
    "enterSpaceSizeEnglish": MessageLookupByLibrary.simpleMessage(
      "أدخل حجم المساحة بالإنجليزي",
    ),
    "enterStartingPrice": MessageLookupByLibrary.simpleMessage(
      "أدخل السعر الابتدائي",
    ),
    "enteraran": MessageLookupByLibrary.simpleMessage(
      "الرجاء ادخال الاسم العربي",
    ),
    "enterc": MessageLookupByLibrary.simpleMessage("الرجاء ادخال الفئة"),
    "entere": MessageLookupByLibrary.simpleMessage("الرجاء ادخال الايميل"),
    "enteremail": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال بريدك الإلكتروني المسجل لإرسال كلمة المرور لمرة واحدة (OTP) المكونة من 4 أرقام لإعادة تعيين كلمة المرور الخاصة بك",
    ),
    "enterenn": MessageLookupByLibrary.simpleMessage(
      "الرجاء ادخال الاسم الانكليزي",
    ),
    "enterp": MessageLookupByLibrary.simpleMessage("الرجاء ادخال كلمة المرور"),
    "enterpc": MessageLookupByLibrary.simpleMessage(
      "الرجاء ادخال تأكيد  كلمة المرور",
    ),
    "face": MessageLookupByLibrary.simpleMessage("رابط الفيسبوك"),
    "failedToAddProject": MessageLookupByLibrary.simpleMessage(
      "فشل في إضافة المشروع",
    ),
    "feaar": MessageLookupByLibrary.simpleMessage("اسم الميزة بالعربي"),
    "feaen": MessageLookupByLibrary.simpleMessage("اسم الميزة بالانكليزي"),
    "featureName": MessageLookupByLibrary.simpleMessage("اسم الميزة"),
    "featuredInCategory": MessageLookupByLibrary.simpleMessage("مميز في الفئة"),
    "featuredOnHome": MessageLookupByLibrary.simpleMessage(
      "مميز في الصفحة الرئيسية",
    ),
    "featuredSettings": MessageLookupByLibrary.simpleMessage("إعدادات المميز"),
    "fill": MessageLookupByLibrary.simpleMessage(
      "الرجاء تعبئة الحقول المطلوبة",
    ),
    "fillRMSFields": MessageLookupByLibrary.simpleMessage(
      "الرجاء ملء حقول RMS",
    ),
    "filteras": MessageLookupByLibrary.simpleMessage("فرز حسب"),
    "floorPlan": MessageLookupByLibrary.simpleMessage("مخطط الطابق"),
    "floorPlanImage": MessageLookupByLibrary.simpleMessage("صورة مخطط الطابق"),
    "floorPlanNameArabic": MessageLookupByLibrary.simpleMessage("الاسم (عربي)"),
    "floorPlanNameEnglish": MessageLookupByLibrary.simpleMessage(
      "الاسم (إنجليزي)",
    ),
    "floorPlans": MessageLookupByLibrary.simpleMessage("مخططات الطوابق"),
    "galleryImages": MessageLookupByLibrary.simpleMessage("صور المعرض"),
    "holidaysi": MessageLookupByLibrary.simpleMessage("مساحة العقار"),
    "hotelar": MessageLookupByLibrary.simpleMessage("اسم الفندق بالعربي"),
    "hotenen": MessageLookupByLibrary.simpleMessage("اسم الفندق بالانكليزي"),
    "imageSelected": MessageLookupByLibrary.simpleMessage("صورة محددة"),
    "imagesSelected": MessageLookupByLibrary.simpleMessage("صور محددة"),
    "insta": MessageLookupByLibrary.simpleMessage("رابط الإنستغرام"),
    "installment": MessageLookupByLibrary.simpleMessage("أقساط"),
    "iosUsers": MessageLookupByLibrary.simpleMessage("مستخدمين الايفون"),
    "isPublished": MessageLookupByLibrary.simpleMessage("منشور"),
    "locationSelected": MessageLookupByLibrary.simpleMessage(
      "تم اختيار الموقع",
    ),
    "manage": MessageLookupByLibrary.simpleMessage(
      "إدارة الميزات والأنواع والعلامات التجارية والأشياء الأخرى",
    ),
    "noResultFound": MessageLookupByLibrary.simpleMessage("لا يوجد نتائج"),
    "nofea": MessageLookupByLibrary.simpleMessage("لا يوجد أي ميزات"),
    "numroom": MessageLookupByLibrary.simpleMessage("عدد غرف النوم"),
    "order": MessageLookupByLibrary.simpleMessage("الترتيب"),
    "passcur": MessageLookupByLibrary.simpleMessage("كلمة السر الحالية"),
    "passwordchangedsuccessfullly": MessageLookupByLibrary.simpleMessage(
      "تم تغيير كلمة المرور بنجاح",
    ),
    "payment": MessageLookupByLibrary.simpleMessage("الدفع (%)"),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("طريقة الدفع"),
    "paymentPlan": MessageLookupByLibrary.simpleMessage("خطة الدفع"),
    "paymentPlans": MessageLookupByLibrary.simpleMessage("خطط الدفع"),
    "pleaseEnterArabicDescription": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال الوصف بالعربي",
    ),
    "pleaseEnterArabicName": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال الاسم بالعربي",
    ),
    "pleaseEnterEnglishDescription": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال الوصف بالإنجليزي",
    ),
    "pleaseEnterEnglishName": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال الاسم بالإنجليزي",
    ),
    "pleaseSelectLocation": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار الموقع",
    ),
    "pleaseSelectPricePlan": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار خطة الأسعار",
    ),
    "pleaseSelectPropertyStatus": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار حالة العقار",
    ),
    "pleaseSelectType": MessageLookupByLibrary.simpleMessage(
      "يرجى اختيار النوع",
    ),
    "pleaseusenewpasswordtologin": MessageLookupByLibrary.simpleMessage(
      "استخدم كلمة السر الجديدة لتسجيل الدخول",
    ),
    "priceFrom": MessageLookupByLibrary.simpleMessage("السعر من"),
    "pricePlan": MessageLookupByLibrary.simpleMessage("خطة الأسعار"),
    "priceTo": MessageLookupByLibrary.simpleMessage("السعر إلى"),
    "pricepernight": MessageLookupByLibrary.simpleMessage(
      "السعر لليلة الواحدة",
    ),
    "priprice": MessageLookupByLibrary.simpleMessage("سعر المحرك الخاص"),
    "projectAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إضافة المشروع بنجاح",
    ),
    "projectPlan": MessageLookupByLibrary.simpleMessage("خطة المشروع"),
    "projectPlans": MessageLookupByLibrary.simpleMessage("خطط المشروع"),
    "promo": MessageLookupByLibrary.simpleMessage("الرمز الترويجي"),
    "propertyStatus": MessageLookupByLibrary.simpleMessage("حالة العقار"),
    "resara": MessageLookupByLibrary.simpleMessage("اسم المطعم بالعربي"),
    "resen": MessageLookupByLibrary.simpleMessage("اسم المطعم بالانكليزي"),
    "reset": MessageLookupByLibrary.simpleMessage("اعادة تهيئة"),
    "rmsProperties": MessageLookupByLibrary.simpleMessage("عقارات RMS"),
    "rmsUnits": MessageLookupByLibrary.simpleMessage("وحدات RMS"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "searchRmsArea": MessageLookupByLibrary.simpleMessage("البحث عن منطقة"),
    "searchRmsDiscount": MessageLookupByLibrary.simpleMessage(
      "البحث عن خصومات RMS",
    ),
    "searchRmsProperty": MessageLookupByLibrary.simpleMessage(
      "البحث عن عقارات RMS",
    ),
    "searchRmsUnit": MessageLookupByLibrary.simpleMessage("البحث عن وحدات RMS"),
    "selectLocation": MessageLookupByLibrary.simpleMessage("اختيار الموقع"),
    "selectPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "اختيار طريقة الدفع",
    ),
    "selectRmsArea": MessageLookupByLibrary.simpleMessage("اختيار منطقة RMS"),
    "selectRmsDiscount": MessageLookupByLibrary.simpleMessage("اختيار خصم RMS"),
    "selectRmsProperty": MessageLookupByLibrary.simpleMessage(
      "الرجاء اختيار عقار RMS",
    ),
    "selectRmsUnit": MessageLookupByLibrary.simpleMessage(
      "الرجاء اختيار وحدة RMS",
    ),
    "selectimg": MessageLookupByLibrary.simpleMessage("الرجاء اختيار الصور"),
    "selectvid": MessageLookupByLibrary.simpleMessage("الرجاء اختيار الفيديو"),
    "setallfeatures": MessageLookupByLibrary.simpleMessage("تحديد كل الميزات"),
    "shopar": MessageLookupByLibrary.simpleMessage("اسم المتجر بالعربي"),
    "shopen": MessageLookupByLibrary.simpleMessage("اسم المتجر بالانكليزي"),
    "spaceSizeArabic": MessageLookupByLibrary.simpleMessage(
      "حجم المساحة (عربي)",
    ),
    "spaceSizeEnglish": MessageLookupByLibrary.simpleMessage(
      "حجم المساحة (إنجليزي)",
    ),
    "startDate": MessageLookupByLibrary.simpleMessage("تاريخ البداية"),
    "starting": MessageLookupByLibrary.simpleMessage("بداية"),
    "startsize": MessageLookupByLibrary.simpleMessage("الحجم البدائي"),
    "tapp": MessageLookupByLibrary.simpleMessage("اضغط هنا لتحميل الصورة"),
    "termar": MessageLookupByLibrary.simpleMessage("الاحكام والشروط بالعربي"),
    "termen": MessageLookupByLibrary.simpleMessage(
      "الاحكام والشروط بالانكليزي",
    ),
    "type": MessageLookupByLibrary.simpleMessage("النوع"),
    "typear": MessageLookupByLibrary.simpleMessage("اسم النوع بالعربي"),
    "typeen": MessageLookupByLibrary.simpleMessage("اسم النوع بالانكليزي"),
    "updatepro": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الملف الشخصي بنجاح",
    ),
    "usersusedPromoCode": MessageLookupByLibrary.simpleMessage(
      "20 مستخدمًا استخدموا الرمز الترويجي",
    ),
    "usersusedpromocode": MessageLookupByLibrary.simpleMessage(
      "مستخدم يستخدم الرمز الترويجي",
    ),
    "villa": MessageLookupByLibrary.simpleMessage("فيلا"),
    "website": MessageLookupByLibrary.simpleMessage("الموقع"),
    "websiteAr": MessageLookupByLibrary.simpleMessage("الموقع بالعربي"),
    "whatsapp": MessageLookupByLibrary.simpleMessage("الواتس اب"),
    "wrong": MessageLookupByLibrary.simpleMessage(
      "حدث خطأ الرجاء المحاولة لاحقا",
    ),
    "yDeleteAgent": MessageLookupByLibrary.simpleMessage("Yes,Delete Agent"),
    "yeDeleteproperty": MessageLookupByLibrary.simpleMessage("نعم حذف العقار"),
    "yesDeleteHolidayhome": MessageLookupByLibrary.simpleMessage(
      "نعم حذف بيت الاجازة",
    ),
    "yesDeleteShop": MessageLookupByLibrary.simpleMessage("نعم حذف المتجر"),
    "yesDeleteactivity": MessageLookupByLibrary.simpleMessage("نعم حذف النشاط"),
    "yesDeletebrand": MessageLookupByLibrary.simpleMessage(
      "نعم حذف العلامة التجارية",
    ),
    "yesDeletechalet": MessageLookupByLibrary.simpleMessage("نعم حذف الشاليه"),
    "yesDeletefeature": MessageLookupByLibrary.simpleMessage("نعم حذف الميزة"),
    "yesDeletereelvideo": MessageLookupByLibrary.simpleMessage(
      "نعم حذف الفيديو القصير",
    ),
    "yesDeleterentalcar": MessageLookupByLibrary.simpleMessage(
      "نعم حذف سيارة الاجرة",
    ),
    "yesDeleteresturant": MessageLookupByLibrary.simpleMessage(
      "نعم حذف المطعم",
    ),
    "yesDeletetype": MessageLookupByLibrary.simpleMessage("نعم حذف النوع"),
    "yesDeleteyear": MessageLookupByLibrary.simpleMessage("نعم حذف السنة"),
    "yesde": MessageLookupByLibrary.simpleMessage("نعم, أريد الحذف"),
    "yesdecar": MessageLookupByLibrary.simpleMessage("نعم, تأكيد"),
  };
}
