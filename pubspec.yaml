name: admin_dubai
description: A new Flutter project.


publish_to: 'none'

version: 1.0.24+24

environment:
  sdk: '>=3.2.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.5
  flutter_svg: ^2.0.5
  pin_code_fields: ^8.0.1
  flutter_staggered_grid_view: ^0.6.2
  flutter_rating_bar: ^4.0.1
  video_player: ^2.6.0
  dotted_border: ^2.0.0+3
  google_maps_flutter: ^2.3.1
  get: ^4.6.5
  dio: ^4.0.0
  rxdart: ^0.27.7
  shared_preferences: ^2.2.0
  progress_dialog_null_safe: ^1.0.7
  #  flutter_datetime_picker: ^1.5.1
  flutter_screenutil: ^5.9.3
  lottie: ^2.5.0
  file_picker: ^10.2.0
  country_code_picker: ^3.0.0
  country_picker: ^2.0.20
  simple_animations: ^5.0.2
  pull_to_refresh: ^2.0.0
  font_awesome_flutter: ^10.4.0
  image_picker: ^0.8.7
  queen_validators: ^1.0.1
  google_fonts: ^4.0.3

  #! Firebase
  #  cloud_firestore: ^4.4.5
  firebase_messaging: ^15.2.4
  firebase_core: ^3.12.1
  #  firebase_messaging: ^14.3.0
  #  firebase_core: ^2.8.0

  permission_handler: ^11.4.0
  intl: ^0.19.0
  restart_app: ^1.3.2
  url_launcher: ^6.1.10
  fluttertoast: ^8.2.12
  carousel_slider: ^4.2.1
  expandable: ^5.0.1
  dots_indicator: ^2.1.0
  equatable: ^2.0.3
  datetime_picker_formfield:
  dropdown_search:
  flutter_hooks:
  flutter_quill: ^11.4.1
  reorderables: ^0.6.0
  drag_and_drop_lists: ^0.4.2

  logger:

dependency_overrides:
  archive: ^3.6.1
  win32: ^5.5.4
  google_maps_flutter_android: ^2.15.0
  image_picker_android: ^0.8.12+22
  path_provider_android: ^2.2.16
  permission_handler_android: ^13.0.1
  shared_preferences_android: ^2.4.8
  url_launcher_android: ^6.3.15
  video_player_android: ^2.8.2
  intl: ^0.20.2
  file_picker: ^10.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.1
  flutter_launcher_icons: ^0.13.1

#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/admin_logo.jpeg"
  #  adaptive_icon_background: "assets/images/admin_logo.jpeg"
  adaptive_icon_foreground: "assets/admin_logo.jpeg"
  remove_alpha_ios: true




flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/
    - assets/lang/
    - shorebird.yaml


flutter_intl:
  enabled: true
